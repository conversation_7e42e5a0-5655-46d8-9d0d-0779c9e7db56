#!/bin/bash
# git-cleanup-repo
#
# Author: <PERSON> <<EMAIL>>
# Adapted from the original by <PERSON><PERSON>ling

git checkout master &> /dev/null

# Make sure we're working with the most up-to-date version of master.
git fetch

# Prune obsolete remote tracking branches. These are branches that we
# once tracked, but have since been deleted on the remote.
git remote prune origin

# List all the branches that have been merged fully into master, and
# then delete them. We use the remote master here, just in case our
# local master is out of date.
git branch --merged origin/master | grep -v 'master$' | xargs git branch -d

# Now the same, but including remote branches.
MERGED_ON_REMOTE=`git branch -r --merged origin/master | sed 's/ *origin\///' | grep -v 'master$'`

if [ "$MERGED_ON_REMOTE" ]; then
	echo "The following remote branches are fully merged and will be removed:"
	echo $MERGED_ON_REMOTE

	read -p "Continue (y/N)? "
	if [ "$REPLY" == "y" ]; then
		git branch -r --merged origin/master | sed 's/ *origin\///' \
			| grep -v 'master$' | xargs -I% git push origin :% 2>&1 \
			| grep --colour=never 'deleted'
		echo "Done!"
	fi
fi
