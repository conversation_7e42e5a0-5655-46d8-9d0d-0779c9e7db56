{"name": "boilerplate", "version": "0.1.0", "private": true, "dependencies": {"@date-io/moment": "1.3.13", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@material-ui/lab": "^4.0.0-alpha.61", "@material-ui/pickers": "3.2.9", "@sentry/react": "^7.3.1", "autosuggest-highlight": "3.1.1", "axios": "0.19.2", "bootstrap": "4.6.0", "chart.js": "^3.5.0", "errorhandler": "^1.5.1", "http-proxy-middleware": "^1.0.3", "lodash": "^4.17.15", "moment": "2.29.1", "numeral": "^2.0.6", "papaparse": "^5.5.3", "qr-scanner": "^1.4.2", "react": "16.14.0", "react-autosuggest": "9.4.3", "react-chartjs-2": "^4.2.0", "react-dom": "16.14.0", "react-number-format": "^4.4.1", "react-redux": "7.2.2", "react-responsive-carousel": "^3.2.23", "react-router-dom": "5.2.0", "react-scripts": "4.0.1", "react-select": "3.1.0", "redux": "4.0.5", "redux-saga": "1.1.3", "redux-thunk": "2.3.0", "reduxsauce": "1.2.0", "sass": "^1.89.2", "typescript": "3.7.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@types/autosuggest-highlight": "3.1.0", "@types/faker": "5.5.3", "@types/lodash": "4.14.150", "@types/node": "12.0.0", "@types/numeral": "0.0.28", "@types/papaparse": "^5.3.16", "@types/react": "^18.0.2", "@types/react-autosuggest": "9.3.13", "@types/react-dom": "16.9.0", "@types/react-places-autocomplete": "7.2.6", "@types/react-redux": "^7.1.23", "@types/react-router": "^5.1.18", "@types/react-router-dom": "^5.3.3", "@types/react-select": "3.0.11", "typescript": "4.5.4"}, "resolutions": {"@babel/core": "^7.16.0", "minimatch": "^3.0.8"}}