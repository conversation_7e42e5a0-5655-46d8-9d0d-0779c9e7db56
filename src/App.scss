/* autosuggest */
.autosuggest-wrap {
  .MuiInputBase-input {
    height: 43px;
    padding: 0 14px;
  }

  .MuiInput-underline {
    &::before,
    &::after {
      display: none;
    }
  }
}

.mandatory-flied {
  margin-left: 2px;
  color: #ed1b00;
  line-height: 12px;
  font-size: 14px;
  vertical-align: middle;
}

label.error,
.MuiFormHelperText-root {
  margin: 3px 0 0 0 !important;
  font-size: 11px !important;
  line-height: 12px !important;
  color: #ed1b00 !important;
  font-weight: normal !important;
  position: absolute !important;
  right: 2px;
  top: 100%;
}

/* orientation css */

.orientation-wrap {
  position: fixed;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background-color: #006aaf;
  z-index: 15;
  pointer-events: none;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  z-index: 9999;

  .ori-img {
    img {
      width: 200px;
    }
  }

  .orientation-container {
    height: 100vh;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    text-align: center;
    color: #fff;
  }

  .ori-head {
    font-size: 20px;
    margin-bottom: 10px;
  }
}

/* custom date picker */

.custom-date-picker {
  &.MuiFormControl-root {
    margin: 0;
    width: 100%;
  }

  .MuiInput-root {
    height: 47px;
    border-radius: 4px;
    border: solid 1px #eaeff3;
    background-color: #ffffff;
    padding: 0 15px;
    margin: 0;
  }

  .MuiInputBase-input {
    color: #133751;
    font-size: 14px;

    &.Mui-disabled {
      color: rgba(172, 182, 192, 0.7);
    }
  }

  .MuiIconButton-root {
    color: rgba(0, 108, 201, 0.8);
  }

  .MuiInput-underline {
    &::before,
    &::after {
      display: none;
    }
  }

  .MuiInputLabel-root {
    font-size: 13px;
    line-height: 1.15;
    color: #797979;
    margin-bottom: 13px;
    display: inline-block;
    position: relative;
    transform: inherit;
  }

  label + .MuiInput-formControl {
    margin: 0;
  }
}

.MuiPickersToolbar-toolbar {
  background: #f7931e;
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    from(#f7931e),
    to(#de5811)
  );
  background: -webkit-linear-gradient(left, #f7931e, #de5811);
  background: linear-gradient(90deg, #f7931e, #de5811);
}

.MuiPickersDay-daySelected {
  background: #f7931e;
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    from(#f7931e),
    to(#de5811)
  );
  background: -webkit-linear-gradient(left, #f7931e, #de5811);
  background: linear-gradient(90deg, #f7931e, #de5811);
}

.page-container {
  padding: 20px;
  @media screen and (max-width: 767px) {
    padding: 8px 15px;
  }
}

@media (max-width: 767px) {
  .custom-date-picker {
    .MuiInput-root {
      padding: 0 10px;
    }
  }
}
