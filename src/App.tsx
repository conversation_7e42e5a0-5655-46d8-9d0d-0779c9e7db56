import DateFnsUtils from "@date-io/moment";
import { MuiPickersUtilsProvider } from '@material-ui/pickers';
import React, { useEffect } from 'react';
import { Provider } from 'react-redux';
import './App.scss';
import AppContainer from './AppContainer';
import { ErrorBoundary } from './component/error/ErrorBoundary';
import configureStore from './redux/store/Store';
import './styles/main.scss';

export const appStore = configureStore();

function App() {
  // const store = configureStore();
  const [currentOrientation, setCurrentOrientation] = React.useState<any>("portrait");
  function doOnOrientationChange() {
    switch (window.orientation) {
      case -90: case 90:
        setCurrentOrientation('landscape');
        break;
      default:
        setCurrentOrientation('portrait');
        break;
    }
  }
  useEffect(() => {
    doOnOrientationChange();
    window.addEventListener("orientationchange", doOnOrientationChange, false);
    return () => window.removeEventListener("orientationchange", doOnOrientationChange)
  }, [])

  return (
    (currentOrientation === "portrait") ?
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <Provider store={appStore} >
          <ErrorBoundary>
            <AppContainer />
          </ErrorBoundary>
        </Provider>
      </MuiPickersUtilsProvider> :
      <div className="orientation-wrap">
        <div className="orientation-container">
          <div className="ori-img"><img src="./images/landscape.png" alt="orientation" /></div>
          <div className="ori-head">Please rotate your device</div>
          <div className="ori-text">We don't support landscape mode yet.</div>
        </div>
      </div>
  );
}

// /** Intercept any unauthorized request.
// * dispatch logout action accordingly **/
// const FORBIDDEN = 403;
// const { dispatch } = appStore; // direct access to redux store.
// Api.interceptors.response.use(response => response, error => {
//   if (error === FORBIDDEN) {
//     dispatch(setUnauthorizedUser());
//     dispatch(hideLoader());
//     dispatch(enableActionButton())
//   }
//   return Promise.reject(error);
// }
// );

export default App;
