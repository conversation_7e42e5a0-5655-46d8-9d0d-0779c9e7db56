import React, { Suspense } from "react";
import { BrowserRouter as Router, Route, Switch } from "react-router-dom";
import * as Path from './base/constant/RoutePath';
import MessageAlertBox from "./component/alert/MessageAlertBox";
import DataNotFound from './component/error/DataNotFound';
import Header from './component/header/Header';
import Loader from './component/loader/Loader';
import CardMapping from "./pages/cardMappingModule/CardMapping";
import DashBoard from './pages/dashboardModule/DashBoard';
import BulkApproveList from "./pages/requestModule/BulkApproveList";
import CreateRequest from "./pages/requestModule/CreateRequest";
import RequestDetails from "./pages/requestModule/RequestDetails";
import RequestListing from "./pages/requestModule/RequestListing";
import WalletListing from "./pages/walletModule/WalletListing";
import ContactsListing from "./pages/adhocPayments/contacts/ContactsListing";
import PayablesListing from "./pages/adhocPayments/payables/PayablesListing";
import CreateAdhocRequest from "./pages/adhocPayments/CreateAdhocRequest";
import PayablesDetail from "./pages/adhocPayments/payables/PayablesDetail";
import LogsListing from "./pages/adhocPayments/logs/LogsListing";
import logDetails from "./pages/adhocPayments/logs/logDetails";

function AppContainer() {
  return (
    <Router>
      <div id="wrapper" className="wrapper">
        <Suspense fallback={<Loader loading={true} />}>
          <Header />
          <MessageAlertBox />
          <div className="main-page d-flex">
            <Loader />
            {/* <AppDrawer /> */}
            <div className="main-content col">
              <Switch>
                <Route exact path={Path.DASHBOARD} component={DashBoard} />
                <Route exact path={Path.createRequestRoute} component={CreateRequest} />
                <Route exact path={Path.requestListingRoute + ":id?"} component={RequestListing} />
                <Route exact path={Path.bulkApproveRequestRoute} component={BulkApproveList} />
                <Route exact path={Path.walletListingRoute + ":id?"} component={WalletListing} />
                <Route exact path={Path.requestDetailsRoute + ":id?"} component={RequestDetails} />
                <Route exact path={Path.cardMappingRoute + ":id?"} component={CardMapping} />
                <Route exact path={Path.contactsListingRoute + ":id?"} component={ContactsListing} />
                <Route exact path={Path.payablesListingRoute + ":id?"} component={PayablesListing} />
                <Route exact path={Path.createAdhoceRequestRoute} component={CreateAdhocRequest} />
                <Route exact path={Path.payablesDetailsRoute} component={PayablesDetail} />
                <Route exact path={Path.adhocLogsRoute} component={LogsListing} />
                <Route exact path={Path.logDetailsRoute} component={logDetails} />
                <Route component={DataNotFound} />
              </Switch>
            </div>
          </div>
          {/* <Footer /> */}
        </Suspense>
      </div>
    </Router>
  );
}
export default AppContainer;
