export const legacyToken = "bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ik5ESkdSVVV3T0VFMk1rVTNRVGRGUkVNeE16SkVSVVpFUXpWQk1UVTRPRGc0TURJM1F6RXpRdyJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1co8_CbAQqWRc9JcoU3Z4NxLapkDFq6IomzvU0RmFqQu0vBQX4tDhpJYDSVi1okv2Yw_RL2mUx9Z34YbG_6hJYGioCZNIHwcKhGL6pjCl5Zm4J-cZV_UxGZZelkvw7hL5Q4mD8B9WW43vyw3whBrDPts4pZQbQjZelVcxKw6AGplJAk5mOaoyLbmu3_v47ASh7Ro7I3kHa0UxG6EzZYylFsFa3m044dL4ZvghZYkOVVAdPGbpg-JpYMeRvBv3J0w2NZ8Nts4r_UkNhVAJv0uyUxc6Ov141ZRq3MK-Vm3McskY_yWskU2u5IV01pJ6Tgzojyl_unqRv_eLbSuMgrDeQ";
export const auth = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ik5ESkdSVVV3T0VFMk1rVTNRVGRGUkVNeE16SkVSVVpFUXpWQk1UVTRPRGc0TURJM1F6RXpRdyJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1co8_CbAQqWRc9JcoU3Z4NxLapkDFq6IomzvU0RmFqQu0vBQX4tDhpJYDSVi1okv2Yw_RL2mUx9Z34YbG_6hJYGioCZNIHwcKhGL6pjCl5Zm4J-cZV_UxGZZelkvw7hL5Q4mD8B9WW43vyw3whBrDPts4pZQbQjZelVcxKw6AGplJAk5mOaoyLbmu3_v47ASh7Ro7I3kHa0UxG6EzZYylFsFa3m044dL4ZvghZYkOVVAdPGbpg-JpYMeRvBv3J0w2NZ8Nts4r_UkNhVAJv0uyUxc6Ov141ZRq3MK-Vm3McskY_yWskU2u5IV01pJ6Tgzojyl_unqRv_eLbSuMgrDeQ"
export const menuTemplateUrl = "";
export const getUserProfileUrl = "/_svc/ops-payment-service/v1/get-access-roles";


//*************Request Module API */
export const requestListUrl = "/_svc/ops-payment-service/v1/payment-list"
export const requestPaymentDetailsUrl = "/_svc/ops-payment-service/v1/get-payment-details"
export const getVehicleListFromWalletUrl = "/_svc/ops-payment-service/v1/vehicle-list";
export const getVehiclesTripsUrl = "/_svc/ops-payment-service/v1/trip";
export const createRequestUrl = "/_svc/ops-payment-service/v1/payment-request";
export const createRequestForRazorpayWalletUrl = "/_svc/ops-payment-service/v3/payout-payment-request";
// export const getRequestAccessRolesUrl = "/_svc/ops-payment-service/v1/get-access-roles";
export const bulkApproveUrl = "/_svc/ops-payment-service/v1/bulk-approve-payment-request";
export const rejectRequestUrl = "/_svc/ops-payment-service/v1/reject-payment-request";
export const cancelRequestUrl = "/_svc/ops-payment-service/v1/cancel-payment-request";
export const modifyRequestUrl = "/_svc/ops-payment-service/v1/modify-payment-request";
export const cancelRequestPaidUrl = "/_svc/ops-workflowmanager/v1/cancel-payment";
export const cancelRequestPaidGoboltCashUrl = "/_svc/ops-workflowmanager/v1/cancel-payment/gobolt-cash";
export const getBpclMobileNumberUrl = "/_svc/ops-integrator/v1/ops-integrator/bpcl-get-phone-no";
export const getIoclLMobileNumberUrl = "/_svc/gobolt-ops-payments-card-mapping/v1/iocl-mobile-number";
export const getJioBPMobileNumberUrl = "/_svc/ops-integrator/v1/ops-integrator/jio-bp-mobile-number";
export const getBpclOtpUrl = "_svc/ops-integrator/v1/ops-integrator/bpcl-get-otp";
export const getCurrentWalletBalanceUrl = "/_svc/ops-integrator/v1/ops-integrator/get-wallet-balance";
export const updateBpclMobileNumberUrl = "_svc/ops-workflowmanager/v1/bpcl-update-phone-no";
export const updateIoclMobileNumberUrl = "_svc/ops-workflowmanager/v1/iocl-update-phone-no";
export const getPreviousPaymentsUrl = "/_svc/ops-payment-service/v1/get-past-entries";
export const getStateListUrl = "/_svc/ops-payment-service/v1/state-list"
export const getStateFuelRateUrl = "/_svc/ops-payment-service/v1/suggestive-fuel-rate"
export const changeUnknownStatusUrl = "/_svc/ops-workflowmanager/v1/happay-unknown-status-change";
export const updateJioBPMobileNumberUrl = "_svc/ops-workflowmanager/v1/jiobp-update-phone-no";
export const getVehicleDetailsUrl = "/_svc/legacy/gobolt/vehicle/get-vehicle-details/";
export const getPayoutContactsUrl = "/_svc/ops-payment-service/v1/ops-payment/contacts/";
export const createPayoutContactUrl = "/_svc/ops-payment-service/v1/ops-payment/contacts";
export const updatePayoutContactUrl = "/_svc/ops-payment-service/v1/ops-payment/contacts";
export const getPayoutFundAccountsForContactUrl = "/_svc/ops-payment-service/v1/ops-payment/fund-accounts";
export const addFundAccountBankUrl = "/_svc/ops-payment-service/v1/ops-payment/fund-accounts/bank";
export const addFundAccountVpaUrl = "/_svc/ops-payment-service/v1/ops-payment/fund-accounts/vpa";

//*************Wallet Module API */
export const getMcplUrl = "/_svc/gobolt-wallet/v1/gobolt-wallet/transactions";
export const addMcplMoneyUrl = "/_svc/gobolt-wallet/v1/gobolt-wallet/balance";
export const adBlueWalletMoneyUrl = "/_svc/gobolt-wallet/v1/adblue-wallet/balance";

export const getAdBlueWMoneyUrl = "/_svc/gobolt-wallet/v1/adblue-wallet/balance";
export const getAdblueUrl = "/_svc/gobolt-wallet/v1/adblue-wallet/transactions";

export const getContinentalMoneyUrl = "/_svc/gobolt-wallet/v1/continental-petroleums/balance";
export const getContinentalUrl = "/_svc/gobolt-wallet/v1/continental-petroleums/transactions";

//*************OPS Workflow Manager */
export const approveWorkflowManagerUrl = "/_svc/ops-workflowmanager/v1/approve-payment"
export const orchestrationTokenUrl = "/_svc/ops-workflowmanager/v1/ops-orchestration"
export const reconcileCamionsWalletWorkflowManagerUrl = "/_svc/ops-workflowmanager/v1/reconcile-camions-payment"


//************** */
export const getVehicleNumbersUrl = "/_svc/legacy/gobolt/invoice-vehicle-list/";
export const getLocationsUrl = "/_svc/legacy/gobolt/finance/customer/locations/"
export const getTripStatusUrl = "/_svc/legacy/gobolt/trip/own-vehicle/payments-trip-status/";
export const getDriverLicenseUrl = "/_svc/legacy/gobolt/get-driver-license/";

//*************Dashboard Module API */
export const getPaymentsUrl = "/_svc/ops-payment-service/v1/payments-dashboard";


//*************Card Mapping Module API */
export const createCardMappingUrl = "/_svc/ops-workflowmanager/v1/create-card-mapping";
export const updateCardMappingUrl = "/_svc/ops-workflowmanager/v1/update-card-mapping";
export const deleteCardMappingUrl = "/_svc/gobolt-ops-payments-card-mapping/v1/delete-card-mapping";
export const getCardMappingListUrl = "/_svc/gobolt-ops-payments-card-mapping/v1/list-card-mapping";
export const getVehicleNumberListUrl = "/_svc/legacy/gobolt/vehicles-drop-down-list/";
export const getJioBPCardMappingListUrl = "/_svc/gobolt-ops-payments-card-mapping/v1/jio-bp/list-card-mapping";
export const createJioBPCardMappingUrl = "/_svc/gobolt-ops-payments-card-mapping/v1/jio-bp/card-mapping/";
export const updateJioBPCardMappingUrl = "/_svc/gobolt-ops-payments-card-mapping/v1/jio-bp/update-card-mapping/";
export const deleteJioBPCardMappingUrl = "/_svc/gobolt-ops-payments-card-mapping/v1/jio-bp/delete-card-mapping/";
export const getJioBPCardNumberUrl =  "/_svc/gobolt-ops-payments-card-mapping/v1/jio-bp/list-card-mapping";

//*************Upload Document Module API */
export const uploadRequestDocumentsUrl = "/_svc/ops-payments-upload/v1/upload";
export const deleteRequestDocumentsUrl = "/_svc/ops-payments-upload/v1/delete";

//**************Adhoc payments API */
export const getPayableContactListUrl = "/_svc/ops-payment-service/v1/payable-contacts-list";
export const getContactTypeUrl = "/_svc/ops-payment-service/v1/get-contact-types"
export const getIntegrationTypeUrl = "/_svc/ops-payment-service/v1/get-integration-types"