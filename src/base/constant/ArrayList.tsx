import * as Path from './RoutePath';

export const rowsPerPageOptions = [25, 50, 100]

export enum DispatchPeriodsEnum {
    Today = 'Today',
    Last_Week = 'Last Week',
    Last_Month = 'Last Month',
    Last_Year = 'Last Year',
    Custom = 'Custom',
}

export const menuList = [
    {
        name: '/',
        label: 'Dashboard',
        sequence: 1,
        image: '/images/logo.svg',
        routePath: Path.DASHBOARD,
    },
    {
        name: '/procurement/',
        label: 'Procurement',
        sequence: 2,
        image: '/images/logo.svg',
        routePath: Path.CONTRACT,
        stateKey: 'procurementSubMenu',
        subMenu: [
            {
                name: '/procurement/contract/',
                label: 'Contract',
                sequence: 1,
                routePath: Path.CONTRACT,
            },
            {
                name: '/procurement/monthlyDieselRate/',
                label: 'Monthly Diesel Rate',
                sequence: 2,
                routePath: Path.MONTHLYDIESELRATE,
            },
        ],
    },
    {
        name: '/test/',
        label: 'Test',
        sequence: 2,
        image: '/images/logo.svg',
        routePath: Path.TEST,
        stateKey: 'testSubMenu',
        subMenu: [
            {
                name: '/test/test1/',
                label: 'Test 1',
                sequence: 1,
                routePath: Path.TEST,
            },
            {
                name: '/test/test2/',
                label: 'Test 2',
                sequence: 2,
                routePath: Path.TEST2,
            },
        ],
    },
]

export enum requestListingStatusEnum {
    PENDING = 'Pending',
    APPROVED = 'Approved',
    PAID = 'Paid',
    RECONCILED = 'Reconciled',
    REJECTED = 'Rejected',
    CANCELLED = 'Cancelled',
    'INVALID PENDING' = 'INVALID_PENDING',
    'INVALID RECONCILED' = 'INVALID_RECONCILED',
    UNKNOWN = 'Unknown',
}

export enum contactListingStatusEnum {
    PENDING = 'PENDING',
    APPROVED = 'APPROVED',
    REJECTED = 'REJECTED',
   }

export enum payableStatusEnum {
  PENDING = "Pending",
  APPROVED = "Approved",
  PAID = "Paid",
  FAILED = "Failed",
  RECONCILED = "Reconciled",
  REJECTED = "Rejected",
  CANCELLED = "Cancelled",
}

export enum paymentStatusEnum {
    PENDING = 'PENDING',
    APPROVED = 'APPROVED',
    PAID = 'PAID',
    RECONCILED = 'RECONCILED',
    REJECTED = 'REJECTED',
    CANCELLED = 'CANCELLED',
    'INVALID PENDING' = 'INVALID_PENDING',
    'INVALID RECONCILED' = 'INVALID_RECONCILED',
    UNKNOWN = 'UNKNOWN',
}

export const walletNamesArray = ["MCPL", "Hub Adblue", "Continental Petroleums"];

export const requestListingStatusArray = ["Pending", "Approved", "Paid", "Reconciled", "Rejected", "Cancelled", "Invalid Pending", "Invalid Reconciled", "Unknown"];
export const contactListingStatusArray = ["Pending", "Approved", "Rejected"];

export const walletStatusCodeArray = ["pending", "approved", "paid", "reconciled", "rejected", "cancelled", "invalidPending", "invalidReconciled"];

export const contactsListingArray = ["Pending", "Approved", "Rejected"];
export const paybalesListingStatusArray = ["Pending", "Approved", "Paid", "Failed", "Reconciled", "Rejected", "Cancelled"];

export enum walletStatusListEnum {
    PENDING = 'pending',
    APPROVED = 'approved',
    PAID = 'paid',
    RECONCILED = 'reconciled',
    REJECTED = 'rejected',
    CANCELLED = 'cancelled',
    "INVALID PENDING" = 'invalidPending',
    "INVALID RECONCILED" = 'invalidReconciled',
}


export const requestListingPaymentStatusArray = ["PENDING", "APPROVED", "PAID", "RECONCILED", "REJECTED", "CANCELLED", "INVALID_PENDING", "INVALID_RECONCILED"]


export const walletList = [
    {
        label: 'BPCL Wallet',
        value: 'BPCL',
    },
    {
        label: 'Camions Razor Pay',
        value: 'CAMIONS_RAZOR_PAY',
    },
    {
        label: 'Camions Wallet',
        value: 'GOBOLT_CASH',
    },
    {
        label: 'Continental Petroleums',
        value: 'CONTINENTAL_PETROLEUMS',
    },
    {
        label: 'Gobolt Wallet (MCPL)',
        value: 'MCPL',
    },
    {
        label: 'Happay Wallet',
        value: 'HAPPAY',
    },
    {
        label: 'Hub AdBlue',
        value: 'HUB_ADBLUE',
    },
    {
        label: 'IOCL Wallet',
        value: 'IOCL',
    },
    {
        label: 'JioBP Wallet',
        value: 'JIOBP',
    },
    {
        label: 'Om Petro Mart (Cash/Happay)',
        value: 'OM_PETRO_MART_CASH',
    },
    {
        label: 'Om Petro Mart (Diesel/BPCL)',
        value: 'OM_PETRO_MART_DIESEL',
    },
]

export interface UploadDocs {
    label: string;
    value: string;
}
export const UploadDocumentList: Array<UploadDocs> = [
    {
        'label': "Adblue Amount",
        'value': "adblue_amount"
    }, 
    {
        'label': "Challan Amount",
        'value': "challan_amount"
    },
    {
        'label': "Accident Settlement Amount",
        'value': "accident_settlement_amount"
    },
    {
        'label': "Advance Amount",
        'value': "dalla_amount"
    },
    {
        'label': "Toll Amount",
        'value': "toll_amount"
    },
    {
        'label': "Fooding Amount",
        'value': "fooding_amount"
    },
    {
        'label': "Loading Amount",
        'value': "loading_amount"
    },
    {
        'label': "Unloading Amount",
        'value': "unloading_amount"
    }
]

export enum uploadDocumentMapperEnum {
    adblue_amount = 'adblueAmount',
    challan_amount = 'challanAmount',
    maintenance_amount = 'maintenanceAmount',
    accident_settlement_amount = 'accidentSettlementAmount',
    dalla_amount = 'dallaAmount',
    fuel_amount = 'fuelAmount',
    toll_amount = 'tollAmount',
    loading_amount = 'loadingAmount',
    unloading_amount = 'unloadingAmount',
    fooding_amount = 'foodingAmount',
    gobolt_cash_reconcile = 'goboltCashReconcile'
}

export enum balanceWalletListEnum {
    'BPCL' = 'BPCL',
    'HAPPAY' = 'HAPPAY',
    'IOCL'= 'IOCL',
    'JIOBP' = 'JIOBP',
}

export enum walletListEnum {
  'BPCL' = 'BPCL',
  'JIOBP' = 'JIOBP',
  'HAPPAY' = 'HAPPAY',
  'MCPL' = 'MCPL',
  'HUB_ADBLUE' = 'HUB_ADBLUE',
  'OM_PETRO_MART_CASH' = 'OM_PETRO_MART_CASH',
  'OM_PETRO_MART_DIESEL' = 'OM_PETRO_MART_DIESEL',
  'IOCL' = 'IOCL',
  'CONTINENTAL_PETROLEUMS' = 'CONTINENTAL_PETROLEUMS',
  'CAMIONS_RAZOR_PAY' = 'CAMIONS_RAZOR_PAY',
  'GOBOLT_FUEL_PUMP' = 'GOBOLT_FUEL_PUMP',
  'OPS_WALLET' = 'OPS_WALLET',
  'GOBOLT_CASH' = 'GOBOLT_CASH'
}

export const bulkApproveWalletList = [
    {
        label: 'GoBoLT Wallet (MCPL)',
        value: 'MCPL',
    },
    {
        label: 'Om Petro Mart (Cash)',
        value: 'OM_PETRO_MART_CASH',
    },
    {
        label: 'Om Petro Mart (Diesel)',
        value: 'OM_PETRO_MART_DIESEL',
    },
]

export const transactionTypesOptionList = [
    {
        label: 'CREDIT',
        value: 'CREDIT',
    },
    {
        label: 'ALL',
        value: 'ALL',
    },
    {
        label: 'DEBIT',
        value: 'DEBIT',
    },
]

export const fuelWalletModify = ['GoBoLT Wallet (MCPL)', 'Om Petro Mart (Diesel/BPCL)', 'Continental Petroleums']
export const fuelAndAdBlueWalletModify = ['BPCL Wallet', 'Happay Wallet', 'IOCL Wallet', 'Camions Razor Pay', 'JioBP Wallet']

export const rolesArray = ["payments-viewer", "payments-editor", "payments-approver", "wallet-viewer", "wallet-editor"]

export const commonModalArray: any = ["Reject Request", "Cancel Request", "Approve"];

export interface Wallet {
    label: string;
    name: string;
    amount: string;
    percent: string;
}
export const wallets: Array<Wallet> = [
    {
        label: "BPCL Wallet",
        name: "bpcl",
        amount: "bpclAmount",
        percent: "bpclPercent"
    },
    {
        label: "Camions Razor Pay",
        name: "camionsRazorPay",
        amount: "camionsRazorPayAmount",
        percent: "camionsRazorPayPercent"
    },
    {
        label: "Camions Wallet",
        name: "goboltCash",
        amount: "goboltCashAmount",
        percent: "goboltCashPercent"
    },
    {
        label: "Continental Petroleums",
        name: "continentalPetroleums",
        amount: "continentalPetroleumsAmount",
        percent: "continentalPetroleumsPercent"
    },
    {
        label: "GoBolt Fuel Pump",
        name: "goboltFuelPump",
        amount: "goboltFuelPumpAmount",
        percent: "goboltFuelPumpPercent"
    },
    {
        label: "GoBoLT Wallet (MCPL)",
        name: "mcpl",
        amount: "mcplAmount",
        percent: "mcplPercent"
    },
    {
        label: "Happay Wallet",
        name: "happay",
        amount: "happayAmount",
        percent: "happayPercent"
    },
    {
        label: "IOCL Wallet",
        name: "iocl",
        amount: "ioclAmount",
        percent: "ioclPercent"
    },
    {
        label: "JioBP Wallet",
        name: "jiobp",
        amount: "jiobpAmount",
        percent: "jiobpPercent"
    },
    {
        label: "Hub AdBlue",
        name: "hubAdblue",
        amount: "hubAdblueAmount",
        percent: "hubAdbluePercent"
    },
    {
        label: "Om Petro Mart (Diesel/BPCL)",
        name: "omPetroMartDiesel",
        amount: "omPetroMartDieselAmount",
        percent: "omPetroMartDieselPercent"
    },
    {
        label: "Om Petro Mart (Cash/Happay)",
        name: "omPetroMartCash",
        amount: "omPetroMartCashAmount",
        percent: "omPetroMartCashPercent"
    },
    {
        label: "OPS Wallet",
        name: "opsWallet",
        amount: "opsWalletAmount",
        percent: "opsWalletPercent"
    }
];

export const dashboardImageArray = ["./images/Pending.svg", "./images/paid.svg", "./images/paid.svg", "./images/Reconciled.svg", "./images/Rejected.svg", "./images/Cancelled.svg", "./images/Pending.svg", "./images/Reconciled.svg"]

export const walletAmountsArray = ["tollAmount", "challanAmount", "loadingAmount", "unloadingAmount", "foodingAmount", "dallaAmount", "maintainceAmount", "accidentSettlemantAmount", "fuelAmount", "adblueAmount"]

export enum orchestrationEnum {
    SUCCESS = 200,
    FAIL = 400,
    AUTH_FAILURE = 401,
}

export enum orchestrationRunningStatusEnum {
    RUNNING = 'Running',
    COMPLETED = 'Completed',
    FAILED = 'Failed',
}

export const dashboardNumberColors: Array<string> = ["#006CC9", "#5DB3AA", "#748E9F", "#96BA42", "#55c9ca", "#083654", "#F28709", "#B72EAC", "#555BE5", "#ED1B00", "#1FC900", "#E3B600", "#d57979", "#000000"];

export const dashboardPercentageColors: Array<string> = ["#F7931E", "#1FC900", "#1FC900", "#083654", "#006CC9", "#ED1B00", "#F7931E", "#083654"];

export const headerMenuButtons: Array<string> = ["Dashboard", "View Request", "MCPL", "Card Mapping"];
export const adhocMenuButtons: Array<string> = ["Contacts", "Payables"];

export const statusPendingOptionList = [
    {
        label: 'SUCCESS - Wallet is Paid',
        value: 'SUCCESS',
    },
    {
        label: 'FAILED - Wallet not Paid',
        value: 'FAILED',
    },
]

export const statusPaidOptionList = [
    {
        label: 'SUCCESS - Wallet Withdrawn',
        value: 'SUCCESS',
    },
    {
        label: 'FAILED - Wallet not Withdrawn',
        value: 'FAILED',
    },
]
export const fuelWalletCodesList = ['OM_PETRO_MART_DIESEL', 'MCPL', 'CONTINENTAL_PETROLEUMS'];

export const thirdPartyWalletCodesList = ['BPCL', 'HAPPAY', 'IOCL', 'CAMIONS_RAZOR_PAY', 'JIOBP'];

export const cardMappingArray = ['IOCL Mapping', 'JioBP Mapping']

export enum cardMappingEnum {
    IOCL_MAPPING = "IOCL Mapping",
    JIOBP_MAPPING = "JioBP Mapping",
}

export const FilterWalletList = [
    ...walletList,
    {
        label: 'GoBolt Fuel Pump',
        value: 'GOBOLT_FUEL_PUMP',
    },

    {
        label: 'OPS Wallet',
        value: 'OPS_WALLET',
    },
]

export const payoutContactTypeOptions = [
    { label: "Customer", value: "Customer" },
    { label: "Vendor", value: "Vendor" },
    { label: "Employee", value: "Employee" },
    { label: "Driver", value: "Driver" },
]; 

export const adhocPaymentCategoryOptions = [
    { label: "Vendor", value: "Vendor" },
    { label: "Customer", value: "Customer" },
]
