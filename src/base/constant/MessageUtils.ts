export const dataNotFoundMessage = "No Data Found !";

export const fuelQuantityLabel = "Fuel Quantity (Ltr)";
export const fuelQuantityPlaceHolder = "Enter Fuel Quantity";
export const fuelPriceLabel = "Fuel Price (₹/Ltr)";
export const fuelPricePlaceHolder = "Enter Fuel Price";
export const invoiceNoLabel = "Invoice No.";
export const invoiceNoPlaceHolder = "Enter Invoice No.";

export const adblueQuantityLabelInLtr = "AdBlue Quantity (Ltr)";
export const adblueQuantityPlaceHolder = "Enter AdbBlue Quantity";
export const adblueRateLabel = "AdBlue Rate (₹/Ltr)";
export const adblueRatePlaceHolder = "Enter AdBlue Rate";

export const tripCodeLabel = 'Trip Code';
export const orderCodeLabel = 'Order Code';
export const originLabel = 'Origin';
export const destinationLabel = 'Destination';
export const driverNameLabel = 'Driver Name';
export const gbCodeLabel = 'GB Code';
export const driverMonileNoLabel = "Driver Mobile Number";
export const driverMobileNumberLabel = 'Driver Mobile Number';
export const driverMobileNumberNewLabel = 'Updated Mobile Number';
export const approvedByLabel = 'Approved By';
export const approvedAtLabel = 'Approved At';
export const customerNameLabel = "Customer";
export const raisedByLabel = 'Raised By';
export const raisedAtLabel = 'Raised At';
export const tripStatusLabel = 'Trip Status';

export const loadingAmountLabel = "Loading Amount";
export const tollAmountLabel = "Toll Amount";
export const challanAmountLabel = "Challan Amount";
export const unloadingAmountLabel = "Unloading Amount";
export const foodingAmountLabel = "Fooding Amount";
// export const dallaAmountLabel = "Dalla Amount";
export const advanceAmountLabel = "Advance Amount";
export const maintainceAmountLabel = "Maintenance Amount";
export const accidentSettlemantAmountLabel = "Accident Settlement Amount";
export const fuelAmountLabel = "Fuel Amount";
export const fuelRatePerLitreLabel = "Fuel Rate Per Litre";
export const adblueAmountLabel = "AdBlue Amount";
export const adblueQuantityLabel = "AdBlue Quantity";
export const adblueRatePerLitreLabel = "AdBlue Rate Per Litre";
export const remarksLabel = "Remarks";

export const oldMobileNumberLabel = "Old Mobile Number";
export const newMobileNumberLabel = "New Mobile Number";

export const fromDateError = "Enter valid from date";
export const toDateError = "Enter valid to date";
export const downloadCsvTitle = "Download CSV File";

export const vehicleNumberLabel = "Vehicle Number";
export const cardPanLabel = "Card Pan";

export const mcplLimitMessage = "Total Request Amount Cannot Exceed ₹90000 For Gobolt Wallet (MCPL)";
export const adBlueLimitMessage = "Total Request Amount Cannot Exceed ₹7500 For Hub Adblue";
export const omPetroMartCashLimitMessage = "Total Request Amount Cannot Exceed ₹15000 For Om Petro Mart (Cash/Happay)";
export const omPetroMartDieselLimitMessage = "Total Request Amount Cannot Exceed ₹90000 For Om Petro Mart (Diesel/BPCL)";
export const continentalPetroleumsMessage = "Total Request Amount Cannot Exceed ₹90000 For Continental Petroleums";
export const confirmLabel = "Confirm";
export const typeConfrimLabel = "Type confirm here";
export const cardNumberLabel = "Card Number";
export const utrNoLabel = "UTR Number";
export const goboltCashLimitMessage = "Total Request Amount Cannot Exceed ₹15000 For Camions Wallet";
export const reconciledByLabel = 'Reconciled By';
export const reconciledAtLabel = 'Reconciled At';
export const vehicleDetailsTitle = 'Vehicle Details';

export const payoutIdTitle = 'Payout ID';
export const statusTitle = 'Status';
export const beneficiaryNameTitle = 'Beneficiary Name';
export const accountNoTitle = 'Account No.';
export const ifscTtitle = 'IFSC';
export const upiIdTitle = 'UPI ID(VPA)';
export const saveTitle = 'Save';
export const contactNameTitle = 'Contact Name';
export const contactPhoneTitle = 'Contact Phone';
export const contactTypeTitle = 'Contact Tpe';
export const contactEmailTitle = 'Contact Email';
export const addressTitle = 'Address';
export const billNumberTitle = 'Bill Number';
export const narrationTitle = 'Narration';
export const paymentCategoryTitle = 'Payment Category';
export const panCardNumberTitle = 'PAN Card Number';
export const referenceIdTitle = 'Reference ID';
export const documentTitle = 'Document';
export const bankTitle = 'Bank Name';
export const paymentModeTitle = 'Payment Mode';
