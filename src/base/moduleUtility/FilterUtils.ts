export const requestFilters: any = {
    vehicleNumberLabel: "vehicleNumber",
    orderCode: "orderCode",
    tripCode: "tripCode",
    paymentId: "paymentId",
    wallet: "walletCode",
    originLabel: "originName",
    destinationLabel: "destinationName",
    mobileNumber: "driverMobileNumber",
    requestFromDate: "fromDateTime",
    requestToDate: "toDateTime",
    createdBy: "createdBy",
}

export const contactFilters: any = {
    name: "name",
    phone: "phone",
    contactType: "contactType",
    integrationTypeLabel: "integrationType",
    companyName: "companyName",
    panNumber: "panNumber",
}

export const payableFilters: any = {
    paymentId: "paymentId",
    referenceId: "referenceId",
    setPaymentCategory: "setPaymentCategory",
    integrationType: "integrationType",
    contactNumber: "contactNumber",
    contactName: "contactName",
    raisedBy:"raisedBy"
}

export const dashboardFilters: any = {
    fromDate: "fromDateTime",
    toDate: "toDateTime",
}

export const cardMappingFilters: any = {
    vehicleNumberLabel: "vehicleNumber",
    cardPan: "cardPan",
    cardNo: "cardNo",
}