import { hideLoader, showAlert } from '../../redux/actions/AppActions';
import { isNullValue } from './StringUtils';
export function handleApiError(error: any, appDispatch: any, onHideCallback?:Function) {
    appDispatch(hideLoader());
    !isNullValue(error) && isDisplayError(error) && appDispatch(showAlert(error, undefined, undefined, onHideCallback), "false");
}

function isDisplayError(message: string) {
    switch (message) {
        case "Nothing to display":
            return false;
        case "Unauthorized":
            return false;
        case "403":
            return false;
        default:
            return true;
    }

}