import { menuList } from '../base/constant/ArrayList';

export function getMenuIndex(location: any) {
    let data: any = {
        index: 0,
    }
    location.pathname && menuList.map((element: any, index: number) => {
        if (location.pathname.startsWith(element.name)) {
            data.index = index;
            data.element = element;
        }
        return true;
    });
    return data;
}
