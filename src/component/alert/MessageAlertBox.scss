/* message modal */
.message-modal.custom-modal {
	.MuiDialogContent-root {
		padding: 24px 10px 0;
		.MuiSvgIcon-root {
			font-size: 56px;
			line-height: 1.09;
			color: #ED1B00;
			opacity: 0.9;
		}
		.content-heading {
			line-height: 1.18;
			text-align: center;
			margin: 10px 0 0;
			font-size: 24px;
			font-weight: 500;
		}
		.content-heading.success {
			color: #1FC900;
		}
		.content-heading.error {
			color: #ED1B00;
		}
		.content-heading.info {
			color: #f7931e;
		}
		label {
			line-height: 20px;
			margin-bottom: 0;
			font-size: 17px;
			font-weight: 300;
			color: #768a9e;
			margin-top: 10px;
			max-width: 400px;
			word-break: break-word;
			white-space: break-spaces;
		}
	}
	.MuiDialog-paper {
		min-width: 450px;
		max-width: 450px;
	}
	.MuiDialogActions-root {
		justify-content: center !important;
		padding: 32px 0px 20px;
	}
	.center {
		.MuiDialogActions-root {
			.MuiDialogActions-spacing {
				justify-content: center;
			}
		}
	}
}
.message-modal.custom-modal.success {
	.MuiDialogContent-root {
		.MuiSvgIcon-root {
			color: #1FC900;
		}
	}
}
.message-modal.custom-modal.error {
	.MuiDialogContent-root {
		.MuiSvgIcon-root {
			color: #ED1B00;
		}
	}
}
.message-modal.custom-modal.info {
	.MuiDialogContent-root {
		.MuiSvgIcon-root {
			color: #f7931e;
		}
	}
}
@media (max-width: 767px) {
	.message-modal.custom-modal {
		.MuiDialog-paper {
			min-width: 100%;
		}
	}
}
