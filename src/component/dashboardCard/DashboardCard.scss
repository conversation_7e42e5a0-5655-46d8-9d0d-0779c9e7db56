.status-card {
  width: calc(100% / 7);
  box-shadow: 0px 1px 3px #00000029;
  border-radius: 8px;
  background: #fff;
  padding: 10px;
  cursor: pointer;
  position: relative;
  border: 1px solid transparent;
  cursor: pointer;
  &:not(:last-child) {
    margin-right: 15px;
  }
  @media screen and (max-width: 991px) {
    width: 31.8%;
    margin-bottom: 15px;
    &:nth-of-type(3n + 3) {
      margin-right: 0;
    }
  }

  @media screen and (max-width: 767px) {
    width: 100%;
    margin-right: 0;
    margin-bottom: 20px;
    padding: 15px 25px;
  }
  p {
    font-size: 14px;
    color: #748e9f;
    margin-bottom: 0;
    width: max-content;
    line-height: 14px;
    @media screen and (max-width: 767px) {
      margin-bottom: 8px;
    }
  }
  h5 {
    color: #1e1d1f;
    margin-bottom: 0;
    font-size: 30px;
    line-height: 32px;
    font-weight: 600;
    @media screen and (max-width:767px) {
      margin-bottom: 5px;
    }
  }
  span {
    color: #e39840;
    font-size: 14px;
    font-weight: 500;
  }
  img {
    width: 60px;
    height: 60px;
    @media screen and (max-width:767px) {
      width: 75px;
      height: 75px;
    }
  }

  .status-card-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.status-card-active {
  border: 1px solid #f7931e;
  &::after,
  &::before {
    content: "";
    display: block;
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  &::before {
    border-color: #f7931e transparent transparent transparent;
    border-width: 11px;
    bottom: -33px;
  }
  &::after {
    border-color: #fff transparent transparent transparent;
    border-width: 11px;
    bottom: -32px;
  }
}
