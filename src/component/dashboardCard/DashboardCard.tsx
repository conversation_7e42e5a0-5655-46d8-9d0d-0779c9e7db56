import React from 'react';
import { dashboardImageArray } from '../../base/constant/ArrayList';
import { isMobile } from '../../base/utility/ViewUtils';
import "./DashboardCard.scss";

interface DashboardCardProps {
    statusName?: string,
    numberOfPayments?: any,
    statusPercent?: any,
    index?: any,
    onClick?: any,
    className?: any,
    activeCard?: any
    statusCode?: string,
    percentageColor?: string,
    children?: any
}

function DashboardCard(props: DashboardCardProps) {
    const { statusName, numberOfPayments, statusPercent, index, onClick, activeCard, statusCode, percentageColor, children } = props;

    return (
        <>
            <div className={(activeCard === statusCode) ? `status-card status-card-active` : "status-card"} onClick={() => onClick(statusCode, statusName)} >
                {!isMobile && <p>{statusName}</p>}
                <div className='status-card-inner'>
                    <div className="status-card--left">
                        {isMobile && <p>{statusName}</p>}
                        <h5>{numberOfPayments}</h5>
                        <span style={{ color: percentageColor }} >{statusPercent + '%'}</span>
                    </div>
                    <div>
                        <img src={dashboardImageArray[index]} alt="icon" />
                    </div>
                </div>
            </div>
            <div>
                {children}
            </div>
        </>
    )
}
export default DashboardCard