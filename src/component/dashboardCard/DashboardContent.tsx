import Numeral from 'numeral';
import React from 'react';
interface DashboardContentProps {
    walletName: string,
    textStyle?: string
    walletAmount: number,
    walletNumberOfPayments: number,
    statusLabel?:string,
    numberColor?:string,
}
function DashboardContent(props: DashboardContentProps) {
    const { walletName, walletNumberOfPayments, textStyle, walletAmount, statusLabel, numberColor } = props;

    return (
        <div>
            <div className='number-wallet'>
                <h5 style={{ color: numberColor}} className={textStyle}>{walletNumberOfPayments}</h5>
                <p>{walletName === 'Total' ? walletName + " " + statusLabel : walletName}</p>
                <div className="currency-wrap">
                    <p><span>₹</span>{Numeral(walletAmount).format("0,0")}</p>
                </div>
            </div>
        </div>
    )
}
export default DashboardContent