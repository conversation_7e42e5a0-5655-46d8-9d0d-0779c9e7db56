import React from 'react'
import ModalContainer from '../../modals/ModalContainer'
import { List, ListItem, ListItemText, makeStyles } from '@material-ui/core'
import Button from '../widgets/button/Button'
import { GetApp } from '@material-ui/icons'

type DownloadItem = {
  label: string;
  onClick?: (url: string) => void;
  href?: string;
};

type DownloadCSVModalProps = {
  open: boolean
  title?: string
  items: DownloadItem[]
  onClose: () => void
}

const useStyles = makeStyles({
  modalContact: {
    margin: '0',
    '& .MuiDialogContent-root': {
      padding: '0'
    },
    '& .MuiDialogActions-root': {
      display: 'none'
    }
  },
  list: {
    '& .MuiTypography-root': {
      fontSize: '15px'
    }
  }
});

const DownloadCSVModal = (props: DownloadCSVModalProps) => {
  const { open, onClose, title = "Download CSV Sample", items } = props;
  const classes = useStyles();
  return (
    <ModalContainer
      title={title}
      styleName={`${classes.modalContact} modal-contact`}
      open={open}
      onClose={onClose}
    >
      <List className={classes.list}>
        {items.map((item, index) => (
          <ListItem
            style={{ borderBottom: 'solid 1px #ddd' }}
            key={index}
          >
            <ListItemText primary={item.label} />
            <Button
              title={"Download"}
              buttonStyle={"btn-detail"}
              leftIcon={<GetApp />}
              onClick={() => item.onClick?.(item.href || '')}
            />
          </ListItem>
        ))}
      </List>
    </ModalContainer>
  )
}

export default DownloadCSVModal