.product {
    .MuiButton-root{
        width: 131px;
        height: 33px;
        border-radius: 4px;
        background-image: linear-gradient(to right, #006cc9, #145288);
        padding: 0 0px 0 25px;
        font-size: 14px;
        font-weight: 500;
        line-height: 1.14;
        justify-content: space-between;
        text-transform: capitalize;
        overflow: hidden;
        box-shadow: none;

        .icon-wrap{
            width: 36px;
            height: 33px;
            background-color: #054176;
            display: flex;
            align-items: center;
            justify-content: center;

            .MuiSvgIcon-root{
                font-size: 22px;
            }
        }
    }
}

.customized-product{
    .MuiMenu-paper{
        margin-top: 5px;
        border: none;
    }
    .MuiPopover-paper{
        overflow: initial;
    }
     .MuiMenu-list{
        &::after {
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-bottom: 4px solid #f7931e;
        position: absolute;
        bottom: 30px;
        right: 20%;
        display: none;
        content: "";
        top: -4px;
        @media screen and (max-width:767px) {
            right:9%;
        }
      }
    }
    .MuiMenuItem-root{
        min-height: auto;
        height: 42px;
        border: 1px solid #FF8900;
        background: #fff;
        min-width: 0;
        box-shadow: 0px 3px 6px #00000029;
        border-radius: 8px;

        &:focus{
            min-height: auto;
            height: 42px;
            border: 1px solid #FF8900;
            background: #fff;
            min-width: 0;
            border-radius: 8px;
            box-shadow: 0px 3px 6px #00000029;
        }
        &.menu-file-outline {
            @media screen and (max-width:767px) {

                height: 36px;
            }
        }

        &.menu-file-top {
            
                border-bottom-left-radius: 0;
                border-bottom-right-radius: 0;
                border-bottom: none;
            
        }

        &.menu-file-bottom {
            
                border-top-left-radius: 0;
                border-top-right-radius: 0;
            
        }
    }
    .MuiListItemIcon-root {
        min-width: 26px;

        .MuiSvgIcon-root{
            font-size: 16px;
            color: #083654;
        }
    }
    .MuiMenu-list{
        padding: 0;
    }
    .MuiMenuItem-root{
        &:focus {
            .MuiListItemText-primary{
                color: #083654;
                font-size: 13px;
                font-weight: 500;
            }
        }
    }
    .MuiListItemText-primary{
        color: #083654;
        font-size: 13px;
        font-weight: 500;
    }
}

.file-upload {
    .btn-orange{
        padding: 10px 4px;
        background: #eaeff3;
        height: 30px;

        .MuiSvgIcon-root{
            font-size: 18px !important;
            color: #083654 !important;
        }
    }
}