.upload_wrap{
    position: relative;
    label{
        font-size: 14px;
        line-height: 1.15;
        margin-bottom: 5px;
        color: #133751;
        .mandatoryFlied{
            color: red;
            margin-left: 4px;
        }
    }
    .inputFileWrap{
        border-radius: 3px;
        padding: 0px 10px;
        border: solid 2px #E0E4E6;
        background-color: #fff;
        display: flex;
        align-items: center;
        min-height: 44px;
        .input_tooltip_wrap{
            position: relative;
            :global .tool-tip-icon{
                position: absolute;
                z-index: 1;
                width: 128px;
                height: 40px;
                margin-top: 4px;
                opacity: 0;
            }
        }
        label{
            margin-right: 8px;
            color: #2B2B2B;
            font-size: 12px;
        }
        /*custom input file*/
        .inputFileBox{
            position: relative;
            display: flex;
            align-items: center;
        .upIcon{
            position: absolute;
            left: 8px;
            color: #282828;
            font-size: 20px;
        }
        .inputFile{
            color: transparent;
            max-width: 160px;
            &::-webkit-file-upload-button {
                visibility: hidden;
            }
            &::before {
                content: "UPLOAD";
                color: #282828;
                display: inline-block;
                background: #FAFAFA;
                padding: 6px 22px;
                padding-left: 36px;
                outline: none;
                white-space: nowrap;
                // -webkit-user-select: none;
                cursor: pointer;
                font-weight: 400;
                border-radius: 2px;
                outline: none;
                border: solid 1px #E0E4E6;
            }
            &:focus {
                outline: none !important;
            
            }
            &:active::before {
                transform: scale(.9) translate(0px, 2px);
            box-shadow:  inset 4px 4px 5px 0px rgba(0, 0, 0, 0.20);
            }
        }
    }
        .imgWrap{
            position: relative;
            margin-right: 20px;
            .closeIcon{
                position: absolute;
                color: #fff;
                line-height: normal;
                border-radius: 50%;
                background: #C5C6C7 0% 0% no-repeat padding-box;
                border: 2px solid #FFFFFF;
                display: flex;
                align-items: center;
                justify-content: center;
                right: -12px;
                top: -8px;
                :global .MuiSvgIcon-root{
                    width: 16px;
                    height: 16px;
                }
            }
        }
        .checkIcon{
        color: #52A83E;
        margin-left: auto;      
        }
    }
}