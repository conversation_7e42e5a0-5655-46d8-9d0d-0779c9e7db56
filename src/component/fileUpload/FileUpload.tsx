import React, { useEffect, useState } from 'react';
import styles from './FileUpload.module.scss'
import { CancelOutlined, Check, PublishOutlined } from '@mui/icons-material'
import { CircularProgress, IconButton } from '@mui/material'
import { showAlert } from '../../../redux/actions/AppActions'
import { isNullValue, isNullValueOrZero, isObjectEmpty } from '../../../base/utility/StringUtils'
import { getDeleteDocumentParams, getUploadDocumentsParams } from '../../../pages/enrouteBreakDownModule/utility'
import { deleteEnrouteDocuments, uploadEnrouteDocuments } from '../../../base/api/serviceActions/EnrouteBreakdownServiceActions'
import { InfoTooltip } from '../tooltip/InfoTooltip';
import { uploadDocumentMapperEnum, maxDocsToUploadEnum } from '../../../base/constant/ArrayList';
import { isEnrouteBreakdownRequest } from '../../../pages/CreateRequestModule/utility';
import { uploadReactiveRepairDocuments, deleteReactiveRepairDocuments } from '../../../base/api/serviceActions/ReactiveRepairServiceAction';

interface FileUploadProps {
    label?:string,
    styleName?:string,
    height?:string,
    completeWorkData: any,
    appDispatch?: Function,
    uploadDocuments?: any,
    setUploadDocuments?: Function,
    totalDocs: number,
    setTotalDocs: Function,
    totalSize: number,
    setTotalSize: Function,
    uploadLoading: boolean,
    setUploadLoading: Function,
    createRequestLoading: boolean,
    allowedDocsCount: number,
}

function FileUpload(props:FileUploadProps) {
  const {
            label, styleName, height, completeWorkData,
            appDispatch, uploadDocuments, setUploadDocuments, 
            totalDocs, setTotalDocs, totalSize, setTotalSize, 
            uploadLoading, setUploadLoading, createRequestLoading, allowedDocsCount
        } = props;
  const [showTooltip, setShowTooltip] = useState<boolean>(false);
  const [completeUpload, setCompleteUpload] = useState<boolean>(false);

  useEffect(()=>{
    if(totalDocs && totalDocs >= allowedDocsCount){
        setShowTooltip(true);
    }else{
        setShowTooltip(false);
    }
  },[totalDocs, allowedDocsCount])

  return (
    <div className={`${styles.upload_wrap} ${styleName ? styleName : ''}`}>
        <label>{label} <span className={styles.mandatoryFlied}>*</span></label>
        <div className={styles.inputFileWrap}>
            <div className={styles.input_tooltip_wrap}>
                {showTooltip && <InfoTooltip
                    arrow={true}
                    style={{
                        tooltip:{
                            maxWidth: '100px'
                        },
                        arrow:{
                            fontSize: '16px',
                            color: '#fff',
                            top:'27px'
                        },
                    }}
                    placement="top" 
                    title={completeWorkData?.walletCode?.toUpperCase() === 'OPS_WALLET' 
                            ? `You can upload maximum ${maxDocsToUploadEnum.OPS_WALLET} files.` 
                            : `You can upload maximum ${maxDocsToUploadEnum.WALLET} files.`} />
                }
                <div className={styles.inputFileBox} style={{height:`${height}`}}>
                    <PublishOutlined className={styles.upIcon} /> 
                    <input 
                        type='file'
                        multiple 
                        accept="image/jpg,image/jpeg,application/pdf,image/png"
                        className={styles.inputFile} 
                        onChange={(uploadEvent: any)=>{
                            if (uploadEvent && uploadEvent.target && uploadEvent.target.files && uploadEvent.target.files.length!==0) {
                                var tempTotalDoc = uploadEvent.target.files.length;
                                if(totalDocs < allowedDocsCount && ((tempTotalDoc+totalDocs) <= allowedDocsCount)){
                                    var tempFileSize = 0;
                                    for(const file of uploadEvent.target.files){
                                        tempFileSize += Number(file.size);
                                    }
                                    if (tempFileSize <= 10*1024*1024 && ((totalSize+tempFileSize)<=10*1024*1024)) {
                                        setUploadLoading(true);
                                        setCompleteUpload(false);
                                        let queryParams = getUploadDocumentsParams(uploadEvent.target.files, completeWorkData.id, completeWorkData.repairType);
                                        let uploadRequestDocuments = isEnrouteBreakdownRequest(completeWorkData.repairType) ? uploadEnrouteDocuments : uploadReactiveRepairDocuments;
                                        appDispatch && appDispatch(uploadRequestDocuments(queryParams)).then((resp: any)=>{
                                            let tempDocs: any = !isObjectEmpty(uploadDocuments) ? {...uploadDocuments} : {};
                                            const documentType = uploadDocumentMapperEnum[completeWorkData.repairType as keyof typeof uploadDocumentMapperEnum];
                                            if(resp){
                                                setTimeout(()=>{
                                                    tempDocs = !isNullValueOrZero(uploadDocuments) ?
                                                    (
                                                        [...uploadDocuments, ...resp[documentType]]
                                                    ) : (
                                                        [...resp[documentType]]
                                                    );
                                                    setUploadDocuments && setUploadDocuments(tempDocs);
                                                    setUploadLoading(false);
                                                    setCompleteUpload(true);
                                                },5000)
                                                setTotalDocs(tempTotalDoc+totalDocs);
                                                setTotalSize(totalSize+tempFileSize);

                                            }else{
                                                setUploadLoading(false);
                                                setCompleteUpload(true);
                                            }
                                        })
                                    }else{
                                        appDispatch && appDispatch(showAlert("Total file size of 10 MB is allowed", "true"));
                                    }
                                }else{
                                    appDispatch && appDispatch(showAlert(`Maximum ${allowedDocsCount} files can be uploaded`, "true"));
                                }
                            }
                            uploadEvent.target.value="";
                        }}
                        disabled={uploadLoading || showTooltip || createRequestLoading }
                    />
                </div>
            </div>
            <>
                {
                    !isNullValue(uploadDocuments) && uploadDocuments.length!==0 && uploadDocuments.map((elementDoc: any, index: any)=>{
                        return(
                            <div className={styles.imgWrap} key={index}>
                                <span className={styles.closeIcon}>
                                    { (!createRequestLoading && !uploadLoading) && <IconButton
                                        className='p-0 bg-white'
                                        onClick={()=>{
                                            //remove document
                                            let queryParams = getDeleteDocumentParams(elementDoc, completeWorkData.id, completeWorkData.repairType);
                                            setUploadLoading(true);
                                            setCompleteUpload(false);
                                            const deleteRequestDocuments = isEnrouteBreakdownRequest(completeWorkData.repairType) ? deleteEnrouteDocuments : deleteReactiveRepairDocuments;
                                            appDispatch && appDispatch(deleteRequestDocuments(queryParams)).then((response: any)=>{
                                                if(response && response.code===200){
                                                    let tempDocs = [...uploadDocuments]
                                                    tempDocs = tempDocs.filter((item: any)=>{
                                                        return item.uuid!==elementDoc.uuid;
                                                    })
                                                    setUploadDocuments && setUploadDocuments(tempDocs);
                                                    setTimeout(()=>{
                                                        setUploadLoading(false);
                                                        setCompleteUpload(true);
                                                    },5000);
                                                    const tempDocLen = totalDocs-1;
                                                    setTotalDocs(tempDocLen);
                                                    const tempDocSize = totalSize - Number(elementDoc?.documentSize);
                                                    setTotalSize(tempDocSize);
                                                }else{
                                                    appDispatch(showAlert("Unable to delete documents", "true"));
                                                    setUploadLoading(false);
                                                    setCompleteUpload(true);
                                                }
                                            })
                                        }}
                                    >
                                        <CancelOutlined/>
                                    </IconButton>
                                    }  
                                </span>
                                <img src='/images/image-icon.svg'className={styles.upImg} />
                            </div>
                        )
                    })
                }
            </>
            {completeUpload ? <Check className={styles.checkIcon}/>  : (uploadLoading ?  <CircularProgress className={styles.checkIcon} size={24} /> : <></> )}
        </div>
    </div>
  )
}

export default FileUpload