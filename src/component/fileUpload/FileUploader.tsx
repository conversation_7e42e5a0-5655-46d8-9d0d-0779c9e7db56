import React, { CSSProperties, ChangeEvent, useState } from 'react'
import styles from './FileUpload.module.scss'
import { Dispatch } from 'redux';
import { showAlert } from '../../redux/actions/AppActions';
import { InfoTooltip } from '../widgets/tooltip/InfoTooltip';
import { CancelOutlined, Check, PublishOutlined } from '@material-ui/icons';
import { isNullValue } from '../../base/utility/StringUtils';
import { CircularProgress, IconButton } from '@material-ui/core';

interface FileUploaderProps {
    label: string;
    styleName?: string;
    height?: string;
    appDispatch?: Dispatch<any>,
    uploadedDocuments?: Array<any>,
    onFilesUpload: (files: File[], filesSize: number) => Promise<{ success: boolean, data: any }>;
    onFileDeleted: (file: any) => Promise<{ success: boolean, data: any }>;
    maxAllowedFilesCount: number;
    maxAllowedFilesSize?: number;
    uploadTooltipTitle?: string;
    uploadLoading: boolean;
    totalSize: number;
    error?: string;
}

const FileUploader = (props: FileUploaderProps) => {
    const {
        label,
        styleName,
        height,
        appDispatch,
        uploadedDocuments,
        onFilesUpload,
        onFileDeleted,
        maxAllowedFilesCount,
        maxAllowedFilesSize = 10 * 1024 * 1024, //max files size is 10 MB
        uploadTooltipTitle,
        uploadLoading,
        totalSize,
        error = ""
    } = props;

    const [isUploadCompleted, setUploadCompleted] = useState<boolean>(false);

    const handleFilesChange = (event: ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (!files) return;

        const filesCount = files?.length || 0;
        if (filesCount > 0 && (filesCount + (uploadedDocuments?.length || 0)) <= maxAllowedFilesCount) {
            let tempFileSize = 0;
            for (let i = 0; i < filesCount; i++) {
                tempFileSize += Number(files[i].size);
            }

            if (tempFileSize <= maxAllowedFilesSize && (totalSize + tempFileSize) <= maxAllowedFilesSize) {
                setUploadCompleted(false);
                onFilesUpload && onFilesUpload(Array.from(files), tempFileSize).then(result => {
                    setUploadCompleted(true);
                }).catch(_error => {
                    setUploadCompleted(true);
                });
            } else {
                appDispatch && appDispatch(showAlert(`Total file size of ${maxAllowedFilesSize / (1024 * 1024)} MB is allowed`));
            }
        } else {
            appDispatch && appDispatch(showAlert(`Maximum ${maxAllowedFilesCount} files can be uploaded`));
        }
    }

    const handleFileDelete = (file: any) => {
        //remove document
        setUploadCompleted(false);
        onFileDeleted && onFileDeleted(file).then(result => {
            if (!result.success) {
                appDispatch && appDispatch(showAlert("Unable to delete documents"));
            }
            setUploadCompleted(true);
        }).catch(error => {
            setUploadCompleted(true);
        });
    }

    return (
        <div className={`${styles.upload_wrap} ${styleName ? styleName : ''}`}>
            <label>{label} <span className={styles.mandatoryFlied}>*</span></label>
            <div className={styles.inputFileWrap}>
                <div className={styles.input_tooltip_wrap}>
                    {uploadTooltipTitle && <InfoTooltip
                        arrow={true}
                        style={{
                            tooltip: {
                                maxWidth: '100px'
                            },
                            arrow: {
                                fontSize: '16px',
                                color: '#fff',
                                top: '27px'
                            },
                        }}
                        placement="top"
                        title={uploadTooltipTitle}
                    />
                    }
                    <div className={styles.inputFileBox} style={{ height: `${height}` }}>
                        <PublishOutlined className={styles.upIcon} />
                        <input
                            type='file'
                            multiple
                            accept="image/jpg,image/jpeg,application/pdf,image/png"
                            className={styles.inputFile}
                            onChange={(event: ChangeEvent<HTMLInputElement>) => {
                                handleFilesChange(event);
                                event.target.value = "";
                            }}
                            disabled={uploadLoading || !isNullValue(uploadTooltipTitle)}
                        />
                    </div>
                </div>
                <>
                    {
                        !isNullValue(uploadedDocuments) && uploadedDocuments?.length !== 0 && uploadedDocuments?.map((elementDoc: any, _index: any) => {
                            return (
                                <div className={styles.imgWrap} key={elementDoc?.uuid}>
                                    <span className={styles.closeIcon}>
                                        {(!uploadLoading) && <IconButton
                                            className='p-0 bg-white'
                                            onClick={() => handleFileDelete(elementDoc)}
                                        >
                                            <CancelOutlined />
                                        </IconButton>
                                        }
                                    </span>
                                    <img src='/images/image-icon.svg' alt='img' className={styles.upImg} />
                                </div>
                            )
                        })
                    }
                </>
                {isUploadCompleted ? <Check className={styles.checkIcon} /> : (uploadLoading ? <CircularProgress className={styles.checkIcon} size={24} /> : <></>)}
            </div>
            <label className='error'>{error}</label>
        </div>
    )
}

export default FileUploader