.filter-panel {
  background-color: #ffffff;
  position: sticky;
  top: 0px;
  left: 0;
  right: 0;
  z-index: 9;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.16);
  padding: 8px 24px;
  margin-top: 2px;
  @media screen and (max-width: 767px) {
    margin: 0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.08);
    padding: 6px 16px;
  }

  .btn {
    margin-left: 10px;
    height: 40px;
    font-weight: normal;
    @media screen and (max-width: 767px) {
      height: 32px;
      margin-left: 5px;
      font-size: 13px;
    }
  }

  .btn-detail {
    color: #768a9e;
    &:hover {
      color: #ffffff;
    }
  }

  .MuiToolbar-root {
    min-height: 40px;
    max-height: 65px;
    padding: 0;
  }

  .MuiTypography-root {
    font-size: 18px;
    font-weight: normal;
    line-height: 1.17;
    color: #133751;
  }
  @media screen and (max-width: 767px) {
    .icon-space {
      svg {
        margin-right: 0;
      }
    }
  }
}

.icon-list .MuiSvgIcon-root {
  margin-right: 0 !important;
}

.filter-panel .icon-list {
  width: 50px;
}

.filter-panel .MuiTypography-root {
  font-size: 16px;
  font-weight: normal;
  line-height: 1.17;
  color: #083654;
}

.legacy-heading {
  align-items: center;
  .legacy-currency {
    width: 48px;
    height: 48px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #1fc900;
    border-radius: 50px;
    background-color: rgba(31, 201, 0, 0.2);
    margin-right: 18px;
    @media screen and (max-width: 767px) {
      width: 30px;
      height: 30px;
    }
  }

  span {
    color: #1fc900;
    opacity: 1;
    display: block;
    font-size: 20px;
    @media screen and (max-width: 767px) {
      font-size: 14px;
    }
  }

  .legacy-name {
    color: #768a9e;
    font-size: 14px;
    @media screen and (max-width: 767px) {
      font-size: 10px;
    }
  }

  .legacy-price {
    color: #323232;
    font-size: 24px;
    font-weight: 700;
    @media screen and (max-width: 767px) {
      font-size: 14px;
    }
  }
}

@media (max-width: 767px) {
  .filter-panel {
    margin-bottom: 0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.08);
    padding: 2px 10px;

    .mob-btn-blue,
    .btn-detail-mob {
      height: 34px;
      padding: 5px 12px;
      min-width: auto;
      background: #f9f9f9;
      background-image: linear-gradient(180deg, #f9f9f9, #eaeff3);

      .MuiSvgIcon-root {
        margin-right: 0;
        font-size: 18px;
        color: #768a9e;
      }
    }

    .MuiTypography-h6 {
      font-size: 14px;
      color: #083654;
      font-weight: 500;
    }
  }
}
