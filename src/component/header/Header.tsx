import AppBar from "@material-ui/core/AppBar";
import Box from "@material-ui/core/Box";
import Toolbar from "@material-ui/core/Toolbar";
import { AddCircle } from "@material-ui/icons";
import React, { useEffect } from "react";
import { useDispatch, shallowEqual, useSelector } from "react-redux";
import { useHistory, useLocation } from "react-router-dom";
import { createRequestRoute, DASHBOARD, createAdhoceRequestRoute, requestListingRoute, payablesListingRoute } from "../../base/constant/RoutePath";
import { isMobile } from "../../base/utility/ViewUtils";
import { setHeaderMenu, setCurrentPaymentType } from "../../redux/actions/AppActions";
import { getUserProfileData } from "../../serviceActions/AppServiceActions";
import HeaderMenu from "../headerMenu/HeaderMenu";
import Button from "../widgets/button/Button";
import "./Header.css";
import ProfileLogo from "./profileLogo/profileLogo";
import CompareArrowsIcon from '@material-ui/icons/CompareArrows';


const Header = () => {
  const history = useHistory()
  const location = useLocation();
  const appDispatch = useDispatch();
  const currentPaymentType = useSelector((state: any) => state.appReducer.currentPaymentType, shallowEqual);

  useEffect(() => {
    const getUserInfo = async () => {
      // appDispatch(showLoader())
      appDispatch(getUserProfileData())
    }
    getUserInfo();
    // eslint-disable-next-line
  }, [])

  useEffect(() => {
    if (location.pathname.startsWith("/adhoc")) {
      if (currentPaymentType === "TRIP_PAYMENTS") {
        appDispatch(setCurrentPaymentType("ADHOC_PAYMENTS"));
      }
      if (location.pathname.startsWith("/adhoc/contacts")) {
        appDispatch(setHeaderMenu("Contacts"));
      } else if (location.pathname.startsWith("/adhoc/payables")) {
        appDispatch(setHeaderMenu("Payables"));
      }
    } else if (location.pathname.startsWith("/request")) {
      if (currentPaymentType === "ADHOC_PAYMENTS") {
        appDispatch(setCurrentPaymentType("TRIP_PAYMENTS"));
      }
    }
    // appDispatch(setHeaderMenu(currentPaymentType === "ADHOC_PAYMENTS" ? "Payables" : "View Request"));
  }, [appDispatch, currentPaymentType, location.pathname]);

  return (
    <>
      <header className="header">
        <AppBar position="sticky">
          <Toolbar className="d-flex justify-content-between">
            <div className="d-flex align-items-center">
              <Box
               component="div"
              >
              {
                <div
                  className="logo"
                  onClick={() => {
                    appDispatch(setCurrentPaymentType("TRIP_PAYMENTS"));
                    history.push({
                      pathname: DASHBOARD,
                    })
                  }}
                >
                  <img
                    src={'/images/logo.svg'}
                    alt="logo" />
                </div>
              }
            </Box>
            <div>
              <div className="d-flex">
                {!isMobile && <div className='border-line' />}
                {!isMobile && <HeaderMenu />}
              </div>
            </div>
          </div>
          <div className="d-flex align-items-center">
            <div>
              <Button
                buttonContainerStyle={'mr-2'}
                buttonStyle={isMobile ? "mobile-create-btn" : "btn-orange"}
                title={isMobile ? "" : currentPaymentType === "TRIP_PAYMENTS" ? "Adhoc Payments" : "Trip Payments"}
                leftIcon={<CompareArrowsIcon />}
                onClick={() => {
                  appDispatch(setCurrentPaymentType(currentPaymentType === "ADHOC_PAYMENTS" ? "TRIP_PAYMENTS" : "ADHOC_PAYMENTS"));
                  history.push({
                    pathname: currentPaymentType === "ADHOC_PAYMENTS" ? requestListingRoute : payablesListingRoute,
                  });
                  // appDispatch(setHeaderMenu(""));
                }}
              />
                {currentPaymentType === "TRIP_PAYMENTS" ? (
                  <Button
                    buttonStyle={isMobile ? "mobile-create-btn" : "btn-white"}
                    title={isMobile ? "" : "Create Trip Request"}
                    leftIcon={isMobile ? <img src="/images/Add.png" alt="Enable " /> : <AddCircle />}
                    onClick={() => {
                      history.push({
                        pathname: createRequestRoute,
                      });
                      appDispatch(setHeaderMenu(""));
                    }}
                  />
                ) : (
                    <Button
                      buttonStyle={isMobile ? "mobile-create-btn" : "btn-white"}
                      title={isMobile ? "" : "Create Adhoc Request"}
                      leftIcon={isMobile ? <img src="/images/Add.png" alt="Enable " /> : <AddCircle />}
                      onClick={() => {
                        history.push({
                          pathname: createAdhoceRequestRoute,
                        });
                        appDispatch(setHeaderMenu(""));
                      }}
                    />
                  )}
            </div>
            <div className="profile-login-wrap">
              <ProfileLogo
                styleName=""
              />
            </div>
          </div>
        </Toolbar>
      </AppBar>
    </header>
      <div className="header-menu-mob">
        {isMobile && <HeaderMenu />}
      </div>
    </>
  );
}
export default Header;
