import Badge from '@material-ui/core/Badge';
import IconButton from '@material-ui/core/IconButton';
import Menu from '@material-ui/core/Menu';
import MenuItem from '@material-ui/core/MenuItem';
import NotificationsIcon from '@material-ui/icons/Notifications';
import React from 'react';
import "./notification.css";

export default function Notification() {

    // Dropdown Menu
    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    return (
        <div className="notification">
            <IconButton aria-label="show notifications" color="inherit"
                onClick={handleClick}
            >
                <Badge badgeContent={0} aria-controls="simple-menu" aria-haspopup="true" >
                    <NotificationsIcon />
                </Badge>
            </IconButton>
            <Menu
                // id="simple-menu"
                anchorEl={anchorEl}
                keepMounted
                open={open}
                variant="selectedMenu"
                onClose={handleClose}
                className="notification-menu"
            >
                <div className="notification-header">{0} New notifications</div>
                <div className="notification-list">
                    {[].map((item: any, index: number) => {
                        return (
                            <MenuItem
                                key={index}
                                className={item.unread ? "un-read" : ""}
                                onClick={() => {
                                    handleClose();
                                }}>
                                {item.unread && <span className="un-read-icon"></span>}
                                <div className="menu-icon">
                                    <img src={item.channel} alt={item.channel} />
                                </div>
                                <div className="menu-content">
                                    <h6 className="menu-heading">{item.channel}</h6>
                                    <p>{item.message}</p>
                                    <span>{item.createdAt}</span>
                                </div>
                            </MenuItem>


                        )

                    })}
                </div>
                {/* <a className="view-btn" href={NotificationUrl}>View All</a> */}
                <div className="view-btn" onClick={() => {
                    handleClose();
                    // history.push()
                }}>View All</div>

            </Menu>
        </div>
    )
}