.profile-login{
    width: 40px;
    height: 40px;
    padding: 0px 15px;
    /* max-width: 210px; */
    display: flex;
    align-items: center;
    border-radius: 50%;
    border-radius: 26px;
    background-color: #ffffff;
    justify-content: center;
}
.profile-login .client-logo{
    margin-right: 15px;
}
.profile-login .client-logo img{
    max-width: 260px;
    width: auto;
    max-height: 47px;
    display: block;
}
.profile-login .profile-btn{
    /* max-width: 200px; */
    padding: 0;
    background: transparent;
    box-shadow: none;
    font-size: 13px;
    font-weight: 500;
    line-height: 1.57;
    color: #606e7b;
    text-transform: capitalize;
}
.profile-login .profile-btn .MuiButton-label .MuiSvgIcon-root{
    color: #768a9e;
}
.profile-login .profile-btn .MuiButton-label  .profile-icon{
    color: #acb6c0;
    font-size: 30px;
}
.profile-login .profile-btn .MuiButton-label span{
    padding: 0 6px;
}
.profile-login .profile-btn:hover{
    background: transparent;
    box-shadow: none;
}

.customized-menu .MuiMenu-list{
    padding: 0;
}
.customized-menu .MuiMenuItem-root{
    min-height: 48px;
    padding: 0 16px 15px;
    display: block;
    text-align: center;
}
.customized-menu .MuiMenuItem-root:focus, .customized-menu .MuiListItem-button:hover{
    background:transparent;
}
.customized-menu .MuiMenuItem-root:not(:last-child){
    padding-top: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #E4E4E4;
}
.customized-menu .MuiMenu-paper{
    top: 64px !important;
    left: auto!important;
    right: 22px !important;
    border-radius: 4px;
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.3);
    min-width: 218px;
    border: 0 none;
    overflow: inherit;
}
.user-info-icon {
    width: 48px;
    height: 48px;
    background: #f9f9f9;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 30px;
    margin: 0 auto;
}
.customized-menu .MuiListItemIcon-root,
.customized-menu .MuiMenuItem-root:focus .MuiListItemIcon-root{
    min-width: 35px;
    color: #768A9E;
}
.user-info-icon .MuiSvgIcon-root{
    font-size: 32px;
    color: #acb6c0;
}
.customized-menu .MuiMenuItem-root .MuiTypography-root,
.customized-menu .MuiMenuItem-root:focus .MuiListItemText-primary{
    font-size: 14px;
    font-weight: normal;
    line-height: 1.71;
    letter-spacing: 0.28px;
    color: #2B2B2B;
}
.user-name{
    margin: 10px 0 3px;
}
.customized-menu .MuiMenuItem-root .user-name .MuiTypography-root {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.14;
    letter-spacing: 0.7px;
    color: #133751;
}
.customized-menu .MuiMenuItem-root .user-email .MuiTypography-root{
    font-size: 13px;
    line-height: 1.15;
    letter-spacing: 0.65px;
    color: #768a9e;
}
.MuiButton-contained.logout-btn, .MuiButton-contained.logout-btn:hover,
.MuiButton-contained.logout-btn:focus{ 
    margin-top: 13px;
    height: 35px;
    border-radius: 4px;
    border: solid 1px #eaeff3;
    background-color: #ffffff;
    box-shadow: none;
}
.MuiButton-contained.logout-btn .MuiButton-label{
    font-size: 12px;
    font-weight: 500;
    line-height: 2.08;
    color: #768a9e;
    text-transform: capitalize;
}
.MuiButton-contained.logout-btn .MuiSvgIcon-root{
    margin-right: 10px;
    font-size: 19px;
    line-height: 1.11;
}
/* .customized-menu .MuiMenu-paper::before{
    content: "";
    position: absolute;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
    top: -7px;
    z-index: 99999;
    left: 50%;
    transform: translateX(-50%);
} */

@media screen and (max-width:767px) {
    .profile-login{
       width: 36px;
       height: 36px;
    }
    .customized-menu .MuiMenu-paper{
        right: 0 !important;
        left: auto !important;
        top: 49px !important
    }
    .customized-menu .MuiMenuItem-root {
        padding: 4px 12px;
    }
    .customized-menu .mob-client-logo{
        flex-direction: column;
        align-items: flex-start;
    }
    .customized-menu .mob-client-logo span{
        font-size: 8px;
        font-weight: 300;
        color: #7f7f7f;
        margin-bottom: 1px;
    }
    .customized-menu .MuiMenuItem-root .MuiListItemText-root{
        margin: 0;
    }
    .customized-menu .mob-client-logo img{max-width: 60px;}
    .partner-logo{
        max-width: 150px;
        max-height: 50px;
        width: auto;
        margin-right: 5px !important;
        border-radius: 4px;
        background: #fff;
        padding: 5px;
    }
    .profile-login .profile-btn{
        font-size: 11px;
    }
    .profile-login .profile-btn .MuiButton-label .MuiSvgIcon-root{font-size: 16px;}
    .userName{
        font-size: 11px;
        margin: auto 0;
    }
    .userName .MuiSvgIcon-root{
        color: #777777;
    }
    .customized-menu .MuiMenuItem-root .MuiTypography-root,
    .customized-menu .MuiMenuItem-root:focus .MuiListItemText-primary{font-size: 12px;}
    .MuiButton-contained.logout-btn {
        margin-top: 4px;
    }
    
}
@media screen and (max-width:320px) {
    .partner-logo{
        max-width: 100px;
        max-height: 50px;
    }
    /* .profile-login .profile-btn{
        max-width: 100px;
    }     */
}