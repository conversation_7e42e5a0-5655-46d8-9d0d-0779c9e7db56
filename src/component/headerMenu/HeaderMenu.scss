.square-btn {
  background-color: #264e69;
  color: #fff;
  border-radius: 0;
  height: 56px;
  margin-left: 6px;
  &:hover {
    color: #fff;
  }
  span {
    font-size: 18px;
  }
  @media screen and (max-width: 767px) {
    margin-left: 0;
  }
}
.header-menu-mob {
  .header-item {
    box-shadow: 0px 1px 1px #00000029;
    li {
      @media screen and (max-width: 767px) {
        width: 32%;
      }

      button {
        width: 100%;
      }
    }
  }
  .btn {
    padding: 8px 17px;
    @media screen and (max-width: 767px) {
        padding: 8px;
      }
  }
  .square-btn {
    background-color: rgba(#083654, 0.07);
    color: #1b3e57;
    height: 45px;
  }
  .square-btn-active {
    background-color: #f7791e;
    color: #fff;
  }
}
.square-btn-active {
  background-color: #f7791e;
}
.btn-white {
  svg {
    color: #ff8900;
  }
}
.border-line {
  border: 1px solid #224a65;
  height: 29px;
  top: 14px;
  position: relative;
  margin-left: 27px;
  margin-right: 18px;
}

.square-border svg{
  border: 2px solid white ;
  border-radius: 2px;
}

@media screen and (max-width: 767px) {
  .header-menu-mob {
    margin-top: 48px;
  }
  .square-btn span {
    font-size: 12px;
  }
}
@media (min-width: 768px) and (max-width: 820px) {
  .square-btn span {
    font-size: 11px;
  }
}
