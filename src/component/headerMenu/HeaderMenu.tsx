import { AccountBalanceWalletRounded, Dashboard, FormatListBulleted, SwapVert, PersonOutline } from "@material-ui/icons";
import React from "react";
import { shallowEqual, useDispatch, useSelector } from "react-redux";
import { useHistory } from "react-router-dom";
import { headerMenuButtons, adhocMenuButtons } from "../../base/constant/ArrayList";
import * as Path from '../../base/constant/RoutePath';
import { cardMappingRoute, requestListingRoute, walletListingRoute } from "../../base/constant/RoutePath";
import { getDateFilters, getSearchDateFilter } from "../../base/utility/Routerutils";
import { setHeaderMenu } from "../../redux/actions/AppActions";
import Button from "../widgets/button/Button";
import './HeaderMenu.scss';


const HeaderMenu = () => {
    const history = useHistory();
    const appDispatch = useDispatch();
    //const [activeButton, setActiveButton] = React.useState<any>(headerMenuButtons[0]);
    const activeHeaderMenu = useSelector((state: any) => state.appReducer.headerMenu, shallowEqual);
    const currentPaymentType = useSelector((state: any) => state.appReducer.currentPaymentType, shallowEqual);

    return (
        <div>
            <ul className="header-item d-flex justify-content-between">
                {currentPaymentType === "TRIP_PAYMENTS" ? (
                    <>
                        <li>
                            <Button
                                buttonStyle={activeHeaderMenu === headerMenuButtons[0] ? "square-btn square-btn-active" : "square-btn"}
                                title={"Dashboard"}
                                leftIcon={<Dashboard />}
                                onClick={() => {
                                    history.push({
                                        pathname: Path.DASHBOARD,
                                        search: new URLSearchParams(getSearchDateFilter(getDateFilters())).toString()
                                    });
                                    appDispatch(setHeaderMenu(headerMenuButtons[0]));
                                }}
                            />
                        </li>
                        <li>
                            <Button
                                buttonStyle={activeHeaderMenu === headerMenuButtons[1] ? "square-btn square-btn-active" : "square-btn"}
                                title={"View Request"}
                                leftIcon={<FormatListBulleted />}
                                onClick={() => {
                                    history.push({
                                        pathname: requestListingRoute
                                    })
                                    appDispatch(setHeaderMenu(headerMenuButtons[1]));
                                }}
                            />
                        </li>
                        <li>
                            <Button
                                buttonStyle={activeHeaderMenu === headerMenuButtons[2] ? "square-btn square-btn-active" : "square-btn"}
                                title={"Wallet"}
                                leftIcon={<AccountBalanceWalletRounded />}
                                onClick={() => {
                                    history.push({
                                        pathname: walletListingRoute
                                    })
                                    appDispatch(setHeaderMenu(headerMenuButtons[2]));
                                }}
                            />
                        </li>
                        <li>
                            <Button
                                buttonStyle={activeHeaderMenu === headerMenuButtons[3] ? "square-btn square-btn-active square-border" : "square-btn square-border"}
                                title={"Card Mapping"}
                                leftIcon={<SwapVert />}
                                onClick={() => {
                                    history.push({
                                        pathname: cardMappingRoute
                                    })
                                    appDispatch(setHeaderMenu(headerMenuButtons[3]));
                                }}
                            />
                        </li>
                    </>
                ) : (
                        <>
                            <li>
                                <Button
                                    buttonStyle={activeHeaderMenu === adhocMenuButtons[0] ? "square-btn square-btn-active" : "square-btn"}
                                    title={"Contacts"}
                                    leftIcon={<PersonOutline />}
                                    onClick={() => {
                                        history.push({
                                            pathname: Path.contactsListingRoute,
                                            // search: new URLSearchParams(getSearchDateFilter(getDateFilters())).toString() // TODO
                                        });
                                        appDispatch(setHeaderMenu(adhocMenuButtons[0]));
                                    }}
                                />
                            </li>
                            <li>
                                <Button
                                    buttonStyle={activeHeaderMenu === adhocMenuButtons[1] ? "square-btn square-btn-active" : "square-btn"}
                                    title={"Payables"}
                                    leftIcon={<FormatListBulleted />}
                                    onClick={() => {
                                        history.push({
                                            pathname: Path.payablesListingRoute,
                                            // search: new URLSearchParams(getSearchDateFilter(getDateFilters())).toString() //TODO
                                        });
                                        appDispatch(setHeaderMenu(adhocMenuButtons[1]));
                                    }}
                                />
                            </li>
                            <li>
                                <Button
                                    buttonStyle={activeHeaderMenu === adhocMenuButtons[2] ? "square-btn square-btn-active" : "square-btn"}
                                    title={"Logs"}
                                    leftIcon={<img src="/images/Logs.svg" alt="Logs" width={18} />}
                                    onClick={() => {
                                        history.push({
                                            pathname: Path.adhocLogsRoute,
                                        });
                                        appDispatch(setHeaderMenu(adhocMenuButtons[2]));
                                    }}
                                />
                            </li>
                        </>
                    )}

            </ul>
        </div>
    )
}

export default HeaderMenu;