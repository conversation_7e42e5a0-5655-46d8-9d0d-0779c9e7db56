/* payment-info-profle */
.info-heading {
	margin-bottom: 5px;
	font-size: 14px;
	font-weight: 400;
	line-height: 1.14;
	letter-spacing: 0.28px;
	color: #768a9e;
	display: block;
}
.vehicle-info {
	align-items: center;
	font-size: 16px;
	font-weight: normal;
	line-height: 1.2;
	letter-spacing: 0.3px;
	color: #083654;
	.MuiSvgIcon-root {
		margin-right: 10px;
		color: #768a9e;
		display: inline-flex;
		min-width: 24px;
		flex-shrink: 0;
	}
	.blue-icon {
		.MuiSvgIcon-root {
			margin: 0;
			color: #006CC9;
			font-size: 14px;
			cursor: pointer;
		}
	}
}
.media
	.media-body, .lane-text {
		max-width: 230px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		display: block;
		@media (max-width:767px) {
			max-width: 145px;
		}
}
.pay-info-icon {
	img {
		margin-right: 10px;
	}
}
@media (max-width:767px) {
	.info-heading {
		font-size: 10px;
		margin-bottom: 2px;
	}
	.vehicle-info {
		font-size: 12px;
	}
}
