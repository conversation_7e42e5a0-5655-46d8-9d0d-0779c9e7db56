import React from 'react';
import "./Information.scss";

interface InformationProps {
    title: any;
    valueClassName?: any;
    icon?: any;
    text?: string;
    customView?: any
    sup?: number
    tooltip?: any,
    valueTooltip?: any,
    image?: any,
    className?: string,
    customLabel?: any
}
function Information(props: InformationProps) {
    const { customView, sup, valueClassName,customLabel, tooltip, valueTooltip, image, className, title } = props;

    return (
        <div className={`payment-info d-flex align-items-center ${className}`}>
            <div className="pay-info-icon">
                {image}
            </div>
            <div className="flex-grow-1">
                {title && <div className="d-flex align-items-center">
                    <label className="info-heading">{title}
                        <span>{tooltip && tooltip()}</span>
                    </label>
                    {customLabel && customLabel}
                </div>}
                <div className="media vehicle-info text-truncate">
                    {props.icon}
                    { (customView ? customView : (
                        sup ? <div className="media-body text-truncate">{props.text}<sup>{props.text ? sup : ""}</sup></div>
                            : (valueTooltip ?
                                <div className={valueClassName ? "media-body text-truncate " + valueClassName : "media-body text-truncate"}>
                                    <span>{valueTooltip && valueTooltip()}</span>
                                </div>
                                : <div className={valueClassName ? "media-body text-truncate " + valueClassName : "media-body text-truncate"}>{(props.text) || "NA"}
                                </div>))) }
                </div>
            </div>
        </div>
    )
}
export default Information;