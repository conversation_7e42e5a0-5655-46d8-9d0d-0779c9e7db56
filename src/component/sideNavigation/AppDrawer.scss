.side-nav-wrapper .MuiDrawer-paper {
    top: 64px;
    height: calc(100vh - 64px);
    background-color:  #083654;
}
.side-nav-wrapper .MuiDrawer-paper:hover{
    width: 246px;
}
.side-nav-wrapper {
    .tms-small-head, .tms-head{
        font-size: 15px;
        font-weight: normal;
        line-height: 18px;
        color: #fff;
        border-bottom: 2px solid rgba(216, 222, 229, 0.3);
        padding-bottom: 12px;
        margin: 15px 20px 10px;
    }
    .tms-small-head{
        margin: 15px 10px 10px;
    }
}
.side-nav-wrapper .MuiListItem-root{
    min-height: 48px;
    background-color: #002d4a;
    margin-bottom: 5px;
}
.side-nav-wrapper .MuiListItem-button:hover,
.side-nav-wrapper .MuiListItem-button.active{
    background-image: linear-gradient(to right, #006cc9, #145c81);
}
.side-nav-wrapper .MuiListItem-root,
.side-nav-wrapper .MuiSvgIcon-root {
    color: #fff;
}
.side-nav-wrapper .MuiTypography-root {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.14;
}
.side-nav-wrapper .MuiListItemText-root {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.side-nav-wrapper .MuiSvgIcon-root {
    font-size: 20px;
}
.side-nav-wrapper .MuiListItemIcon-root{
    min-width: auto;
    margin-right: 12px;
}
.side-nav-wrapper .MuiListItemIcon-root img{width: 24px;}

/* collapse menu */
.side-nav-wrapper .MuiCollapse-wrapperInner svg{
    font-size: 12px;
    margin-right: 12px;
}
.side-nav-wrapper .MuiCollapse-wrapperInner .MuiTypography-root {
    font-size: 13px;
    font-weight: 500;
}
.side-nav-wrapper .MuiCollapse-wrapperInner .MuiListItem-button{
    min-height: 40px;
}
.side-nav-wrapper .MuiCollapse-wrapperInner .MuiListItem-button:hover,
.side-nav-wrapper .MuiCollapse-wrapperInner .MuiListItem-button.active{
    background-image: none;
    background-color:  #f7481e;
}

@media (max-width: 767px) {
  .MuiDrawer-root.MuiDrawer-modal .MuiDrawer-paper {
    background-color: #0d3654;
  }
  .MuiDrawer-modal .MuiListItem-gutters{
    padding-left: 20px;
    padding-right: 20px;
  }
  .MuiDrawer-modal .MuiListItem-root{
    padding-top: 12px;
    padding-bottom: 12px;
  }
  .MuiDrawer-modal .MuiCollapse-wrapper .MuiListItem-gutters{
      padding-left: 30px;
      padding-right: 20px;
  }
  .MuiDrawer-modal .MuiCollapse-wrapper .MuiListItem-root{
    padding-top: 6px;
    padding-bottom: 6px;
  }
  .MuiDrawer-modal .MuiListItem-root,
  .MuiDrawer-modal .MuiSvgIcon-root {
    color: rgba(255, 255, 255, 0.87);
  }
  .MuiDrawer-modal .MuiTypography-body1 {
    font-size: 14px;
    color: #ffffff;
    letter-spacing: 0.56px;
    line-height: 1.15;
  }
  .MuiDrawer-modal .MuiListItemText-root {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
  }
  .MuiDrawer-modal .MuiSvgIcon-root {
      font-size: 20px;
  }
  .MuiDrawer-modal .MuiCollapse-wrapper .MuiSvgIcon-root {
      font-size: 12px;
      margin-right: 16px;
  }
  .mb-wrap{
    padding: 18px 20px;
    transition: all 0.2s ease;
    box-shadow: 0px 2px 0px #0f324c;
    /* background: #f7931e; */
    background-image: linear-gradient(to right, #f7931e, #f73f1e);
    display: block;
  }
  .mb-wrap img{
    max-width: 125px;
  }
  .MuiDrawer-modal .MuiListItemIcon-root{
    min-width: 24px;
    margin-right: 15px;
  }
  .MuiDrawer-modal .MuiListItemIcon-root img {
    width: 22px;
  }
  .MuiDrawer-modal .MuiCollapse-wrapper .MuiListItem-root.active{
    background-image: none;
    background-color:  #f7481e;
  }
  .MuiDrawer-modal .MuiListItem-root.active{
    background-image: linear-gradient(to right, #006cc9, #145c81);
  }
}

