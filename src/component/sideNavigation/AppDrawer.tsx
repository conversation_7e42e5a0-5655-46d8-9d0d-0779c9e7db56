import { Box, Collapse } from '@material-ui/core';
import Drawer from '@material-ui/core/Drawer';
import List from '@material-ui/core/List';
import ListItem from '@material-ui/core/ListItem';
import ListItemIcon from '@material-ui/core/ListItemIcon';
import ListItemText from '@material-ui/core/ListItemText';
import { ExpandLess, ExpandMore } from '@material-ui/icons';
import RadioButtonUncheckedIcon from '@material-ui/icons/RadioButtonUnchecked';
import clsx from 'clsx';
import React, { useEffect, useReducer } from 'react';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import { useHistory, useLocation } from 'react-router-dom';
import { menuList } from "../../base/constant/ArrayList";
import { webTitle, webTitleDetails } from '../../base/constant/MessageUtils';
import { isMobile } from '../../base/utility/ViewUtils';
import { openDrawer, setMainMenuInfo } from '../../redux/actions/AppActions';
import DrawerMenuReducers, { DRAWER_INIT_STATE } from '../../redux/reducers/DrawerMenuReducers';
import { getMenuIndex } from '../CommonView';
import { handelNavClick } from "../sideNavigation/SideNavUtils";
import './AppDrawer.scss';
import { menuStyle } from './MenuStyles';



export default function AppDrawer() {
    const classes = menuStyle();
    const [state, dispatch] = useReducer(DrawerMenuReducers, DRAWER_INIT_STATE)
    const appDispatch = useDispatch();
    const history = useHistory();
    const location = useLocation()
    const drawerSate = useSelector((state: any) => state.appReducer.drawerSate, shallowEqual);
    const selectedIndex = useSelector((state: any) =>
        state.appReducer.menuSelectedIndex, shallowEqual
    );
    const drawerOpen = useSelector((state: any) => state.appReducer.drawerSate);

    const refreshSideNavigation = useSelector((state: any) =>
        state.appReducer.sideNavigation, shallowEqual
    );



    const myListener = (location: any, action: any) => {
        let info = getMenuIndex(location);
        appDispatch(setMainMenuInfo(info));
    };

    useEffect(() => {
        const unListen = history.listen(myListener);
        return () => unListen();
        // eslint-disable-next-line
    }, []);

    useEffect(() => {
        if (history && history.location) {
            let info = getMenuIndex(history.location);
            appDispatch(setMainMenuInfo(info));
        }

        // eslint-disable-next-line
    }, [refreshSideNavigation]);
    return (
        <div className="side-nav-wrapper">
            <Drawer
                variant={isMobile ? "temporary" : "permanent"}
                className={clsx(classes.drawer, {
                    [classes.drawerOpen]: drawerSate,
                    [classes.drawerClose]: !drawerSate,
                })}
                classes={{
                    paper: clsx({
                        [classes.drawerOpen]: drawerSate,
                        [classes.drawerClose]: !drawerSate,
                    }),
                }}
                open={isMobile ? drawerOpen : true}
                onClose={() => {
                    appDispatch(openDrawer());
                }}
            >
                {isMobile ?
                    <Box component="div" className="mb-wrap">
                        <img src="/images/logo.svg" alt="GoBOLT ( Camions Logistics Solution Privet Limited )" />
                    </Box> :
                    <>
                        {
                            drawerSate ?
                                <h3 className="tms-head">{webTitleDetails}</h3> :
                                <h3 className="tms-small-head">{webTitle}</h3>
                        }
                    </>}
                <List>
                    {menuList.map((element: any, index: number) => (
                        <li
                            key={index}
                        >
                            <ListItem button
                                onClick={() => {
                                    handelNavClick(element, dispatch, history, appDispatch)
                                    // !drawerSate && appDispatch(openDrawer());
                                }}
                                className={(element.className ? element.className : "  ") + ((selectedIndex === index) ? "active" : " ")}
                                key={element.label}>
                                <ListItemIcon>
                                    {element.image ?
                                        <img src={element.image} alt={element.name} /> :
                                        element['icon']
                                    }
                                </ListItemIcon>
                                <ListItemText primary={element.label} />
                                {(element.subMenu) ? (
                                    state[element.stateKey] ? <ExpandLess /> : <ExpandMore />) : null}
                            </ListItem>
                            <Collapse in={state[element.stateKey]} timeout="auto" unmountOnExit>
                                {element.subMenu && element.subMenu.map((innerElement: any, index: number) => (
                                    <List component="li" disablePadding key={index}>
                                        <ListItem
                                            button
                                            className={(location.pathname.startsWith(innerElement.name)) ? "active" : ""}
                                            onClick={() => {
                                                history.push(innerElement.routePath);
                                                isMobile && appDispatch(openDrawer());
                                            }}>
                                            <RadioButtonUncheckedIcon />
                                            <ListItemText primary={innerElement.label} />
                                        </ListItem>
                                    </List>
                                ))}
                            </Collapse>
                        </li>
                    ))}
                </List>
            </Drawer>
        </div>
    );
}
