// import React from "react";
import { Dispatch } from "redux";
import { closeAllSubMenu, openSubMenu } from "../../redux/actions/DrawerActions";

/**
 *
 * @param label string (Name of Menu)
 * @param dispatch (react dispatch)
 * @param history (Route Between Pages)
 */
export function handelNavClick(element: any, dispatch: any, history: any, appDispatch: Dispatch,) {
    if (element.subMenu) {
        dispatch(openSubMenu(element.stateKey));
    } else {
        history.push(element.routePath)
        dispatch(closeAllSubMenu());
        // appDispatch(openDrawer())
    }
}