import { createStyles, makeStyles, Theme, useTheme } from '@material-ui/core/styles';
import React, { CSSProperties, useEffect, useRef, useState, ComponentType, useCallback } from 'react';
import Select from 'react-select';
import { components } from 'react-select/';
import { OptionProps } from 'react-select/src/components/Option';
import { SingleValueProps } from 'react-select/src/components/SingleValue';
import _ from 'lodash';
import "./AutoComplete.css";
import { OptionType } from './widgetsInterfaces';
import { isNullValue } from '../../base/utility/StringUtils';


const useStyles = makeStyles((theme: Theme) =>
    createStyles({
        root: {
            flexGrow: 1,
        },
        input: {
            display: 'flex',
            padding: 0,
            height: 'auto',
        },
        valueContainer: {
            display: 'flex',
            flexWrap: 'wrap',
            flex: 1,
            alignItems: 'center',
            overflow: 'hidden',
        },

        noOptionsMessage: {
            padding: theme.spacing(1, 2),
        },
        singleValue: {
            fontSize: 16,
        },
        placeholder: {
            position: 'absolute',
            left: 2,
            bottom: 6,
            fontSize: 16,
        },

    }),
);

const { Option, SingleValue } = components;

const MIN_SEARCH_LENGTH = 3;
const SEARCH_DEBOUNCE_TIME_MS = 300;

interface AutoCompleteProps {
    placeHolder: string,
    label: string,
    error?: string,
    isLoading?: boolean
    onChange: Function,
    options: OptionType[] | undefined,
    value: any,
    icon?: any,
    renderOption?: any,
    renderValueHolder?: any,
    showCustomView?: boolean,
    defaultValue?: OptionType
    isDisabled?: boolean,
    mandatory?: boolean,
    name?: string,
    toolTip?: Function
    isClearable?: boolean,
    isShowAll?: boolean,
    menuPortalTarget?: any,
    labelStyle?: any;
    className?: string;
    customMenuList?: ComponentType<any>;
    onAsyncSearch?: (searchValue: string) => Promise<OptionType[]>;
    inputValue?: string;
    onClearInputValue?: () => void;
    showCustomNotFoundMessage?: string;
}

const SelectComponent = Select as any;
const MenuListComponent = components.MenuList as any;

export default function AutoComplete(props: AutoCompleteProps) {
    const {
        error, mandatory, isClearable, isShowAll, menuPortalTarget, labelStyle,
        className, customMenuList, onAsyncSearch, inputValue: inputValueProp, onClearInputValue,
        showCustomNotFoundMessage
    } = props;
    const classes = useStyles();
    const theme = useTheme();
    const [options, setOptions] = useState<any>();
    const [isSearching, setIsSearching] = useState(false);
    const [selectKey, setSelectKey] = useState(0); // Key to force re-render
    const [inputValue, setInputValue] = useState(inputValueProp || '');
    
    useEffect(() => {
        if ((props.options && props.options.length <= 10) || isShowAll) {
            setOptions(props.options)
        } else if (props.options && props.options.length > 10) {
            setOptions(props.options.slice(0, 10))
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [setOptions, props.options])

    useEffect(() => {
        if (inputValueProp) {
            setInputValue(inputValueProp);
            const localResults = filterLocalOptions(inputValueProp);
            setOptions(localResults);

            if (shouldTriggerApiSearch(inputValueProp, localResults)) {
                debouncedApiSearch(inputValueProp);
            }
            // Simulate a click on the input to trigger menu open
            setTimeout(() => {
                if (myInput.current) {
                    const input = myInput.current;
                    input.focus();
                    // This will open the menu
                    input.onMenuOpen && input.onMenuOpen();
                }
            }, 50);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [inputValueProp]);

    // Filter local options
    const filterLocalOptions = (searchTerm: string): OptionType[] => {
        if (!props.options) return [];

        const filtered = props.options.filter((element: OptionType) => {
            try {
                return element.label.toLowerCase().includes(searchTerm.toLowerCase());
            } catch (error) {
                return element;
            }
        });

        return isShowAll ? filtered : filtered.slice(0, 10);
    };

    // Check if we should trigger API search
    const shouldTriggerApiSearch = (searchTerm: string, localResults: OptionType[]): boolean => {
        if (!onAsyncSearch) return false;
        if (!searchTerm || searchTerm.length < MIN_SEARCH_LENGTH) return false;
        return localResults.length === 0 || searchTerm.length >= MIN_SEARCH_LENGTH;
    };

    const debouncedApiSearch = useCallback(
        _.debounce(async (searchTerm: string) => {
            if (!searchTerm || searchTerm.length < MIN_SEARCH_LENGTH) {
                setIsSearching(false);
                return;
            }

            setIsSearching(true);

            try {
                let apiResults: OptionType[] = [];

                if (onAsyncSearch) {
                    // Use custom API search function
                    apiResults = await onAsyncSearch(searchTerm);
                }

                // Combine local results with API results, removing duplicates
                const localResults = filterLocalOptions(searchTerm);
                const combinedResults = [...localResults];

                // Add API results that don't already exist in local results
                apiResults.forEach(apiResult => {
                    const exists = localResults.some(local =>
                        local.value === apiResult.value ||
                        local.label.toLowerCase() === apiResult.label.toLowerCase()
                    );
                    if (!exists) {
                        combinedResults.push(apiResult);
                    }
                });

                setOptions(combinedResults.slice(0, 10));
            } catch (error) {
                console.error('API search error:', error);
                // Fallback to local search on API error
                setOptions(filterLocalOptions(searchTerm));
            } finally {
                setIsSearching(false);
            }
        }, SEARCH_DEBOUNCE_TIME_MS),
        [onAsyncSearch, props.options, isShowAll]
    );

    function handleChangeSingle(value: any) {
        myInput.current && myInput.current.blur();
        props.onChange(value);
    }

    const selectStyles = {
        input: (base: CSSProperties) => ({
            ...base,
            color: theme.palette.text.primary,
            '& input': {
                font: 'inherit',
            },
        }),
    };

    // Expose reset function to parent components
    const resetComponent = useCallback(() => {
        setSelectKey(prev => prev + 1);
    }, []);

    // Generic MenuList wrapper
    const GenericMenuList = (menuProps: any) => {
        if (customMenuList) {
            const CustomComponent = customMenuList;
            return (
                <CustomComponent
                    {...menuProps}
                    resetSelect={resetComponent}
                />
            );
        }

        // Default MenuList
        return <MenuListComponent {...menuProps} />;
    };

    const customOption = (optionProps: OptionProps<OptionType>) => {
        const CustomOption = Option as any
        return (
            <CustomOption {...optionProps}>
                {(props.showCustomView && props.renderOption && props.renderOption(optionProps.data)) || optionProps.data.label}
            </CustomOption>
        );
    }


    const customSingleValue = (valueProps: SingleValueProps<OptionType>) => {
        const CustomSingleValue = SingleValue as any
        return (
            <CustomSingleValue {...valueProps}>
                {(props.showCustomView && props.renderValueHolder && props.renderValueHolder(valueProps)) || valueProps.children}
            </CustomSingleValue>
        );
    }

    const myInput = useRef<any>();
    useEffect(() => {
        error && myInput.current && myInput.current.focus();
    }, [error]);

    return (
        <div className={`autocomplete-wrap${className ? ` ${className}` : ''}`}>
            <label className={labelStyle ? labelStyle : "d-flex align-items-center"}>{props.label}
                {mandatory && <span className="mandatory-flied">*</span>}
                <span>{props.toolTip && props.toolTip()}</span>
            </label>
            <SelectComponent
                key={selectKey}
                menuPlacement='auto'
                ref={myInput}
                className="select-container"
                menuPortalTarget={menuPortalTarget}
                classes={classes}
                styles={selectStyles}
                // inputId="react-select"
                isClearable={isClearable}
                classNamePrefix="select"
                // filterOption={
                //     createFilter({
                //         ignoreAccents: false,
                //         ignoreCase: true,
                //     })
                // }
                filterOption={null}
                inputValue={inputValue}
                noOptionsMessage={({ inputValue }: { inputValue: string }) => showCustomNotFoundMessage
                    ? showCustomNotFoundMessage : 'No options'
                }
                ignoreAccents={false}
                isLoading={props.isLoading || isSearching}
                isSearchable={true}
                backspaceRemovesValue={true}
                placeholder={props.placeHolder}
                options={options}
                name={props.name || ''}
                isDisabled={props.isDisabled}
                components={{
                    Option: customOption,
                    SingleValue: customSingleValue,
                    MenuList: GenericMenuList,
                }}
                defaultValue={props.defaultValue}
                value={(props.value && props.value) || null}
                onInputChange={(newValue: string, action: any) => {
                    if (action.action === 'input-blur' || action.action === 'menu-close' || action.action === 'set-value') {
                        // onBlur reset label to last selected value label
                        setInputValue('');
                        if (!isNullValue(inputValue) && onClearInputValue) {
                            onClearInputValue();
                        }
                    } else if (action.action === 'input-change') {
                        setInputValue(newValue);
                    }

                    if (!newValue) {
                        // Reset to initial options when search is cleared
                        if ((props.options && props.options.length <= 10) || isShowAll) {
                            setOptions(props.options || []);
                        } else if (props.options && props.options.length > 10) {
                            setOptions(props.options.slice(0, 10));
                        }
                        return;
                    }
                    // Filter local options first
                    const localResults = filterLocalOptions(newValue);
                    setOptions(localResults);

                    // Check if we should trigger API search
                    if (shouldTriggerApiSearch(newValue, localResults)) {
                        debouncedApiSearch(newValue);
                    }
                }}
                onChange={handleChangeSingle}
            />
            {error && <label className="error"

            >{error}</label>}
        </div>
    );
}
