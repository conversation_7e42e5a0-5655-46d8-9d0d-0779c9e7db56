/* button css */
.btn {
    height: 40px;
    padding: 8px 15px;
    font-weight: 500;
    font-size: 14px;
    border-radius: 20px;
    display: inline-flex;
    align-items: center;
    -webkit-align-items: center;
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important;
    border: none;
    position: relative;
  }
  .btn .MuiSvgIcon-root {
    font-size: 19px;
    margin-right: 5px;
  }
  .btn span + .MuiSvgIcon-root {
    font-size: 20px;
    margin-right: 0px;
    margin-left: 10px;
  }
  .btn:focus{
    outline: none;
    box-shadow: none;
  }
  .btn-orange {
    color: #fff;
    background: #f7931e;
    background: -webkit-linear-gradient(to left, #F73F1E, #f7931e, #F73F1E);
    background: linear-gradient(to left, #F73F1E, #f7931e, #F73F1E);
    background-size: 200% auto;
    box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.1);
    transition: 0.5s all !important;
    -webkit-transition: 0.5s all !important;
  }
  .btn-orange:hover {
    color: #fff;
    background-position: right center;
  }

  .btn-rounded {
    border-radius: 50px;
  }

  .btn-blue {
    background: #006cc9;
    /* border: solid 2px #ebeff3; */
    background-image: -webkit-linear-gradient(to left, #006cc9 ,#145288, #006cc9);
    background-image: linear-gradient(to left,#006cc9 ,#145288, #006cc9);
    background-size: 200% auto;
    transition: 0.5s all !important;
    box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.1);
    -webkit-transition: 0.5s all !important;
    color: #fff;
  }
  .btn-outline-blue{
    color: #006cc9;
    border: 1px solid #006cc9;
    transition: 0.5s all !important;
    -webkit-transition: 0.5s all !important;
  }
  .btn-outline-blue:hover {
    color: #006cc9;
    opacity: 0.8;
  }

  .btn-white{
    background-color: #fff;
    margin-right:12px;
    height:35px;
  }
  .btn-action{
    box-shadow: 0px 2px 3px #0000001A;
    font-weight: 500;

  }

  .btn-grey--cancel{
    background: linear-gradient(270deg, #F0EFEF , #DFE5EA );
    background: -webkit-linear-gradient(270deg, #F0EFEF , #DFE5EA);
    color:#6A7A8B;
  }
  .btn-grey--cancel:hover{
    color: #000000;
  }
  .btn-red--reject{
    background: linear-gradient(267deg, #FD1D00 , #C31D07 );
    background: -webkit-linear-gradient(267deg, #FD1D00 , #C31D07);
    color:#FFFFFF;
  }
  .btn-red--reject:hover{
    color: #ffffff;
  }
  .btn-blue:hover {
    color: #fff;
    background-position: right center;
  }
  .btn-detail {
    min-width: 80px;
    background: #f9f9f9;
    background-size: 200% auto;
    transition: 0.5s all !important;
    -webkit-transition: 0.5s all !important;
    height: auto;
    background-image: linear-gradient(to bottom, #f9f9f9, #eaeff3);
    color: #133751;
  }
  .btn-detail:hover {
    color: #fff;
    background: #f7931e;
    background: -webkit-linear-gradient(to right, #f7931e, #de5811, #f7931e);
    background: linear-gradient(to right, #f7931e, #de5811, #f7931e);
    background-size: 200% auto;
    background-position: right center;
  }
  .table-btn .btn-detail{
    font-size: 13px;
    line-height: 1.15;
    border-radius: 18px;
    border: solid 1px #ffffff;
  }
.permission-btn{
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    font-size: 16px;
    font-weight: 400;
}

.btn-outline-orange,
.btn-outline-orange:hover {
  color: #F3933D;
  border: 1px solid #F3933D;
}

.btn-small {
  font-size: 11px;
  padding: 3px;
  height: 19px;
  padding: 4px;
}
.btn-small .MuiSvgIcon-root{font-size: 15px;}

.btn-square {
  border-radius: 4px;
}

.action-btn{
  padding: 2px 3px;
  min-width: 36px;
  height: 36px;
  border: solid 1px #ffffff;
  background-image: linear-gradient(to bottom, #f9f9f9, #eaeff3);
}
.action-btn .MuiSvgIcon-root{
  margin-right: 0;
  font-size: 24px;
}

.add-btn, .minus-btn {
  padding: 0px 3px;
  width: 24px;
  height: 24px;
  font-weight: 500;
  background-color: #1b6cc9;
}
.minus-btn{
  border-radius: 50%;
  background-color: #f6b142;
}
.add-btn .MuiSvgIcon-root, .minus-btn .MuiSvgIcon-root{
  margin-right: 0;
  font-size: 18px;
  color: #ffffff;
}
.view-pod-btn.btn{
  /* max-width: 102px; */
  height: 31px;
  font-size: 12px;
  padding: 0px 10px;
}
.btn.view-pod-btn svg{
  margin-right: 5px;
  font-size: 18px;
}
.file-upload-wrap .file-upload-btn,
.file-upload-wrap .file-upload-btn:hover{
  width: 112px;
  height: 26px;
  border-radius: 3px;
  background: #fff;
  border: 1px solid #f7931e;
  font-size: 11px;
  color: #f7931e;
  box-shadow: none;
  text-transform: capitalize;
  font-weight: normal;
}
.btn.sml-btn{
  border-radius: 50px;
  min-width: 72px;
  font-size: 11px;
  height: 27px;
}
.btn.sml-btn svg {
  margin-right: 5px;
  font-size: 14px;
}
.btn-hide {
  display: none;
}

.btn-outline-danger{
  color: #ED1B00;
  border: 1px solid #ED1B00;
  padding: 4px 16px;
}
.btn-outline-danger:hover{
  color: #fff;
  background: #ED1B00;
}
.btn-outline-gray{
  color: #313131;
  border: 1px solid #313131;
  padding: 4px 16px;
}
.btn-outline-gray:hover{
  color: #fff;
  background: #313131;
}

.btn-edit{
  background: #fff;
  border: 1px solid #f7931d;
  color: #000;
  font-size: 14px;
  height: auto !important;
  border-radius: 3px;
  padding: 3px 8px;
  svg{
    font-size: 18px;
    color: #f7931d;
  }
  &:hover{
    color: #fff;
    /* background: #f7931d; */
    background: linear-gradient(to right, #f7931e, #de5811);
    svg{
      color: #fff;
    }
  }
}
.btn-grey{
  background: linear-gradient(270deg, #F0EFEF , #DFE5EA );
  background: -webkit-linear-gradient(270deg, #F0EFEF , #DFE5EA);
  color:#232323;
}
.btn-grey:hover{
  color: #000000;
}


.count-text{
    display: flex;
    align-items: center;
    justify-content: center;
    letter-spacing: 0px;
    color: #333333;
    width: 21px;
    height: 21px;
    background: #fff;
    border-radius: 50%;
    font-size: 13px;
    font-weight: 500;
}

@media screen and (min-width:768px) {
  .btn img{
    margin-right: 10px;
  }
}
.upload-doc-btn{
  padding: 0;
}
.upload-doc-btn img{margin: 0;}
.upload-doc-btn .value_show{
    position: absolute;
    top: -5px;
    right: -3px;
    background: #fff;
    border-radius: 22px;
    width: 17px;
    line-height: 17px;
    font-size: 11px;
    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1607843137);
}
@media screen and (max-width:767px) {
  .mobile-create-btn{
    position: fixed;
    z-index: 9;
    bottom: 20px;
    right: 20px;
    padding: 0;
    margin: 0;
    height: auto !important;
  }
  .btn.view-pod-btn svg{
    margin-right: 0px;
  }


}