import React from "react";
import { useSelector, shallowEqual } from "react-redux";
import "./Button.css";
import { CircularProgress, makeStyles, createStyles } from "@material-ui/core";
export interface ButtonProps {
  title?: string;
  leftIcon?: any;
  rightIcon?: any;
  buttonStyle?: string;
  onClick?: React.MouseEventHandler;
  type?: any
  loading?: boolean
  primaryButton?: boolean
  count?: number,
  disable?: boolean,
  customView?:any,
  mobileNumberApproved?:boolean
  buttonContainerStyle?:string
}

const useStyles = makeStyles(() =>
  createStyles({
    wrapper: {
      position: 'relative',
      display: "inline"
    },
    buttonProgress: {
      color: "#006cc9",
      position: 'absolute',
      top: '50%',
      left: '50%',
      marginTop: -12,
      marginLeft: -12,
    },
  })
);


export default function Button(props: ButtonProps) {
  const { loading, count = 0, disable } = props;
  const classes = useStyles();
  const disableButton = useSelector((state: any) =>
    state.appReducer.disableActionButton, shallowEqual
  )
  return (
    <div className={`${classes.wrapper} ${props.buttonContainerStyle || ""}`}>
      <button
        type={props.type || 'button'}
        className={"btn ".concat(props.buttonStyle || "")}
        onClick={props.onClick}
        disabled={disableButton || loading || disable}
      >
        {props.leftIcon}
        {(props.mobileNumberApproved && props.customView) ? props.customView :
          props.title && <span> {props.title}</span> }
        {props.rightIcon}
        {(count > 0) && <span className="ml-2 count-text"> {count}</span>}
      </button>
      {loading && <CircularProgress size={24} className={classes.buttonProgress} />}
    </div>
  );
}


