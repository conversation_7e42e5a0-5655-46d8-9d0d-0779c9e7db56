import 'chart.js/auto';
import React from 'react';
import { Pie } from 'react-chartjs-2';
import './PieChart.css';

interface DataProps {
    title?: string,
    data: any,
    options?: any,
    height?: any
    // labels: any
}
export default function Pie<PERSON>hart(props: DataProps) {
    const { data, options, height, title } = props;
    return (
        <section className="graph-wrap">
            <label>{title}</label>
            <div className="container-fluid">
                <Pie
                    data={data}
                    options={{
                        ...options,
                        legend: {
                            ...options.legend,
                            position: "bottom",
                        },
                        tooltips: {
                            callbacks: {
                                label: function (tooltipItems: any, data: any) {
                                    return data.labels[tooltipItems.index] + ': ' + data.datasets[0].data[tooltipItems.index] + ' %';
                                }
                            }
                        },
                    }}
                    height={height}
                />
            </div>
            <div className="chart-description mt-3">Distribution As Per Amount</div>
        </section>
    );
}