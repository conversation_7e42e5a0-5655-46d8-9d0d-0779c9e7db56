/* lane item */
/* table-detail-listing */
/* table field */
.table-list-view {
  .table {
    th {
      vertical-align: middle;
    }

    td {
      vertical-align: middle;
    }
  }

  .MuiPaper-rounded {
    border-radius: 0;
    box-shadow: none;
    background: transparent;
  }

  .MuiTable-root {
    border-collapse: initial;
    border-spacing: 0 5px;
  }

  .MuiTableCell-root {
    border: none;
  }

  .MuiTableRow-root {
    .MuiTableCell-root {
      padding: 6px 10px;
      min-width: 90px;
      max-width: 320px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .MuiTableCell-head {
      font-size: 14px;
      font-weight: 400;
      line-height: 1.17;
      color: #768a9e;
      padding: 0 10px 5px;
    }
  }

  .MuiTableCell-body {
    font-size: 14px;
    line-height: 1.43;
    color: #323232;
    background: #fff;
    border-bottom: 1px solid #ebeff3;
    border-top: 1px solid #ebeff3;
    height: 56px;

    &:first-child {
      border-left: 1px solid #ebeff3;
    }

    &:last-child {
      border-right: 1px solid #ebeff3;
    }

    .btn {
      height: auto;
      font-size: 13px;
    }
    .upload-doc-btn img{
      width: 35px;
    }
  }

  .btn {
    span {
      + {
        .MuiSvgIcon-root {
          font-size: 15px;
        }
      }
    }
  }

  .table-wrapper {
    min-height: 570px;
    max-height: 570px;
    overflow: auto;

    ///////
    &::-webkit-scrollbar {
      width: 7px;
      height: 7px;
    }

    /* Track */
    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      -webkit-border-radius: 10px;
      border-radius: 10px;
    }

    /* Handle */
    &::-webkit-scrollbar-thumb {
      -webkit-border-radius: 10px;
      border-radius: 10px;
      background-color: grey;
      -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
    }
  }

  .MuiTablePagination-toolbar {
    border-radius: 4px;
    border: solid 1px #ebeff3;
    background-color: #fff;
    margin-top: 15px;
    color: #768a9e;
    font-size: 14px;
  }
  .Mui-selected {
    .MuiTableCell-body {
      border-top: solid 1px #ff8900;
      border-bottom: solid 1px #ff8900;
    }
  }
}
.MuiTablePagination-actions {
  margin-left: 0 !important;
}

.table-detail-listing {
  .table-list-view {
    .table-wrapper {
      min-height: auto;
    }

    .MuiTableRow-root {
      .MuiTableCell-root {
        border-radius: 0;
        border-top: 0px;
        border-bottom: 1px solid #ebeff3;
        height: 50px;
        padding: 0 20px;
        font-size: 14px;
        line-height: 1.14;
        letter-spacing: 0.28px;
        color: #083654;
        min-width: 150px;
      }

      .MuiTableCell-head {
        height: 32px;
        font-weight: normal;
        color: #768a9e;
        font-size: 12px;
      }
    }

    // .MuiTableCell-body {
    //   .btn {
    //     width: 82px;
    //     height: 32px;
    //     border-radius: 50px;
    //     margin-left: 15px;
    //   }
    // }

    .MuiTable-root {
      border-spacing: 0;
    }
  }

  .input-wrap {
    .MuiInputBase-root {
      min-height: 32px;
      padding: 0 10px;
    }
  }

  .MuiInputBase-root.Mui-disabled {
    background: #f9f9f9;
  }
}
.MuiInputBase-root.Mui-disabled {
  background: #f9f9f9;
}

.view-text svg {
  width: 16px;
  margin-right: 6px;
}

// table input fields with multiple row

.table-detail-listing {
  &.table-detail-fields {
    border-bottom: 1px solid #ebeff3;
    padding-bottom: 12px;
    .MuiTableRow-root {
      .MuiTableCell-head {
        font-size: 13px;
        line-height: 1.15;
        margin-bottom: 5px;
        color: #083654;
        background: #fff;
        border: 0;
        padding-left: 0;
      }
      .MuiTableCell-root {
        border: 0;
        padding-left: 0;
        &:last-child {
          padding-right: 0;
        }
        .autosuggest-wrap {
          label {
            margin-bottom: 8px;
          }
        }
        .MuiInputBase-root.Mui-disabled {
          height: 47px;
          border: solid 2px #e0e4e6;

          &.MuiInputBase-fullWidth {
            border: 0;
          }
        }

        .input-wrap {
          .MuiInputBase-root {
            height: 45px;
          }
        }
      }
    }
  }
}
