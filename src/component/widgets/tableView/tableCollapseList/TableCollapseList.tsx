import { TablePagination } from '@material-ui/core';
import Paper from '@material-ui/core/Paper';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import React, { useEffect } from 'react';
import { isNullValue } from '../../../../base/utility/StringUtils';
import DataNotFound from '../../../error/DataNotFound';
import "./TableCollapseList.scss";
import TableCollapseRow from './TableCollapseRow';
import TableHeader from './TableHeader';

interface TableListProps {
    rowsPerPage: number,
    rowsPerPageOptions: Array<number>,
    listData: Array<any> | undefined,
    currentPage: number,
    tableColumns: Array<any>,
    childrenColumns?: Array<any>
    onChangePage: (event: React.MouseEvent<HTMLButtonElement> | null, page: number) => void,
    onRowsPerPageChange: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement>,
    onClickActionButton?: Function;
    actionButtonTitle?: string,
    actionButtonStyle?: string,
    totalCount?: number,
    icon?: any,
    onClickWayPoints?: (event: React.MouseEvent<HTMLButtonElement> | null) => void,
    noDataView?: any,
    childElementKey?: string,
    emptyChildMessage?: string,
    byDefaultOpenIndex?: any
}

export default function TableCollapseList({
    onChangePage,
    currentPage,
    noDataView,
    rowsPerPage,
    rowsPerPageOptions,
    onRowsPerPageChange,
    totalCount,
    childrenColumns,
    tableColumns,
    childElementKey,
    emptyChildMessage,
    byDefaultOpenIndex,
    ...props
}: TableListProps) {
    const [expandIndex, setExpandIndex] = React.useState<number>(0);
    const handleExpand = (index: number) => {
        if (index === expandIndex - 1) {
            setExpandIndex(0);
            return
        }
        setExpandIndex(index + 1)
    }

    useEffect(() => {
        !isNullValue(byDefaultOpenIndex) && setExpandIndex(byDefaultOpenIndex + 1)
    }, [byDefaultOpenIndex])

    return (
        (props.listData && props.listData.length > 0 &&
            (
                <div className="table-list-view table-collapse-list">
                    <Paper>
                        <div className="table-wrapper">
                            <Table stickyHeader>

                                <TableHeader tableColumns={tableColumns} isCollapsible />
                                <TableBody>
                                    {props.listData.map((item, index) => (
                                        <TableCollapseRow
                                            key={index}
                                            tableColumns={tableColumns}
                                            data={item}
                                            expand={expandIndex - 1 === index}
                                            changeExpand={() => handleExpand(index)}
                                            emptyChildMessage={emptyChildMessage}
                                            childrenColumns={childrenColumns}
                                            childElementKey={childElementKey}
                                            childColSpan={tableColumns.length + 1}
                                        />
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                        {totalCount &&
                            <TablePagination
                                rowsPerPageOptions={rowsPerPageOptions}
                                component="div"
                                count={totalCount}
                                rowsPerPage={rowsPerPage}
                                page={(currentPage - 1)}
                                backIconButtonProps={{
                                    'aria-label': 'previous page',
                                }}
                                nextIconButtonProps={{
                                    'aria-label': 'next page',
                                }}
                                //New version has change of method name from : onChangePage -> onPageChange
                                onPageChange={(event: any, page: number) => {
                                    onChangePage(event, page + 1)
                                }}
                                onRowsPerPageChange={onRowsPerPageChange}
                            />
                        }

                    </Paper>
                </div>
            )
        ) || (noDataView ? noDataView : <DataNotFound />)

    );
}

