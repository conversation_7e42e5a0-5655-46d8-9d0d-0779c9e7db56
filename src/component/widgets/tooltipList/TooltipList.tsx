import React from 'react';
import { InfoTooltip } from '../tooltip/InfoTooltip';
import Styles from "./TooltipList.module.scss"
import { isMobile } from '../../../base/utility/ViewUtils';
import { OverflowTip } from '../tooltip/OverFlowToolTip';

interface TooltipListProps {
    showTooltipData: boolean,
    label: string | number | React.ReactNode,
    tooltipData: Array<any>,
    infoText: any
}

export const TooltipList = (props: TooltipListProps) => {
    const { showTooltipData, label, tooltipData, infoText } = props;
    return (
            <div className={Styles.reason_tooltip}>
                <OverflowTip
                    elementStyle={{ maxWidth: "97px"}}
                    text={label}
                />
                {showTooltipData  && <InfoTooltip
                    style={{
                        tooltip: {
                            maxWidth: "320px"
                        }
                    }}
                    title={
                        <ul className={Styles.re_list}>
                            {
                                tooltipData.map((element: any, ind: any) => {
                                    return <li key={ind}>{element}</li>
                                })
                            }
                        </ul>
                    }
                    placement={"top"}
                    disableInMobile={"false"}
                    disableTouchListener={!isMobile}
                    infoText={infoText}
                />}
            </div>
        )
}