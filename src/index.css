body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* orientation css */

.orientation-wrap {
  position: fixed;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background-color: #006aaf;
  z-index: 15;
  pointer-events: none;
  display: none;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  z-index: 9999;
}

.orientation-wrap .ori-img img {
  width: 200px;
}

.orientation-wrap .orientation-container {
  width: 80%;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
  color: #fff;
}

.orientation-wrap .ori-head {
  font-size: 20px;
  margin-bottom: 10px;
  /* margin-top: 20px; */
}

@media screen and (max-device-width: 767px) and (orientation: landscape) {
  /* .orientation-wrap {
    display: flex !important;
  } */
}