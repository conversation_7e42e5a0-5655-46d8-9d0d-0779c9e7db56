import { CheckCircle, ReportProblem } from "@material-ui/icons";
import React, { useEffect } from "react";
import { confirmLabel, typeConfrimLabel } from "../../base/constant/MessageUtils";
import EditText from "../../component/widgets/EditText";
import ModalContainer from "../ModalContainer";

interface DuplicateEntryModalProps {
  open: boolean;
  onClose: any;
  onApply: any;
  alertMessage: string;
  confirmMessage: string;
  setConfirmMessage: any;
  loading: boolean;
  buttonTitle: string;
}

function DuplicateEntryModalModal(props: DuplicateEntryModalProps) {
  const { open, onClose, onApply, alertMessage, confirmMessage, setConfirmMessage, loading, buttonTitle } = props;

  useEffect(()=>{
    if(!open){
      setConfirmMessage("");
    }
  }, [open])

  return (
    <ModalContainer
      title="Duplicate Entry"
      open={open}
      onClose={onClose}
      onApply={onApply}
      styleName={"common-modal duplicate-entry-modal"}
      primaryButtonTitle={buttonTitle}
      primaryButtonLeftIcon={<CheckCircle/>}
      primaryButtonDisable={confirmMessage && confirmMessage.length==7 && confirmMessage.toLowerCase()==="confirm" ? false : true }
      loading={loading}
    >
      <div className="text-center modal-common-wrap">
        <ReportProblem/>
        <h3 className="red-text">Duplicate Entry</h3>
        <h4>{alertMessage}</h4>
        <p>If yes, then please type confirm in the below box.</p>
        <EditText 
          label={confirmLabel}
          value={confirmMessage}
          placeholder={typeConfrimLabel}
          mandatory
          maxLength={7}
          onChange={(text: string) => {
            setConfirmMessage(text);
          }}
        />
      </div>
    </ModalContainer>
  );
}

export default DuplicateEntryModalModal;

