.custom-modal .MuiDialog-scrollPaper {
    overflow-x: hidden;
    overflow-y: auto;
    text-align: center;
    display: block;
}
.custom-modal .MuiDialog-paper {
    border-radius: 8px;
    display: inline-block;
    text-align: left;
    vertical-align: middle;
    max-height: inherit;
    overflow-y: initial;
    min-width: 800px;
}
.custom-modal .MuiDialogContent-root {
    border: none;
    padding: 20px 25px 0;
    overflow: visible;
}
.custom-modal .MuiDialogTitle-root {
    padding: 8px 24px;
    height: 50px;
    background-image: linear-gradient(to bottom, #f9f9f9, #eaeff3);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    display: flex;
    align-items: center;
}

.custom-modal .MuiTypography-root {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 18px;
    font-weight: normal;
    line-height: 1.2;
    color: #083654;
    width: 100%;
}
.custom-modal .MuiDialogActions-root {
    padding: 10px 25px 30px;
}

/* form row */
.custom-form-row .form-group {
    margin-bottom: 20px;
}
.custom-form-row .autocomplete-wrap,
.custom-form-row .input-wrap, .custom-form-row .location-search-wrap, .input-wrap,
.custom-form-row .autosuggest-wrap, .autosuggest-wrap, .location-search-wrap{
    position: relative;
}
.custom-form-row .autocomplete-wrap label,
.custom-form-row .custom-date-picker .MuiInputLabel-root,
.custom-form-row .input-wrap label, .custom-form-row .location-search-wrap label, .input-wrap label,
.autosuggest-wrap label {
    font-size: 14px;
    line-height: 1.15;
    /* color: #768a9e; */
    margin-bottom: 5px;
    color: #133751;
}
.custom-form-row .autocomplete-wrap label .MuiSvgIcon-root,
.custom-form-row .custom-date-picker .MuiInputLabel-root .MuiSvgIcon-root,
.custom-form-row .input-wrap label .MuiSvgIcon-root,
.autosuggest-wrap label .MuiSvgIcon-root{
    color: #f7931e;
    font-size: 18px;
    margin-left: 4px;
}

.custom-form-row .select-container .select__control,
.custom-form-row .custom-date-picker .MuiInput-root,
.custom-form-row .input-wrap .MuiInputBase-root,
.autosuggest-wrap .MuiInputBase-input{
    /* background-color: #ffffff; */
    border-radius: 4px;
    border: solid 2px #e0e4e6;
    margin: 0;
    color: #083654;
    font-size: 14px;
}

.text-overflow .autosuggest-wrap .MuiInputBase-input{
    text-overflow: ellipsis;
}
.custom-form-row .select-container .select__dropdown-indicator,
.custom-form-row .custom-date-picker .MuiIconButton-root {
    color: #acb6c0;
}
.custom-form-row .MuiInputBase-root.Mui-disabled, .custom-form-row .MuiInputBase-input.Mui-disabled{
    background-color: #f9f9f9;
}
.form-control:disabled{
    background-color: #f9f9f9;
    border: none !important
}
.custom-form-row .MuiInputBase-root.Mui-disabled .MuiInputBase-input{
    color: #083654;
    opacity: 1;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
.custom-form-row  .custom-date-picker .MuiInput-underline:after{
    border-bottom: none;
}


/* Way Point Modal */
.way-point-modal.custom-modal .MuiDialog-paper{
    min-width: 400px;
}
.way-point-modal.custom-modal .MuiDialogActions-root{
    display: none;
}

.MuiDialogActions-root.csv.MuiDialogActions-spacing{
    justify-content: space-between;
}
.edit-upload-modal.custom-modal .MuiDialogContent-root{
    padding: 0;
}
.bill-file{
    padding: 15px;
}
.ok-btn{
    min-width: 90px;
}
.bill-up-load-modal.custom-modal .MuiDialogContent-root{
    padding-left: 20px;
    padding-right: 20px;
}
.bill-up-load-modal.custom-modal .MuiDialogActions-root{
    padding-bottom: 16px;
    justify-content: flex-end;
}
.bill-up-load-modal .input-wrap .MuiInputBase-root{
    margin-right: 0;
}
/* common modal css */
.common-modal.custom-modal .MuiDialogActions-root {
    padding: 17px 25px 27px;
}
.common-modal .modal-common-wrap .MuiSvgIcon-root{
    width: 51px;
    height: 56px;
    color: #ED1B00;
    opacity: 0.9;
}
.common-modal .modal-common-wrap h3{
    text-align: center;
    font-size: 24px;
    line-height: 32px;
    font-weight: normal;
    margin: 5px 0 0;
}
.common-modal .modal-common-wrap h4{
    font-size: 20px;
    line-height: 26px;
    color: #768A9E;
    margin: 11px 0 24px;
    font-weight: 300;
}
.common-modal .modal-common-wrap p{
    font-size: 14px;
    line-height: 19px;
    color: rgb(8 54 84 / 90%);
    margin-bottom: 21px;
}

.modal-contact .MuiDialog-paper{
    min-width: 500px;
    max-width: 500px;
}

/* .modal-contact .btn {
    border-radius: 3px;
} */

@media(min-width:768px){
    .common-modal.custom-modal .MuiDialog-paper{
        min-width: 480px;
        max-width: 480px;
    }
}

@media (min-width:992px) {
    .order-shipment-modal .MuiDialog-paper {
        min-width: 900px;
    }
}

@media(max-width:992px){
    .custom-modal .MuiDialog-paper{
        min-width: 92%;
    }
}
@media (max-width: 767px) {
    .custom-modal .MuiDialog-paper{
        margin: 0;
        min-width: 100%;
        border-radius: 0;
        box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
        width: 100%;
    }
    /* Way Point Modal */
    .way-point-modal.custom-modal .MuiDialog-paper{
        min-width: 100%;
        box-shadow: none;
    }
    .custom-modal .MuiDialogContent-root {
        border: none;
        padding: 20px 15px 0;
        overflow: visible;
    }
    .way-point-modal.custom-modal .MuiDialogContent-root{
        padding-top: 10px;
    }
    .way-point-stepper .lane-stepper-wrap .MuiStepper-vertical{
	    padding-top:0;
    }
    .custom-modal .MuiDialogTitle-root {
        padding: 0 15px;
        height: 48px;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.07);
        background: #083654;
        border-radius: 0;
    }
    .custom-modal .MuiTypography-root {
        font-size: 18px;
        font-weight: 500;
        line-height: 1.14;
        color: #ffffff;
    }
    .input-wrap .MuiInputBase-input{
        font-size: 14px !important;
    }
    .custom-modal .MuiDialogTitle-root .MuiSvgIcon-root {
         color: #ffffff;
    }
    .custom-modal .MuiDialogActions-root{
        padding: 8px 15px 20px;
        text-align: right;
    }

    /* .MuiDialogActions-spacing > :not(:first-child){
        width: 100%;
    } */
    .custom-modal .MuiDialog-scrollPaper{
        box-shadow: none;
    }
    .custom-modal .MuiDialog-scrollPaper{
        background: #fbfbff;
    }
    .custom-modal .MuiDialogActions-spacing > :not(:first-child){
        width: auto;
    }
     .custom-form-row .input-wrap .MuiInputBase-root .MuiSvgIcon-root{
         color: rgba(0, 0, 0, 0.54);
     }
}


.input-pr-0 {
    padding-right: 0;
}
.createMaterial .label-none {
    margin-top: 14px;
}
.createMaterial .PrivateSwitchBase-root-2596 {
    top: 1px;
    position: relative;
    margin-left: -10px;
}
.createMaterial span.checkLabel {
    top: 2px;
    position: relative;
}
.createMaterial .reduce-margin {
    margin-bottom: 11px;
}

@media (max-width: 767px) {
    .input-pr-0 {
        padding-right: 15px;
    }
}

/* create material modal width */

@media (min-width: 992px) {
    .custom-modal.createMaterialModal .MuiDialog-paper{
        min-width: 900px;
    }
}

