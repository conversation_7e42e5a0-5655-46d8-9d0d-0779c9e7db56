import { IconButton } from "@material-ui/core";
import Dialog from "@material-ui/core/Dialog";
import DialogActions from "@material-ui/core/DialogActions";
import DialogContent from "@material-ui/core/DialogContent";
import DialogTitle from "@material-ui/core/DialogTitle";
import { Close, GetApp } from "@material-ui/icons";
import React, { ReactNode } from "react";
import { isNullValue } from "../base/utility/StringUtils";
import Button from "../component/widgets/button/Button";
import "./ModalContainer.css";


interface ModalContainerProps {
    children: ReactNode,
    open: any,
    onClose: any,
    onApply?: any,
    onClear?: any,
    title: any,
    hideCloseButton?: boolean
    primaryButtonTitle?: string,
    primaryButtonType?: string,
    primaryButtonStyle?: string,
    secondaryButtonStyle?: string,
    primaryButtonRightIcon?: any,
    primaryButtonLeftIcon?: any,
    primaryButtonDisable?: boolean,
    secondaryButtonRightIcon?: any,
    secondaryButtonLeftIcon?: any,
    secondaryButtonTitle?: string,
    secondaryButtonLoading?: boolean,
    styleName?: any,
    loading?: boolean,
    csvSample?: boolean,
    secondaryButtonDisable?: boolean,
    onClickDownloadSample?: any,
    actionButtonStyle?: any,
    orderCsvSampleMessage?: string
    actionButtonLabel?: any
    actionButtonValue?: any
    fuelQuantity?: number
    fuelPrice?: number
}

function ModalContainer(props: ModalContainerProps) {
    const { title, children, open = false, onClose, onApply, onClear, csvSample, onClickDownloadSample, orderCsvSampleMessage,
        primaryButtonTitle, secondaryButtonTitle, secondaryButtonLoading, primaryButtonLeftIcon, primaryButtonType, actionButtonStyle, primaryButtonStyle, secondaryButtonStyle,
        styleName, secondaryButtonRightIcon, secondaryButtonLeftIcon, primaryButtonRightIcon, loading, secondaryButtonDisable, primaryButtonDisable, hideCloseButton = false, actionButtonLabel, actionButtonValue, fuelQuantity, fuelPrice } = props;

    const descriptionElementRef = React.useRef<HTMLElement>(null);
    React.useEffect(() => {
        if (open) {
            const { current: descriptionElement } = descriptionElementRef;
            if (descriptionElement !== null) {
                descriptionElement.focus();
            }
        }
    }, [open]);

    return (
        <div>
            <Dialog
                open={open}
                onClose={() => {
                    return false;
                }}
                scroll={'paper'}
                aria-labelledby="scroll-dialog-title"
                aria-describedby="scroll-dialog-description"
                className={"custom-modal " + styleName}
            >
                <DialogTitle id="scroll-dialog-title">
                    <span className="text-truncate">{title}</span>
                    {!hideCloseButton &&
                        <IconButton
                            aria-label="close"
                            onClick={onClose}>
                            <Close />
                        </IconButton>}
                </DialogTitle>
                <DialogContent>
                    {children}
                </DialogContent>
                <DialogActions
                    className={actionButtonStyle ? actionButtonStyle : ""}
                >
                    {actionButtonLabel &&
                        <div className="action-btn-label total-amount">
                            <p className="total-amount-label m-0">{actionButtonValue}</p>
                            <p className="total-amount-value m-0">₹ {(fuelQuantity && fuelPrice) ? (fuelQuantity * fuelPrice).toFixed(2) : 0} </p>
                        </div>
                    }
                    {!isNullValue(secondaryButtonTitle) && <Button
                        title={secondaryButtonTitle}
                        buttonStyle={secondaryButtonStyle ? secondaryButtonStyle : "btn-orange ok-btn"}
                        rightIcon={secondaryButtonRightIcon}
                        leftIcon={secondaryButtonLeftIcon}
                        disable={secondaryButtonDisable}
                        onClick={onClear}
                        loading={secondaryButtonLoading}
                    />
                    }
                    <>
                        {csvSample && <span className="download-csv"
                            onClick={onClickDownloadSample}
                        >
                            {orderCsvSampleMessage !== " " && (<> < GetApp />{orderCsvSampleMessage || "Download CSV sample"}</>)} </span>}
                        {primaryButtonTitle && <Button
                            title={primaryButtonTitle}
                            onClick={onApply}
                            loading={loading}
                            rightIcon={primaryButtonRightIcon}
                            leftIcon={primaryButtonLeftIcon}
                            type={primaryButtonType}
                            buttonStyle={primaryButtonStyle ? primaryButtonStyle : "btn-blue"}
                            primaryButton={true}
                            disable={primaryButtonDisable}
                        />
                        }

                    </>
                </DialogActions>
            </Dialog>
        </div>
    );
}

export default ModalContainer;
