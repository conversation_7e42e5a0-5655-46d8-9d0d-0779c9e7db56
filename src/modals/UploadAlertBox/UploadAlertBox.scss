/* message modal */
.message-modal.custom-modal {
	.MuiDialogContent-root {
		padding: 24px 10px 0;
		.MuiSvgIcon-root {
			font-size: 56px;
			line-height: 1.09;
			color: #ED1B00;
			opacity: 0.9;
		}
		.content-heading {
			line-height: 1.18;
			text-align: center;
			margin: 10px 0 0;
			font-size: 24px;
			font-weight: 500;
		}
		.content-heading.success {
			color: #1FC900;
		}
		.content-heading.error {
			color: #ED1B00;
		}
		.content-heading.info {
			color: #f7931e;
		}
		label {
			line-height: 20px;
			margin-bottom: 0;
			font-size: 17px;
			font-weight: 300;
			color: #768a9e;
			margin-top: 10px;
			max-width: 400px;
			word-break: break-word;
			white-space: break-spaces;
		}
	}
	.MuiDialog-paper {
		min-width: 450px;
		max-width: 450px;
	}
	.MuiDialogActions-root {
		justify-content: center !important;
		padding: 32px 0px 20px;
	}
	.center {
		.MuiDialogActions-root {
			.MuiDialogActions-spacing {
				justify-content: center;
			}
		}
	}
}
.message-modal.custom-modal.success {
	.MuiDialogContent-root {
		.MuiSvgIcon-root {
			color: #1FC900;
		}
	}
}
.message-modal.custom-modal.error {
	.MuiDialogContent-root {
		.MuiSvgIcon-root {
			color: #ED1B00;
		}
	}
}
.message-modal.custom-modal.info {
	.MuiDialogContent-root {
		.MuiSvgIcon-root {
			color: #f7931e;
		}
	}
}
@media (max-width: 767px) {
	.message-modal.custom-modal {
		.MuiDialog-paper {
			min-width: 100%;
		}
	}
}
.upload-box-modal{
    border-radius: 10px;
    border: solid 1px #F77E1E;
    background-color: #fff;
    text-align: center;
    padding: 16px 0 20px;
    color: #768a9e;
    margin: 30px 20px 0px;
}
.upload-box-modal p{
    font-size: 13px;
    margin-bottom: 10px;
    color:#133751;
}
.upload-box-modal p + span {
    font-size: 10px;
    display: block;
    margin-bottom: 8px;
    line-height: 1.1;
}
.upload-box-modal .file-upload-btn,
.upload-box-modal .file-upload-btn:hover {
    width: auto;
}
.file-upload-wrap span{
    text-transform: uppercase;
}
.okBtn-grey{
	background: linear-gradient(270deg, #f9f9f9, #eaeff3);
    width: 81px;
    border: 1px solid #7070701D;
	font-size: 16px;
}
.upload-success.message-modal.custom-modal .MuiDialogActions-root {
    padding: 15px 0 25px;
}