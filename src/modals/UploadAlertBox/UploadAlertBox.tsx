
import { CheckCircle } from "@material-ui/icons";
import React from "react";
import { shallowEqual, useDispatch, useSelector } from "react-redux";
import ModalContainer from "../../modals/ModalContainer";
import { hideUploadAlert, showUploadDocumentsModal } from '../../redux/actions/AppActions';
import "./UploadAlertBox.scss";
import { useHistory } from "react-router";
import { requestDetailsRoute } from "../../base/constant/RoutePath";
import Button from "../../component/widgets/button/Button";

function UploadAlertBox() {
    const appDispatch = useDispatch();
    const appReducer = useSelector((state: any) => state.appReducer, shallowEqual);
    const history = useHistory();
   
    return (
        (appReducer.showUploadAlert && appReducer.alertMessage && appReducer.alertMessage !== "" && <ModalContainer
            title={"Success"}
            secondaryButtonTitle={"OK"}
            secondaryButtonStyle={"okBtn-grey"}
            onClear={() => {
                appDispatch(hideUploadAlert())
            }}
            open={appReducer.showUploadAlert}
            onClose={() => {
                appDispatch(hideUploadAlert());
            }}
            styleName={`message-modal success upload-success` }
        >
            <div className="text-center">
                <CheckCircle />
                <h2 className={"content-heading success"}>{"Success"}</h2>
                <label>{appReducer.alertMessage}</label>
                <div className="upload-box upload-box-modal">
                    <img src="/images/storage.svg" alt="upload" />
                    <p>Upload any supporting document</p>
                    <div className="file-upload-wrap">
                        <label htmlFor="contained-button-file" className="m-0">
                        <Button
                            buttonStyle="btn-orange "
                            title={"UPLOAD DOCUMENT"}
                            onClick={()=>{
                                history.push({
                                    pathname: requestDetailsRoute + appReducer?.paymentId,
                                });
                                appDispatch(showUploadDocumentsModal());
                                appDispatch(hideUploadAlert());
                            }}
                        />
                        </label>
                    </div>
                </div>
            </div>
        </ModalContainer>) || null
    );
}
export default UploadAlertBox;