import React, { useEffect } from 'react';
import { Button, CircularProgress } from "@material-ui/core";
import { makeStyles, createStyles, Theme } from '@material-ui/core/styles';
import { useDispatch } from 'react-redux';
import { Done, Publish } from '@material-ui/icons';
import { showAlert } from '../../redux/actions/AppActions';
import UploadIcon from './UploadIcon';
import "./UploadDocumentModal.scss";
import { uploadRequestDocuments } from '../../serviceActions/RequestServiceAction';
import { getUploadDocumentsParams } from '../../pages/requestModule/requestModuleUtility/RequestUtility';
import { isNullValue, isNullValueOrZero, isObjectEmpty } from '../../base/utility/StringUtils';
import { uploadDocumentMapperEnum } from '../../base/constant/ArrayList';
import { InfoTooltip } from '../../component/widgets/tooltip/InfoTooltip';

interface UploadButtonProps {
    elementType: string;
    paymentRequestId: string;
    loading: boolean;
    setLoading: any;
    uploadDocuments: any;
    setUploadDocuments: any;
    open: boolean;
    setDisableLess: any;
    setDisableSubmit: any;
    walletCode: any;
    repairId?: string
}

const useStyles = makeStyles((theme: Theme) =>
    createStyles({
        input: {
            display: 'none',
        },
        buttonProgress: {
            color: "#006cc9",
            position: 'absolute',
            top: '50%',
            right: '0',
            marginTop: 0,
            marginLeft: -12,
          }
    }),
);
function UploadButton(props: UploadButtonProps) {
    const { elementType, paymentRequestId, loading, setLoading, uploadDocuments, setUploadDocuments, open, setDisableLess, setDisableSubmit, walletCode, repairId } = props;
    const documentKey: string = uploadDocumentMapperEnum[elementType as keyof typeof uploadDocumentMapperEnum];
    const [totalSize, setTotalSize] = React.useState<any>(Number(!isNullValue(uploadDocuments?.[documentKey]?.[0]?.totalDocumentSize) ? uploadDocuments?.[documentKey]?.[0]?.totalDocumentSize : 0));
    const [totalDocs, setTotalDocs] = React.useState<any>(Number(!isNullValue(uploadDocuments?.[documentKey]?.[0]?.totalDocumentCount) ? uploadDocuments?.[documentKey]?.[0]?.totalDocumentCount : 0 ));
    const [showTooltip, setShowTooltip] = React.useState<boolean>(false);
    const [showLoader, setShowLoader] = React.useState<boolean>(false);
    const [showTick, setShowTick] = React.useState<boolean>(false);
    const classes = useStyles();
    const appDispatch = useDispatch();

    useEffect(()=>{
        if(open){
            if(totalDocs && totalDocs>=4){
                setShowTooltip(true);
            }else{
                setShowTooltip(false);
            }
        }
    },[open, totalDocs])

    useEffect(()=>{
        if(open){
            setShowTick(false);
            setShowLoader(false);

        }
    },[open])

    const uploadedDocResponse = (responseArray: any) => {
        const newResponseArray: any = [];
        responseArray && responseArray.forEach((item: any) => {
            let newResponse = {
                documentLink: item?.document_link,
                documentSize: item?.document_size,
                uuid: item?.uuid
            };
            newResponseArray.push(newResponse);
        });
        
        return newResponseArray;
    }

    return(
        <>
            <div className="upload-box text-right">
                <div className="file-upload-wrap">
                    <input
                        accept="image/jpg,image/jpeg,application/pdf,image/png"
                        className={classes.input}
                        id={`contained-button-file ${elementType}`}
                        type="file"
                        onChange={(event: any) => {
                            setLoading(true);
                            if (event && event.target && event.target.files && event.target.files.length!==0) {
                                var tempTotalDoc = event.target.files.length;
                                if(totalDocs<4 && ((tempTotalDoc+totalDocs)<=4)){
                                    setShowTick(false);
                                    setShowLoader(true);
                                    var tempSize = 0;
                                    for(const file of event.target.files){
                                        tempSize += Number(file.size);
                                    }
                                    if (totalSize <= 10*1024*1024 && ((totalSize+tempSize)<=10*1024*1024)) {
                                        setDisableLess(true);
                                        let queryParams = getUploadDocumentsParams(event.target.files, paymentRequestId, elementType, walletCode)
                                        appDispatch(uploadRequestDocuments(queryParams)).then((resp: any)=>{
                                            let tempDocs: any = !isObjectEmpty(uploadDocuments) ? {...uploadDocuments} : {};
                                            if(resp){
                                                setTimeout(()=>{
                                                    tempDocs[documentKey] = !isNullValueOrZero(uploadDocuments?.[documentKey]) ?
                                                    (
                                                        [...uploadDocuments[documentKey], ...uploadedDocResponse(resp?.[elementType])]
                                                    ) : (
                                                        [...resp?.[elementType]]
                                                    );
                                                    setUploadDocuments(tempDocs);
                                                    setShowTick(true);
                                                    setShowLoader(false);
                                                    setLoading(false);
                                                    if (tempDocs?.[documentKey]?.length) {
                                                        setDisableSubmit(false);
                                                    } else {
                                                        setDisableSubmit(true);
                                                    }
                                                },5000)
                                                setTotalDocs(tempTotalDoc+totalDocs);
                                                setTotalSize(totalSize+tempSize);
                                            }else{
                                                setLoading(false);
                                                setShowTick(false);
                                                setShowLoader(false);
                                                setDisableLess(false);
                                            }
                                        })
                                    } else {
                                        appDispatch(showAlert("Total file size of 10 MB is allowed"));
                                        setLoading(false);
                                        setShowTick(false);
                                        setShowLoader(false);
                                    }
                                }else{
                                    appDispatch(showAlert("Maximum 4 files can be uploaded"));
                                    setLoading(false);
                                    setShowTick(false);
                                    setShowLoader(false);
                                }
                                event.target.value="";
                            }
                        }}
                        multiple={true}
                        disabled={loading || showTooltip}
                    />
                    <span className="file-name">
                        {
                            !isObjectEmpty(uploadDocuments) && (uploadDocuments[documentKey]!==undefined) && (
                                <>
                                    {
                                        uploadDocuments[documentKey].map((el:any)=>(
                                            <UploadIcon
                                                key={el.uuid}
                                                elementType={elementType}
                                                documentKey={documentKey}
                                                paymentRequestId={paymentRequestId}
                                                loading={loading}
                                                setLoading={setLoading}
                                                uploadDocuments={uploadDocuments}
                                                setUploadDocuments={setUploadDocuments}
                                                elementDoc={el}
                                                totalDocs={totalDocs}
                                                totalSize={totalSize}
                                                setTotalSize={setTotalSize}
                                                setTotalDocs={setTotalDocs}
                                                setShowLoader={setShowLoader}
                                                setShowTick={setShowTick}
                                                setDisableLess={setDisableLess}
                                                setDisableSubmit={setDisableSubmit}
                                                repairId={repairId}
                                            />
                                        ))
                                    }
                                </>
                            )
                        }
                    </span>
                    <label htmlFor={`contained-button-file ${elementType}`} className={`m-0 ${(loading || showTooltip) ? 'disable-btn' : ""}`}>
                        <Button disabled={loading || showTooltip} variant="outlined" className="btn-grey-bg " component="span">
                            <Publish /> <span className='upload-button'>UPLOAD</span>
                        </Button>
                    </label>

                    <span className='max-docs-limit-tooltip'>
                        {showTooltip && <InfoTooltip
                            arrow={true}
                            style={{
                                tooltip:{
                                   maxWidth: '100px'
                                },
                                arrow:{
                                    fontSize: '16px',
                                    color: '#fff',
                                    top:'27px'
                                },
                            }}
                            placement="top" title={"You can upload maximum 4 files."} />}
                    </span>
                    <span className='upload-done'>
                        {showTick ? <Done />  : (showLoader ?  <CircularProgress size={24} className={classes.buttonProgress} /> : <></> )}
                    </span>
                </div>
            </div>
        </>
    )
}
export default UploadButton;
