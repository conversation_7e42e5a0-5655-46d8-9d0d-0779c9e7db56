.request-upload-modal {
    .MuiDialog-paper{
        min-width: 650px;
        max-width: 650px;
        @media screen and (max-width:767px) {
            min-width: 100%;
        }
        .MuiDialogContent-root {
            padding: 5px 24px 0;
            @media screen and (max-width:767px) {
                padding: 5px 13px 0;
            }
        }
        .MuiDialogActions-root{
            padding: 20px 24px;
        }
        .table-detail-listing {
            .table-list-view {
                .MuiTableRow-root {
                    .MuiTableCell-root{
                        padding: 0;
                    }
                }
            }
            .upload-more {
                color: #F7931E;
                margin-top: 10px;
                font-size: 13px;
                cursor: pointer;
                svg {
                    font-size: 1.2rem;
                    vertical-align: top;
                }
            }

            .upload-more-disabled {
                opacity: 0.5;
                color: #F7931E;
                margin-top: 10px;
                font-size: 13px;
                svg {
                    font-size: 1.2rem;
                    vertical-align: top;
                }
            }
        }
    }
    .file-upload-wrap {
        margin-right: 40px;
        .file-inner-name {
            border-radius: 4px;
            position: relative;
            margin-right: 15px;
            button {
                background: none;
                border: none;
                position: absolute;
                top: -13px;
                right: -12px;
                svg{
                    font-size: 20px;
                    background: #fff;
                }
            }
        }
        .MuiButton-outlined {
            border: 2px solid #DFE4E6;
            border-radius: 5px;
            padding: 7px 15px;
            .MuiSvgIcon-root {
                font-size: 19px;
                color: #707070;
            }
            .upload-button {
                font-size: 12px;
                line-height: 12px;
                color: #5F5F5F;
                margin-left: 2px;
            }
        }
    }
}
.upload-box{
    position: relative;
}
.max-docs-limit-tooltip{
    position: absolute;
    top: 12px;
    right: 25px;
    opacity: 0;
    span.tool-tip-icon{
        padding: 10px 40px;
    }
}
.upload-done{
    position: absolute;
    top: 7px;
    right: 6px;
    .MuiSvgIcon-root{
        fill: #3DB246;
    }
}
.disable-btn{
    opacity: .5;
}
.table-list-view .MuiTableRow-root .MuiTableCell-root{
    @media screen and (max-width:767px) {
        max-width: 370px;
    }
}