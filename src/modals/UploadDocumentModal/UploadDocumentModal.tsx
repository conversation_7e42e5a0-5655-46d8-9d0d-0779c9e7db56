import React, { useEffect } from 'react';
import ModalContainer from "../ModalContainer";
import { CheckCircleSharp, KeyboardArrowDown, KeyboardArrowUp } from '@material-ui/icons';
import TableList from '../../component/widgets/tableView/TableList';
import UploadButton from './UploadButton';
import { getUploadFields, getUploadFieldsForReactiveRepairMaintenance } from '../../pages/requestModule/requestModuleUtility/RequestUtility';

interface UploadDocumentModalProps {
    open: boolean
    onClose: any
    onApply?: any
    title: string
    paymentRequestId: any
    uploadDocuments: any
    setUploadDocuments: any
    walletCode: string
    repairId: string
    isUploadMandatory?: boolean
    isGoboltCashReconcileRequest?: boolean
    isReactiveRepairRequest?: boolean
}

function UploadDocumentModal(props: UploadDocumentModalProps) {
    const { open, onClose, onApply, title, paymentRequestId, uploadDocuments, setUploadDocuments,
        walletCode, repairId, isUploadMandatory = false, isGoboltCashReconcileRequest = false,
        isReactiveRepairRequest = false
    } = props;
    const [openMore, setOpenMore] = React.useState<boolean>(false);
    const [loading, setLoading] = React.useState<boolean>(false);
    const [disableLess, setDisableLess] = React.useState<boolean>(false);
    const [disableSubmit, setDisableSubmit] = React.useState<boolean>(false);
    const uploadFields = isReactiveRepairRequest
        ? getUploadFieldsForReactiveRepairMaintenance()
        : getUploadFields(walletCode, repairId, isGoboltCashReconcileRequest);
    
    useEffect(()=>{
        setOpenMore(false);
        !open && setDisableLess(false);
        !openMore && setDisableSubmit(true);
        if (open && uploadDocuments?.goboltCashReconcile?.length) {
            setDisableSubmit(false);
        }
    },[open])

    return (
        <ModalContainer
            title={title}
            styleName={"request-upload-modal"}
            primaryButtonTitle={"Submit"}
            primaryButtonLeftIcon={<CheckCircleSharp/>}
            primaryButtonDisable={disableSubmit}
            open={open}
            onClose={() => {
                if(!loading){
                    onClose();
                }
            }}
            onApply={() => {
                if (isGoboltCashReconcileRequest) {
                    setLoading(true);
                    onApply(setLoading);
                }else {
                    if(!loading){
                        onApply()
                        onClose();
                    }
                }
            }}
            actionButtonStyle={"right"}
            loading={loading}
        >
            <div className="table-detail-listing inp-tableList scroll-table">
                <TableList
                    tableColumns={getColumns(paymentRequestId, loading, setLoading, uploadDocuments, setUploadDocuments, open, setDisableLess, setDisableSubmit, walletCode, repairId, isUploadMandatory)}
                    currentPage={0}
                    rowsPerPage={25}
                    rowsPerPageOptions={[]}
                    listData={openMore ? uploadFields : uploadFields?.slice(0,3)}
                    onChangePage={(event: any, page: number) => {
                    }}
                    onRowsPerPageChange={(event: any) => {
                    }}
                    showTableHeader={false}
                />
                {uploadFields && uploadFields?.length > 1  && (openMore ? <div className={!disableLess ? 'upload-more' : 'upload-more-disabled'} onClick={()=>{
                        !disableLess && setOpenMore(false);
                    }}>
                        <KeyboardArrowUp />Less
                    </div> : (
                        <div className='upload-more' onClick={()=>{
                            setOpenMore(true);
                            setDisableLess(false);
                        }}>
                            <KeyboardArrowDown/> More
                        </div>
                    )
                )}
            </div>
        </ModalContainer>
    );
}

function getColumns(paymentRequestId: string, loading: boolean, setLoading: any, uploadDocuments: any, setUploadDocuments: any, open: boolean, setDisableLess: any, setDisableSubmit: any, walletCode: string, repairId?: string, isMandatory: boolean = false){
    let columnNames: Array<any> = [
        {id: 'label', label: '', isMandatory: isMandatory, format: (value: any) => value || "NA"},
        {id: 'button', label: '', format: (value: any) => value || "NA",
            customView: (element:any) => {
                return (
                    (
                        <UploadButton
                            elementType={element.value}
                            paymentRequestId={paymentRequestId}
                            loading={loading}
                            setLoading={setLoading}
                            uploadDocuments={uploadDocuments}
                            setUploadDocuments={setUploadDocuments}
                            open={open}
                            setDisableLess={setDisableLess}
                            setDisableSubmit={setDisableSubmit}
                            walletCode={walletCode}
                            repairId={repairId}
                        />
                    )
                )
            }
        }
    ]
    return columnNames;
}

export default UploadDocumentModal;
