import { Cancel } from '@material-ui/icons';
import React from "react";
import { useDispatch } from 'react-redux';
import { getDeleteDocumentParams } from '../../pages/requestModule/requestModuleUtility/RequestUtility';
import { showAlert } from '../../redux/actions/AppActions';
import { deleteRequestDocuments } from '../../serviceActions/RequestServiceAction';
import { isNullValue } from '../../base/utility/StringUtils';
interface UploadIconProps {
    elementType: string;
    documentKey: any;
    paymentRequestId: string;
    loading: boolean;
    setLoading: any;
    uploadDocuments: any;
    setUploadDocuments: any;
    elementDoc: any;
    totalDocs: any;
    totalSize: any;
    setTotalDocs: any;
    setTotalSize: any;
    setShowLoader: any;
    setShowTick: any;
    setDisableLess: any;
    setDisableSubmit:any;
    repairId?: string
}
function UploadIcon(props: UploadIconProps) {
    const { elementType, documentKey, paymentRequestId, loading, setLoading, uploadDocuments, setUploadDocuments, elementDoc, setTotalDocs, setTotalSize, totalDocs, totalSize, setShowTick, setShowLoader, setDisableLess, setDisableSubmit, repairId } = props
    const appDispatch = useDispatch();
    return(
        <span id={elementDoc.uuid} className="file-inner-name">
            <img src="/images/InsertPhotoOutlined.svg" alt="InsertPhotoOutlined" />
            {!loading && (
                    <button
                        style={{"background":'none',"border":'none'}}
                        onClick={()=>{
                            setLoading(true);
                            setShowLoader(true);
                            setShowTick(false);
                            setDisableLess(true);
                            let queryParams = getDeleteDocumentParams(elementDoc, paymentRequestId, elementType);
                            if(!isNullValue(repairId)){
                                queryParams['repair_id'] = repairId;
                                queryParams['document_link'] = elementDoc['documentLink'];
                            }
                            appDispatch(deleteRequestDocuments(queryParams)).then((response: any)=>{
                                if(response && response.code===200){
                                    let tempDocs = {...uploadDocuments}
                                    tempDocs[documentKey] = tempDocs[documentKey].filter((item: any)=>{
                                        return item.uuid!==elementDoc.uuid;
                                    })
                                    setUploadDocuments(tempDocs);
                                    setTimeout(()=>{
                                        setLoading(false);
                                        setShowLoader(false);
                                        setShowTick(true);
                                        if (tempDocs?.[documentKey]?.length) {
                                            setDisableSubmit(false);
                                        } else {
                                            setDisableSubmit(true);
                                        }
                                    },5000);
                                    const tempDocLen = totalDocs-1;
                                    setTotalDocs(tempDocLen);
                                    const tempDocSize = totalSize - Number(elementDoc?.documentSize);
                                    setTotalSize(tempDocSize);
                                }else{
                                    if (response) {
                                        if (!isNullValue(response?.message)) {
                                            appDispatch(showAlert(response.message));
                                        } else {
                                            appDispatch(showAlert("Unable to delete documents"));
                                        }
                                    }
                                    setLoading(false);
                                    setShowLoader(false);
                                    setShowTick(false);
                                    setDisableLess(false);
                                }
                            })
                        }}
                    >
                        <Cancel color='disabled' />
                    </button>
                )
            }
        </span>
    )
}
export default UploadIcon;
