import React from "react";
import ModalContainer from "../ModalContainer";
import "./ViewDocumentModal.scss";
import "react-responsive-carousel/lib/styles/carousel.min.css"; // requires a loader
import { Carousel } from 'react-responsive-carousel';

interface ViewDocumentModalProps {
    open: boolean,
    onClose: any
    fileLinks?: any
}
function ViewDocumentModal(props: ViewDocumentModalProps) {
    const { open, onClose, fileLinks } = props;
    const [loading, setLoading] = React.useState<boolean>(false);
    return (
        <ModalContainer
            open={open}
            loading={loading}
            onClose={onClose}
            onApply={() => {
                setLoading(true);
            }}
            styleName={"pod-upload-modal"}
            actionButtonStyle="center"
            title=""
        >
            <Carousel
                infiniteLoop={true}
                showIndicators={false}
                showStatus={false}
                autoPlay={false}
            >
                {fileLinks.map((item : any, i: any) => (
                    <iframe key={i} src={item.documentLink} width="100%" height="500px"/>
                ))}
            </Carousel>
        </ModalContainer>
    );
}
export default ViewDocumentModal;