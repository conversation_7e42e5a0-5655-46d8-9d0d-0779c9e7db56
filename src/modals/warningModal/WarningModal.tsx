import React from "react";
import ModalContainer from "../ModalContainer";
import './WarningModal.css';

interface WarningModalProps {
    open: boolean
    onClose: any
    onSuccess: any,
    warningMessage: any,
    primaryButtonTitle: any
    secondaryuttonTitle: any,
    successLoader?: boolean
    secondaryButtonLeftIcon?: any,
    primaryButtonLeftIcon?: any,
    primaryButtonStyle?: string,
    pollingLoader?: boolean

}

function WarningModal(props: WarningModalProps) {
    const { open, onClose, warningMessage, primaryButtonTitle, secondaryuttonTitle, onSuccess, successLoader, secondaryButtonLeftIcon, primaryButtonLeftIcon, primaryButtonStyle, pollingLoader } = props;
    return (
        <ModalContainer
            title="Confirm Deletion"
            secondaryButtonTitle={secondaryuttonTitle}
            primaryButtonTitle={primaryButtonTitle}
            primaryButtonStyle={primaryButtonStyle}
            open={open}
            secondaryButtonLeftIcon={secondaryButtonLeftIcon}
            primaryButtonLeftIcon={primaryButtonLeftIcon}
            loading={successLoader || pollingLoader}
            onApply={onSuccess}
            onClear={() => {
                onClose();
            }}
            onClose={() => {
                onClose();
            }}
            styleName={"warning-modal"}
        >
            <div className="warning-container text-center">
                <img src="/images/warning-icon.png" alt="truck-icon" />
                <h3>Warning!</h3>
                <h4>{warningMessage}</h4>
            </div>

        </ModalContainer>
    );
}

export default WarningModal;
