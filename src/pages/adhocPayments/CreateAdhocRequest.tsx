import React, { useReducer, useRef, ChangeEvent } from 'react'
import <PERSON> from "papapar<PERSON>";
import Filter from '../../component/filter/Filter'
import { isMobile } from '../../base/utility/ViewUtils'
import { AccountBalance, CheckCircle, EmailOutlined, KeyboardBackspace, LocalPhoneOutlined, Publish, GetApp } from '@material-ui/icons'
import { useHistory } from 'react-router-dom'
import PageContainer from '../../component/pageContainer/PageContainer'
import { Avatar, Card, CardContent, Chip, List, ListItem, ListItemAvatar, ListItemText } from '@material-ui/core'
import AutoComplete from '../../component/widgets/AutoComplete'
import { OverflowTip } from '../../component/widgets/tooltip/OverFlowToolTip'
import { convertToTitleCase, isNullValue, isObjectEmpty } from '../../base/utility/StringUtils'
import { OptionType } from '../../component/widgets/widgetsInterfaces'
import NumberEditText from '../../component/widgets/NumberEditText'
import EditText from '../../component/widgets/EditText'
import Button from '../../component/widgets/button/Button'
import FileUploader from '../../component/fileUpload/FileUploader'
import CreateAdhocRequestReducer, { CREATE_ADHOC_REQUEST_STATE, CreateAdhocRequestState } from './payables/reducers/createAdhocRequest/CreateAdhocRequestReducer'
import { setErrors, setFormData, setValue } from './payables/reducers/createAdhocRequest/CreateAdhocRequestActions'
import { PAN_REGEX } from '../../base/moduleUtility/ConstantValues'
import { adhocPaymentCategoryOptions } from '../../base/constant/ArrayList'
import { payablesListingRoute } from '../../base/constant/RoutePath'
import { useDispatch } from 'react-redux'
import FileAction from '../../component/fileAction/FileAction'
import DownloadCSVModal from '../../component/downloadCSV/DownloadCSVModal'
import { openInNewTab } from './adhocPaymentUtility/adhocPaymentUtility'
import { showAlert } from '../../redux/actions/AppActions';

const contactData = [
    { label: 'John Doe', value: 'john_doe', data: { name: 'John Doe', contact: '+91 **********', email: '<EMAIL>', type: 'Vendor' } },
    { label: 'Jane Smith', value: 'jane_smith', data: { name: 'Jane Smith', contact: '+91 **********', email: '<EMAIL>', type: 'Customer' } },
    { label: 'Alice Johnson', value: 'alice_johnson', data: { name: 'Alice Johnson', contact: '+91 **********', email: '<EMAIL>', type: 'Vendor' } },
]

const paymentMethodData = [
    { label: 'UPI', value: 'upi', data: { accountType: 'vpa', vpa: { address: 'john.doe@upi' } } },
    { label: 'Bank Account', value: 'bank_account', data: { accountType: 'bank_account', bankAccount: { accountNumber: '**********', bankName: 'ICICI Bank' } } },
]

const validateCreateAdhocRequest = (formData: CreateAdhocRequestState['formData']): CreateAdhocRequestState['errors'] => {
    const errors: CreateAdhocRequestState['errors'] = {};
    if (isNullValue(formData.contact)) {
        errors.contact = "Contact is required";
    }
    if (isNullValue(formData.paymentMethod)) {
        errors.paymentMethod = "Payment Method is required";
    }
    if (isNullValue(formData.amount)) {
        errors.amount = "Amount is required";
    }
    if (isNullValue(formData.narration)) {
        errors.narration = "Narration is required";
    }
    if (isNullValue(formData.paymentCategory)) {
        errors.paymentCategory = "Payment Category is required";
    }
    if (!isNullValue(formData.panNumber) && !PAN_REGEX.test(formData.panNumber)) {
        errors.panNumber = "PAN Number is not valid";
    }
    if (isObjectEmpty(formData.documents) || formData.documents.totalFilesCount === 0) {
        errors.documents = "Document is required";
    }

    return errors;
}

const CreateAdhocRequest = () => {
    const appDispatch = useDispatch();
    const history = useHistory()
    const [state = CREATE_ADHOC_REQUEST_STATE, dispatch] = useReducer(
        CreateAdhocRequestReducer,
        CREATE_ADHOC_REQUEST_STATE
    );

    const { formData, errors, contacts, paymentMethods, isContactLoading,
        isPaymentMethodLoading, isDocumentUploading, isCreateRequestLoading,
        showDownloadCsvSampleModal, isBulkCreateLoading
    } = state;

    const bulkCreateFileRef = useRef<HTMLInputElement>(null);

    const handleCreateRequest = () => {
        const errors = validateCreateAdhocRequest(formData);
        if (Object.keys(errors).length > 0) {
            dispatch(setErrors(errors));
            return;
        }

        dispatch(setValue('isCreateRequestLoading', true));
        // TODO: API call
        setTimeout(() => {
            dispatch(setValue('isCreateRequestLoading', false));
            console.log("Adhoc request created successfully with data:", formData);
            history.push(payablesListingRoute);
        }, 1000);
    }

    const handleFilesUpload = (files: File[], filesSize: number): Promise<{ success: boolean, data: any }> => {
        dispatch(setValue('isDocumentUploading', true));
        return new Promise((resolve) => {
            //TODO: API call to upload files
            setTimeout(() => {
                dispatch(setValue('isDocumentUploading', false));
                const updatedDocuments = {
                    files: [...formData.documents.files, ...files],
                    totalSize: formData.documents.totalSize + filesSize,
                    totalFilesCount: formData.documents.totalFilesCount + files.length,
                };
                dispatch(setFormData('documents', updatedDocuments));
                resolve({ success: true, data: [] });
            }, 1000);
        });
    }

    const handleFileDelete = (file: File): Promise<{ success: boolean, data: any }> => {
        dispatch(setValue('isDocumentUploading', true));
        return new Promise((resolve) => {
            //TODO: API call to delete file
            setTimeout(() => {
                dispatch(setValue('isDocumentUploading', false));
                const updatedFiles = formData.documents.files.filter(f => f.name !== file.name);
                const updatedDocuments = {
                    files: updatedFiles,
                    totalSize: updatedFiles.reduce((acc, f) => acc + f.size, 0),
                    totalFilesCount: updatedFiles.length,
                };
                dispatch(setFormData('documents', updatedDocuments));
                resolve({ success: true, data: [] });
            }, 1000);
        });
    }

    const handleApplyDownload = (url: string) => {
        dispatch(setValue('showDownloadCsvSampleModal', false));
        openInNewTab(url);
    };

    const handleBulkCreateFileUpload = (event: ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
    
        if (!file) return;
    
        const requiredHeaders = [
            "Contact", "Payment Method", "Amount", "Bill Number", "Narration", 
            "Payment Category", "PAN Number", "Reference ID",
        ]; // Expected headers
    
        const columnKeyMapping: Record<string, string> = {
            "Contact": "contact",
            "Payment Method": "paymentMethod",
            "Amount": "amount",
            "Bill Number": "billNo",
            "Narration": "narration",
            "Payment Category": "paymentCategory",
            "PAN Number": "panNumber",
            "Reference ID": "referenceId",
        };
    
        Papa.parse(file, {
            header: true,
            skipEmptyLines: true,
            complete: ({ data }) => {
                if (!data.length) {
                    appDispatch(showAlert("CSV is empty or incorrectly formatted."));
                    return;
                }
    
                const headers = Object.keys(data[0] as Record<string, unknown>).map(header => header.trim()); // Extract headers from first row
    
                // Validate required headers
                const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));
                if (missingHeaders.length) {
                    appDispatch(showAlert(`Invalid CSV format! Missing headers: ${missingHeaders.join(", ")}`));
                    return;
                }
    
                // Map old keys to new keys
                const bulkCreateData = data.map(row => {
                    return Object.entries(row as Record<string, string>).reduce((acc, [key, value]) => {
                        const newKey = columnKeyMapping[key.trim()] || key;
                        acc[newKey] = value.trim();
                        return acc;
                    }, {} as Record<string, string>);
                });
    
                const bulkCreatePayload = {
                    //TODO
                 };

                 dispatch(setValue('isBulkCreateLoading', true));
                 //TODO
                //  appDispatch(createBulkAdhocRequest(bulkCreatePayload)).then((response: any) => {
                //     if (response && response.code === 200) {
                //         appDispatch(showAlert(response.message));
                //         onUpload();
                //     }
                //     dispatch(setValue('isBulkCreateLoading', false));
                //     if (bulkCreateFileRef.current) {
                //         bulkCreateFileRef.current.value = '';
                //     }
                //  });
            },
            error: (error) => console.error("Parsing error:", error),
        });
    };

    return (
        <div className='create-request-wrapper'>
            <DownloadCSVModal
                open={showDownloadCsvSampleModal}
                items={[
                    {
                        label: "Razorpay Bank Account Sample Download",
                        // href: "/sample/adhoc_payment_sample.csv",
                        onClick: (url: string) => { handleApplyDownload(url) }
                    },
                    {
                        label: "Razorpay UPI Sample Download",
                        onClick: (url: string) => { handleApplyDownload(url) }
                    },
                    {
                        label: "Entity Payment Sample Download",
                        onClick: (url: string) => { handleApplyDownload(url) }
                    }
                ]}
                onClose={() => dispatch(setValue('showDownloadCsvSampleModal', false))}
            />
            <Filter
                pageTitle={"Create Request"}
                buttonTitle={isMobile ? " " : "Back"}
                leftIcon={<KeyboardBackspace />}
                buttonStyle={isMobile ? "btn-detail-mob" : "btn-detail btn-rounded"}
                onClick={() => {
                    history.goBack()
                }}
            >
                <FileAction
                    options={[
                        {
                            menuTitle: "Bulk Upload",
                            Icon: Publish,
                            className: "menu-file-top menu-file-outline",
                            onClick: () => bulkCreateFileRef?.current?.click()
                        },
                        {
                            menuTitle: "Download CSV Sample",
                            Icon: GetApp,
                            className: "menu-file-bottom menu-file-outline",
                            onClick: () => {
                                dispatch(setValue('showDownloadCsvSampleModal', true));
                            },
                        }
                    ]}
                />
                <input
                    type="file"
                    ref={bulkCreateFileRef}
                    accept=".csv"
                    style={{ display: 'none' }}
                    onChange={(e) => {
                        handleBulkCreateFileUpload(e);
                    }}
                />
            </Filter>
            <PageContainer>
                <Card className="card-wrapper">
                    <div className="bpcl-wallet--heading">
                        <h6 className="bpcl-wallet--heading-left">Payable Details</h6>
                    </div>
                    <CardContent>
                        <div className="custom-form-row row align-items-end">
                            <div className="form-group col-md-6">
                                <AutoComplete
                                    className="payout-wrap payout-contact-wrap"
                                    label="Contact"
                                    mandatory
                                    placeHolder="Select Contact"
                                    value={formData.contact}
                                    options={contactData}
                                    isLoading={isContactLoading}
                                    showCustomView={true}
                                    error={errors.contact}
                                    // onAsyncSearch={searchRazorPayContactsByName}
                                    renderValueHolder={(data: any) => {
                                        return (
                                            <>
                                                <ul className="align-items-center payout-contact-list">
                                                    <li className="d-flex align-items-center">
                                                        <OverflowTip
                                                            text={data.data.data.name}
                                                            elementStyle={{
                                                                maxWidth: "130px",
                                                                minWidth: "130px"
                                                            }}
                                                        />
                                                    </li>
                                                    <li>
                                                        {data.data.data.contact && (
                                                            <span><LocalPhoneOutlined />{data.data.data.contact}</span>
                                                        )}
                                                    </li>
                                                    <li className="d-flex align-items-center">
                                                        {data.data.data.email && (
                                                            <EmailOutlined />
                                                        )}
                                                        <OverflowTip
                                                            text={data.data.data.email}
                                                            elementStyle={{ maxWidth: "150px", minWidth: "150px" }}
                                                        />
                                                    </li>
                                                    {data.data.data.type && (
                                                        <li>
                                                            <Chip label={convertToTitleCase(data.data.data.type)} />
                                                        </li>
                                                    )}
                                                </ul>
                                            </>
                                        )
                                    }}
                                    renderOption={(data: any) => {
                                        return (
                                            <>
                                                <ul className="align-items-center gap-0 column-gap-3 payout-contact-list">
                                                    <li className="d-flex align-items-center">
                                                        <OverflowTip
                                                            text={data.data.name}
                                                            elementStyle={{
                                                                maxWidth: "140px",
                                                                minWidth: "140px"
                                                            }}
                                                        />
                                                    </li>
                                                    <li>
                                                        {data.data.contact && (
                                                            <span><LocalPhoneOutlined />{data.data.contact}</span>
                                                        )}
                                                    </li>
                                                    <li className="d-flex align-items-center">
                                                        {data.data.email && (
                                                            <EmailOutlined />
                                                        )}
                                                        <OverflowTip
                                                            text={data.data.email}
                                                            elementStyle={{
                                                                maxWidth: "170px",
                                                                minWidth: "170px"
                                                            }}
                                                        />
                                                    </li>
                                                    {data.data.type && (
                                                        <li>
                                                            <Chip label={convertToTitleCase(data.data.type)} />
                                                        </li>
                                                    )}
                                                </ul>
                                            </>
                                        )
                                    }}
                                    onChange={(element: OptionType) => {
                                        dispatch(setFormData('contact', element));
                                    }}
                                />
                            </div>
                            <div className="form-group col-md-6">
                                <AutoComplete
                                    label={"Payment Method"}
                                    className="payout-wrap payout-payment-wrap"
                                    mandatory
                                    placeHolder={"Select Payment Method"}
                                    value={formData.paymentMethod}
                                    error={errors.paymentMethod}
                                    options={paymentMethodData}
                                    isLoading={isPaymentMethodLoading}
                                    isDisabled={formData.contact ? false : true}
                                    showCustomView={true}
                                    renderValueHolder={(data: any) => {
                                        const accountData = data?.data?.data;
                                        if (!accountData) return null;

                                        const isUPI = accountData.accountType === 'vpa';

                                        return (
                                            <List>
                                                <ListItem>
                                                    <ListItemAvatar>
                                                        <Avatar>
                                                            <AccountBalance />
                                                        </Avatar>
                                                    </ListItemAvatar>
                                                    <ListItemText primary={
                                                        <>
                                                            {isUPI ? (
                                                                <>
                                                                    <span>UPI:</span> {accountData?.vpa?.address || 'N/A'}
                                                                </>
                                                            ) : (
                                                                    <>
                                                                        <span>{accountData?.bankAccount?.bankName || 'Bank'}:</span> {accountData?.bankAccount?.accountNumber || 'N/A'}
                                                                    </>
                                                                )}
                                                        </>
                                                    } />
                                                </ListItem>
                                            </List>
                                        );
                                    }}
                                    renderOption={(data: any) => {
                                        const accountData = data?.data;
                                        if (!accountData) return null;

                                        const isUPI = accountData.accountType === 'vpa';

                                        return (
                                            <List>
                                                <ListItem>
                                                    <ListItemAvatar>
                                                        <Avatar>
                                                            <AccountBalance />
                                                        </Avatar>
                                                    </ListItemAvatar>
                                                    <ListItemText
                                                        primary={
                                                            <>
                                                                {isUPI ? (
                                                                    <>
                                                                        <span>UPI:</span> {accountData?.vpa?.address || 'N/A'}
                                                                    </>
                                                                ) : (
                                                                        <>
                                                                            <span>{accountData?.bankAccount?.bankName || 'Bank'}:</span> {accountData?.bankAccount?.accountNumber || 'N/A'}
                                                                        </>
                                                                    )}
                                                            </>
                                                        }
                                                    />
                                                </ListItem>
                                            </List>
                                        )
                                    }}
                                    onChange={(element: OptionType) => {
                                        dispatch(setFormData('paymentMethod', element));
                                    }}
                                />
                            </div>
                            <div className="form-group col-md-6">
                                <NumberEditText
                                    label={"Amount"}
                                    mandatory
                                    placeholder={"Amount"}
                                    maxLength={10}
                                    value={formData.amount}
                                    error={errors.amount}
                                    onChange={(amount: string) => {
                                        dispatch(setFormData('amount', amount));
                                    }}
                                />
                            </div>
                            <div className="form-group col-md-6">
                                <EditText
                                    label={"Bill Number"}
                                    placeholder={"Bill Number"}
                                    maxLength={10}
                                    value={formData.billNo}
                                    error={errors.billNo}
                                    onChange={(billNo: string) => {
                                        dispatch(setFormData('billNo', billNo));
                                    }}
                                />
                            </div>
                            <div className="form-group col-md-6">
                                <EditText
                                    label={"Narration"}
                                    mandatory
                                    placeholder={"Narration"}
                                    maxLength={10}
                                    value={formData.narration}
                                    error={errors.narration}
                                    onChange={(narration: string) => {
                                        dispatch(setFormData('narration', narration));
                                    }}
                                />
                            </div>
                            <div className="form-group col-md-6">
                                <AutoComplete
                                    label={"Payment Category"}
                                    mandatory
                                    placeHolder={"Payment Category"}
                                    value={formData.paymentCategory}
                                    error={errors.paymentCategory}
                                    options={adhocPaymentCategoryOptions}
                                    onChange={(paymentCategory: OptionType) => {
                                        dispatch(setFormData('paymentCategory', paymentCategory));
                                    }}
                                />
                            </div>
                            <div className="form-group col-md-6">
                                <EditText
                                    label={"PAN Number"}
                                    placeholder={"PAN Number"}
                                    maxLength={10}
                                    value={formData.panNumber}
                                    error={errors.panNumber}
                                    onChange={(panNumber: string) => {
                                        dispatch(setFormData('panNumber', panNumber));
                                    }}
                                />
                            </div>
                            <div className="form-group col-md-6">
                                <EditText
                                    label={"Reference ID"}
                                    placeholder={"Reference ID"}
                                    maxLength={10}
                                    value={formData.referenceId}
                                    error={errors.referenceId}
                                    onChange={(referenceId: string) => {
                                        dispatch(setFormData('referenceId', referenceId));
                                    }}
                                />
                            </div>
                            <div className="form-group col-md-6">
                                <FileUploader
                                    label="Upload Document"
                                    uploadedDocuments={formData.documents.files}
                                    maxAllowedFilesCount={4}
                                    totalSize={formData.documents.totalSize}
                                    onFilesUpload={(files: File[], filesSize: number) => {
                                        return handleFilesUpload(files, filesSize);
                                    }}
                                    onFileDeleted={(file: File) => {
                                        return handleFileDelete(file);
                                    }}
                                    uploadLoading={isDocumentUploading}
                                    appDispatch={appDispatch}
                                    error={errors.documents}
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>
                <div className="d-flex justify-content-end pt-4">
                    <Button
                        title={"Create Request"}
                        leftIcon={<CheckCircle />}
                        buttonStyle='btn-blue'
                        disable={isNullValue(formData.contact) || isDocumentUploading}
                        loading={isCreateRequestLoading || isBulkCreateLoading}
                        onClick={handleCreateRequest}
                    />
                </div>
            </PageContainer>
        </div>
    )
}

export default CreateAdhocRequest