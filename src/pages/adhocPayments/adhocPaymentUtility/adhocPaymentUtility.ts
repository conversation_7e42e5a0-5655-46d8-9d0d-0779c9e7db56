import { contactListingStatusEnum, payableStatusEnum } from "../../../base/constant/ArrayList";

export const getContactTabStatus = (value: any) => {
    let status = ""
    switch (value) {
        case 0:
            status = contactListingStatusEnum.PENDING
            break;
        case 1:
            status = contactListingStatusEnum.APPROVED
            break;
        case 2:
            status = contactListingStatusEnum.REJECTED
            break;
        default:
            status = ""
    }
    return status
}

export const getPayableTabStatus = (value: any) => {
  let status = "";
  switch (value) {
    case 0:
      status = payableStatusEnum.PENDING;
      break;
    case 1:
      status = payableStatusEnum.APPROVED;
      break;
    case 2:
      status = payableStatusEnum.PAID;
      break;
    case 3:
      status = payableStatusEnum.FAILED;
      break;
    case 4:
      status = payableStatusEnum.RECONCILED;
      break;
    case 5:
      status = payableStatusEnum.REJECTED;
      break;
    case 6:
      status = payableStatusEnum.CANCELLED;
      break;
    default:
      status = "";
  }
  return status;
};
export const openInNewTab = (url: string) => {
    const newWindow = window.open(url, '_blank', 'noopener,noreferrer')
    if (newWindow) newWindow.opener = null
}
