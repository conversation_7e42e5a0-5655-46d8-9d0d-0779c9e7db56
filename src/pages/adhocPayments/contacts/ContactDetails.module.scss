.contact_details_wrapper{
    .btn_close{
        width: 40px;
        height: 40px;
        svg{
            font-size: 20px;
            margin-right: 0;
        }
        span{
            display: none;
        }
        &:hover{
            background-color: #f1f1f1;
        }
    }
    :global{
        .filter-panel{
            padding: 8px 20px; 
        }
    }
    .card_panel{
        padding: 10px 20px;
        box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.1);
        .card_header{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            border-bottom: 2px solid #e9eff4;
            padding-bottom: 10px;
            margin-left: -20px;
            margin-right: -20px;
            padding-left: 20px;
            padding-right: 20px;
            .title{
                margin: 0;
                font-size: 16px;
                font-weight: 400;
                color: #323232;
            }
        }
        .card_content{
            .list{
                list-style: none;
                margin: 0;
                .list_item{
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 0;
                    padding-bottom: 0;
                    &:not(:last-child) {
                        border-bottom: 1px solid #e9eff4;
                        padding-bottom: 10px;
                        margin-bottom: 10px;
                    }
                    .label{
                        font-weight: normal;
                        color: #323232;
                        margin-right: 10px;
                        opacity: 0.6;
                        font-size: 12px;
                    }
                    .value{
                        color: #323232;
                        text-align: right;
                        font-size: 12px;
                        font-weight: 500;
                        img{
                            width: 28px;
                            height: 28px;
                        }
                    }
                }
            }
        }
    }
    .card_footer{
        margin-top: 1rem;
        .button_list{
            display: flex;
            justify-content: center;
            gap: 1rem;
            .button_item{
                flex: 1;
                :global{
                    .btn{
                        width: 100%;
                    }
                }
            }
        }
    }
    .stapper_box {
        padding: 0;
        margin: 0;
        .stapper_list {
          position: relative;
          .stapper_item {
            list-style: none;
            display: flex;
            align-items: flex-start;
            position: relative;
            padding-left: 20px;
            margin-bottom: 16px;
            &::before {
              content: '';
              position: absolute;
              left: 9px;
              top: 24px;
              bottom: 0;
              width: 1.6px;
              border-left: 1.6px dashed #ccc;
            }
            // &:not(:last-child) {
            //   padding-bottom: 16px;
            // }
            .stapper_icon {
              position: absolute;
              left: 0;
              top: 0;
              background: white;
              z-index: 1;
              svg {
                font-size: 20px;
                color: #f3933c;
                margin-left: -1px;
              }
              img{
                width: 20px;
                height: 20px;
              }
            }
            .stapper_content {
                flex: 1;
                padding-left: 10px;
                .stapper_header{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 2px 0;
                    .stapper_title {
                        font-size: 14px;
                        font-weight: 400;
                        color: #323232;
                        margin-bottom: 0;
                        line-height: normal;
                    }
                    .stapper_action {
                        line-height: normal;
                        :global .btn{
                            font-size: 12px;
                            color: #f7791e;
                            height: auto;
                            padding:0;
                        }
                    }
                }
                .stapper_body{
                    margin-top: 8px;
                    .list_info{
                        list-style: none;
                        margin: 0;
                        padding: 0;
                        .list_item{
                            display: flex;
                            align-items: center;
                            margin-bottom: 4px;
                            gap: 4px;
                            .label{
                                font-weight: normal;
                                color: #323232;
                                opacity: 0.7;
                                font-size: 12px;
                            }
                            .value{
                                color: #323232;
                                font-size: 12px;
                                font-weight: 500;
                            }
                        }
                    }
                }
            }
            &:not(.active) {
                .stapper_title,
                .stapper_body,
                .stapper_icon {
                  opacity: 0.5;
                }
                .stapper_action {
                  opacity: 1;
                }
              }
            &.active{
                opacity: 1;
            }
          }
        }
    }
    .remarks{
        margin-top: 1rem;
        margin: 10px 20px;
        padding: 10px;
        background-color: #f1f1f1;
        border-radius: 3px;
        border:solid 1px #e9eff4;
        .remarks_title{
            margin: 0;
            font-size: 12px;
            font-weight: 400;
            color: #555555;
            position: relative;
            display: flex;
            align-items: center;
            &::after {
                content: "";
                flex: 1;     
                height: 1px;
                background-color: #d9d9d9;
                margin-left: 6px;
            }
        }
        .remarks_content{
            margin-top: 8px;
            .text{
                margin: 0;
                font-size: 12px;
                color: #323232;
                line-height: 1.5;
            }
        }
    }
    .approval_required{
        margin-top: 1rem;
        padding: 12px 20px;
        background-color: #f7921e1f;
        text-align: center;
        .title{
            margin: 0;
            font-size: 12px;
            font-weight: 500;
            color: #F7931E;
        }
    }
}