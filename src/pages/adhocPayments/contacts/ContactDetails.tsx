import React, { useState } from 'react'
import Filter from '../../../component/filter/Filter'
import { AccountBalance, CheckCircle, Close, EditOutlined} from '@material-ui/icons'
import Styles from './ContactDetails.module.scss'
import Button from '../../../component/widgets/button/Button'
import { Typography } from '@material-ui/core'
import ViewDocumentModal from '../../../modals/ViewDocumentModal/ViewDocumentModal'


const ContactDetails = () => {

  const [openViewDocumentsModal, setOpenViewDocumentsModal] = useState(false);
  const data = [
    { documentLink: '/images/data-not-found.png' },
    { documentLink: '/images/data-not-found.png' },
  ];

  return (
    <>
    <ViewDocumentModal
      open={openViewDocumentsModal}
      onClose={()=>{
        setOpenViewDocumentsModal(false);
      }}
      fileLinks={data}
    />
    <div className={Styles.contact_details_wrapper}>
        <Filter
          pageTitle={"Contact Details"}
          buttonTitle={" "}
          buttonStyle={Styles.btn_close}
          leftIcon={<Close />}
          onClick={() => {}}
          filterSection={
              <Button
                buttonStyle={"btn-edit"}
                title={"Edit"}
                leftIcon={<EditOutlined />}
                onClick={()=>{}}
              />
          }
        />

        {/* Contact Details */}
        <div className={Styles.card_panel}>
          <div className={Styles.card_header}>
            <h4 className={Styles.title}>Deepak Kumar</h4>
            <span className='badge-item'>Vendor</span>
          </div>
          <div className={Styles.card_content}>
            <ul className={Styles.list}>
              <li className={Styles.list_item}><span className={Styles.label}>Phone:</span> <span className={Styles.value}>9876543210</span></li>
              <li className={Styles.list_item}><span className={Styles.label}>Email:</span> <span className={Styles.value}><EMAIL></span></li>
              <li className={Styles.list_item}><span className={Styles.label}>Company Name:</span> <span className={Styles.value}>Balaji Transport</span></li>
              <li className={Styles.list_item}><span className={Styles.label}>Address:</span> <span className={Styles.value}>12/34 AK Dummy Address, location, pincode 100092</span></li>
              <li className={Styles.list_item}><span className={Styles.label}>Document:</span> 
                <span className={Styles.value}>
                  <Button
                    title={"2"}
                    buttonStyle={"btn_doc"}
                    leftIcon={<img src="/images/InsertPhotoOutlined.svg" alt="InsertPhotoOutlined" />}
                    onClick={()=>{
                      setOpenViewDocumentsModal(true);
                    }}
                  />
                </span>
              </li>
            </ul>
          </div>
        </div>

        {/* Payment Methods */}
        <div className={Styles.card_panel} style={{boxShadow: 'none'}}>
          <div className={Styles.card_header} style={{borderBottom: 'none'}}>
            <h4 className={Styles.title} style={{marginTop: '8px'}}>Payment Methods</h4>
          </div>
          <div className={Styles.card_content}>
            <div className={Styles.stapper_box}>
              <ul className={Styles.stapper_list}>
                <li className={`${Styles.stapper_item}`}>
                  <span className={Styles.stapper_icon}>
                    <AccountBalance />
                  </span>
                  <div className={Styles.stapper_content}>
                    <div className={Styles.stapper_header}>
                      <Typography component={'h3'} className={Styles.stapper_title}>
                        {'Bank Account'}
                      </Typography>
                      <Button
                        buttonContainerStyle={Styles.stapper_action}
                        title={"Activate"}
                        onClick={() => {}}
                      />
                    </div>
                    <div className={Styles.stapper_body}>
                      <ul className={Styles.list_info}>
                        <li className={Styles.list_item}>
                          <span className={Styles.label}>Account No.:</span> 
                          <span className={Styles.value}>**********</span>
                        </li>
                        <li className={Styles.list_item}>
                          <span className={Styles.label}>IFSC Code:</span> 
                          <span className={Styles.value}>IFSC123456</span>
                        </li>
                        <li className={Styles.list_item}>
                          <span className={Styles.label}>Beneficiary Name:</span> 
                          <span className={Styles.value}>Deepak Kumar</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </li>
                <li className={`${Styles.stapper_item} ${Styles.active}`}>
                  <span className={Styles.stapper_icon}>
                    <img src="/images/upi.svg" alt="upi" />
                  </span>
                  <div className={Styles.stapper_content}>
                    <div className={Styles.stapper_header}>
                      <Typography component={'h3'} className={Styles.stapper_title}>
                        {'UPI ID'}
                      </Typography>
                      <Button
                        buttonContainerStyle={Styles.stapper_action}
                        title={"Mark as Inactive"}
                        onClick={() => {}}
                      />
                    </div>
                    <div className={Styles.stapper_body}>
                      <ul className={Styles.list_info}>
                        <li className={Styles.list_item}>
                          <span className={Styles.label}>UPI ID:</span> 
                          <span className={Styles.value}>9837489deepak@ybl</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
          <div className={Styles.card_footer}>
            <div className={Styles.button_list}>
              <Button
                buttonStyle={"btn-red--reject"}
                buttonContainerStyle={Styles.button_item}
                title={"Reject"}
                leftIcon={<Close />}
                onClick={() => {}}
              />
              <Button
                buttonStyle={"btn-blue"}
                buttonContainerStyle={Styles.button_item}
                title={"Approve"}
                leftIcon={<CheckCircle />}
                onClick={() => {}}
              />
            </div>
            {/* Add Fund Account Button */}
            {/* <Button
              buttonStyle={"btn-outline-blue w-100"}
              buttonContainerStyle={'w-100'}
              title={"Add Fund Account"}
              leftIcon={<AddCircle />}
              onClick={() => {}}
            /> */}
          </div>
        </div>  

        {/* Remarks  */}
        <div className={Styles.remarks}>
          <h4 className={Styles.remarks_title}>Remarks</h4>
          <div className={Styles.remarks_content}>
            <p className={Styles.text}>Lorem Ipsum has been the industry’s standard dummy text ever since the 1500s, when an unknown printer.</p>
          </div>
        </div>      

        {/* Approval required */}
        <div className={Styles.approval_required}>
          <h4 className={Styles.title}>2 Approvals required to active this contact</h4>
        </div>
    </div>
    </>
  )
}

export default ContactDetails