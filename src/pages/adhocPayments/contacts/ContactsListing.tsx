import React, { useEffect, useReducer, useState } from "react";
import TableList from '../../../component/widgets/tableView/TableList'
import Filter from '../../../component/filter/Filter'
import Button from '../../../component/widgets/button/Button'
import { AddCircle, FilterList, GetApp, Publish} from '@material-ui/icons'
import { makeStyles, Tab, Tabs } from '@material-ui/core'
import { TabPanel } from '../../../component/tabs/TabPanel'
import { isMobile } from '../../../base/utility/ViewUtils'
import { contactsListingTableColumns } from '../../../templates/ContactsListingTableColums'
import PageContainer from "../../../component/pageContainer/PageContainer";
import { contactListingStatusEnum, contactsListingArray, rowsPerPageOptions } from "../../../base/constant/ArrayList";
import ContactDetails from "./ContactDetails";
import ContactsListingFilter from "./ContactsListingFilter";
import FileAction from "../../../component/fileAction/FileAction";
import DownloadCSVModal from "../../../component/downloadCSV/DownloadCSVModal";
import ContactListingReducer, { CONTACT_LISTING_STATE } from "./reducers/listing/ContactListingReducer";
import { hideLoading, refreshList, setCurrentPage, setIsContactDetailOpen, setResponse, setRowPerPage, setSelectedTab, showLoading, toggleFilter } from "./reducers/listing/ContactListingActions";
import { contactsListingRoute } from "../../../base/constant/RoutePath";
import { useHistory, useParams } from "react-router-dom";
import { getAdvanceFilterChips, useQuery } from "../../../base/utility/Routerutils";
import { isObjectEmpty } from "../../../base/utility/StringUtils";
import { useSearchParams } from "../../../base/hooks/useSearchParams";
import { contactFilters } from "../../../base/moduleUtility/FilterUtils";
import CreateContact from "./createContact/CreateContact";
import Chips from "../../../component/chips/Chips";
import { getContactTabStatus } from "../adhocPaymentUtility/adhocPaymentUtility";
import { getPayableContactList } from "../../../serviceActions/AdhocPaymentServiceActions";
import { useDispatch } from "react-redux";


const useStyles = makeStyles({
  indicator: {
    background: "none",
  },
  tabs: {
    background:'#fff',
    "& button[aria-selected='true']": {
      margin: "0 20px",
      borderBottom: "3px solid #F7931E",
    },
  },
});


const ContactsListing = () => {
  const appDispatch = useDispatch();
  const classes = useStyles();
  const [downloadOpen, setDownloadOpen] = useState(false);
  const [contactOpen, setContactOpen] = useState(false);
  const [state = CONTACT_LISTING_STATE, dispatch] = useReducer(ContactListingReducer, CONTACT_LISTING_STATE);
  const history = useHistory();
  const params = useQuery();
  const { id } = useParams<any>();
   const [filterState, addFiltersQueryParams, removeFiltersQueryParams] = useSearchParams(contactFilters)

  const handleOpenFilter = () => {
    dispatch(toggleFilter())
  };
  const handleCloseFilter = () => {
    dispatch(toggleFilter());
  };

  const handleOpenDownload = () => {
    setDownloadOpen(true);
  };
  const handleCloseDownload = () => {
      setDownloadOpen(false);
  };
  const handleApplyDownload = (url: string) => {
      console.log("Download CSV triggered");
      setDownloadOpen(false); 
  };
  
  const handleOpenContact = () => {
    setContactOpen(true);
  };
  const handleCloseContact = () => {
    setContactOpen(false);
  };

  useEffect(() => {

    const getList = async () => {
      let queryParams: any = {
        page: state.currentPage,
        size: state.pageSize,
        contactStatus: id
          ? getContactTabStatus(contactsListingArray.indexOf(id))
          : contactListingStatusEnum.PENDING,
      };
      if (!isObjectEmpty(filterState.params)) {
        queryParams = Object.assign(queryParams, filterState.params);
      }
      if (queryParams && queryParams.queryFieldLabel) {
        delete queryParams["queryFieldLabel"];
      }
      dispatch(setSelectedTab(id ? contactsListingArray.indexOf(id) : 0));
      dispatch(showLoading());
      try {
        const response = await appDispatch(getPayableContactList(queryParams));
        if (response) {
          dispatch(setResponse(response));          
        }
      } catch (error) {
        console.error("Error fetching request list:", error);
      } finally {
        dispatch(hideLoading());
      }
    };

    getList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [state.refreshList, state.currentPage, state.pageSize, history.location.search, id]);

  return (
    <div className="container-fluid">
      <ContactsListingFilter
        open={state.openFilter}
        filterChips={filterState.chips}
        filterParams={filterState.params}
        onClose={handleCloseFilter}
        onApplyFilter={(filterChips: any, filterParams: any) => {
          dispatch(refreshList());
          dispatch(toggleFilter());
          addFiltersQueryParams(filterChips, filterParams);
        }}
      />
      <DownloadCSVModal
        open={downloadOpen}
        items={[
          {
            label: "Razorpay Bank Account Sample Download",
            // href: "/sample/adhoc_payment_sample.csv",
            onClick: (url: string) => handleApplyDownload(url)
          },
          {
            label: "Razorpay UPI Sample Download",
            onClick: (url: string) => handleApplyDownload(url)
          },
        ]}
        onClose={handleCloseDownload}
      />
      <CreateContact
        isOpen={contactOpen}
        onClose={handleCloseContact}
        isContact={true}
        handleSubmitSuccess={() => {}}
      />
      <div className='row main-section'>
        <div className="page-section col">
          <Filter
            pageTitle={"Contacts Listing"}
          >
            <Button
              buttonStyle={"btn-blue "}
              title={"Filter"}
              leftIcon={<FilterList />}
              onClick={handleOpenFilter}
            />
            <Button
              buttonStyle={"btn-orange "}
              title={"Add New Contact"}
              leftIcon={<AddCircle />}
              onClick={handleOpenContact}
            />
            <FileAction
              options={[
                {
                  menuTitle: "Upload CSV",
                  Icon: Publish,
                  className: "menu-file-top menu-file-outline",
                },
                {
                  menuTitle: "Download CSV Sample",
                  Icon: GetApp,
                  onClick: handleOpenDownload,
                  className: "menu-file-bottom menu-file-outline",
                }
              ]}
            />

          </Filter>
          <div className="tab-nav">
            {
              <Tabs
                value={state.selectedTabIndex}
                className={classes.tabs}
                classes={{ indicator: classes.indicator }}
                onChange={(event: any, newValue: any) => {
                  if (newValue !== state.selectedTabIndex) {
                    dispatch(setSelectedTab(newValue));
                    dispatch(setCurrentPage(1));
                    dispatch(setRowPerPage(rowsPerPageOptions[0]));
                    history.replace({
                      pathname:
                        contactsListingRoute + contactsListingArray[newValue],
                      search: params.toString(),
                    });
                  }
                }}
                variant="scrollable"
                scrollButtons={isMobile ? "on" : "off"}
              >
                {contactsListingArray.map((element, index) => (
                  <Tab key={index} label={element} />
                ))}
              </Tabs>
            }

            <TabPanel value={0} index={0}>
              {pageContent()}
            </TabPanel>
          </div>
        </div>
        {state.isContactDetailsOpen && (
          <div className="drawer-section col-auto">
            <ContactDetails />
          </div>
        )}
      </div>
    </div>
  );

  function pageContent() {
    return (
      <PageContainer loading={state.loading} listData={state.listData}>

       {!isObjectEmpty(getAdvanceFilterChips(filterState.chips)) &&
            Object.keys(getAdvanceFilterChips(filterState.chips)).map(
              (element: any, index: any) => (
                <Chips
                  key={index}
                  label={filterState.chips[element]}
                  onDelete={() => {
                    dispatch(refreshList());                    
                    removeFiltersQueryParams([element]);
                  }}
                />
              )
            )}

        <TableList
          tableColumns={contactsListingTableColumns(state.selectedTabName)}
          currentPage={state.currentPage}
          rowsPerPage={state.pageSize}
          rowsPerPageOptions={rowsPerPageOptions}
          totalCount={state.pagination && state.pagination.count}
          listData={state.listData}
          onChangePage={(event: any, page: number) => {
            dispatch(setCurrentPage(page));
          }}
          onRowsPerPageChange={(event: any) => {
            dispatch(setRowPerPage(event.target.value));
          }}
          onClickActionButton={() => {}}
          onRowClick={(_row: any, listItemIndex: number) => {
            dispatch(setIsContactDetailOpen(true));
          }}
        />
      </PageContainer>
    );
  }

}

export default ContactsListing