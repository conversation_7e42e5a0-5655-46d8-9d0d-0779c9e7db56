import React, { useEffect, useReducer } from 'react'
import FilterContainer from '../../../modals/FilterModal/FilterContainer'
import EditText from '../../../component/widgets/EditText'
import AutoComplete from '../../../component/widgets/AutoComplete'
import ContactListingFilterReducer, { CONTACT_LISTING_FILTER_STATE } from './reducers/listingFilter/ContactListingFilterReducer'
import { resetState, setcontactTypeList, setError, setFilterParams, setFilterValues, setIntegrationTypeList, setIsFilterChanged } from './reducers/listingFilter/ContactListingFilterActions'
import { isObjectEmpty } from '../../../base/utility/StringUtils'
import { useDispatch } from 'react-redux'
import { getContactType, getIntegrationType } from '../../../serviceActions/AdhocPaymentServiceActions'
import { setAutoCompleteListWithoutLabelAndValue } from '../../../base/moduleUtility/DataUtils'

interface ContactsListingFiltersProps {
    open: boolean,
    onClose: any,
    onApplyFilter: any
    filterChips: any,
    filterParams: any
}

function ContactsListingFilter(props: ContactsListingFiltersProps) {
    const appDispatch = useDispatch();
    const { open, onClose, onApplyFilter, filterChips, filterParams } = props;
    const [state = CONTACT_LISTING_FILTER_STATE, dispatch] = useReducer(ContactListingFilterReducer, CONTACT_LISTING_FILTER_STATE);

    function setValues(chips: any, params?: any) {
    dispatch(setFilterValues({ ...state.filterValues, ...chips }));
    dispatch(setError({}));
    if (params) {
      dispatch(setFilterParams({ ...state.filterParams, ...params }));
    }
    dispatch(setIsFilterChanged(true));
  }
  console.log(state);
  

  function onApply() {
    if (!isObjectEmpty(state.filterParams)) {
      if (state.isFilterChanged) {
        dispatch(setError({}));
        onApplyFilter(state.filterValues, state.filterParams);
      } else {
        dispatch(setError({}));
        onClose();
      }
    } else {
      dispatch(
        setError(
          { name: "Enter valid name" }
        )
      );
    }
  }

  useEffect(() => {
    if (open) {
      dispatch(setFilterValues(filterChips));
      dispatch(setFilterParams(filterParams));
      dispatch(setIsFilterChanged(false));      
    }
  }, [open, filterChips, filterParams]);

  useEffect(()=>{
    const getData = async () => {
      const contactTypes = await appDispatch(getContactType());
      const integrationTypes = await appDispatch(getIntegrationType())
      if(contactTypes){        
        dispatch(setcontactTypeList(setAutoCompleteListWithoutLabelAndValue(contactTypes)))
      }
      if(integrationTypes){        
        dispatch(
          setIntegrationTypeList(
            integrationTypes.map((integrationType: string) => {
              return {
                label: integrationType,
                value: integrationType.replaceAll(" ", "_").toUpperCase(),
              };
            })
          )
        );
      }
    }
    getData()
  },[appDispatch, open])

  return (
    <FilterContainer
      open={open}
      onClose={() => {
        onClose();
        dispatch(setError({}));
      }}
      onClear={() => {
        dispatch(resetState());
      }}
      onApply={onApply}
    >
      <div className="filter-form-row">
        <div className="form-group">
          <EditText
            label={"Name"}
            placeholder={"Enter Name"}
            maxLength={50}
            value={state?.filterValues && state?.filterValues.name}
            error={state.error.name}
            onChange={(text: any) => {
              setValues({ name: text }, { name: text.trim() });
            }}
          />
        </div>
        <div className="form-group">
          <EditText
            label={"Phone"}
            placeholder={"Enter Phone"}
            maxLength={50}
            value={state?.filterValues && state?.filterValues.phone}
            onChange={(text: any) => {
                setValues({ phone: text }, { phone: text.trim() });
            }}
          />
        </div>
        <div className="form-group">
          <AutoComplete
            label={"Contact Type"}
            placeHolder={"Select Contact Type"}
            value={state?.filterValues?.contactType ? {
                   label: state.filterValues.contactType,
                   value: state.filterParams.contactType
                   } : undefined}
            options={state.contactTypeList}
            onChange={(value: any) => {
                setValues({ contactType: value.label }, { contactType: value.label.trim() });
            }}
          />
        </div>
        <div className="form-group">
          <AutoComplete
            label={"Integration Type"}
            placeHolder={"Select Integration Type"}
            value={state?.filterValues?.integrationTypeLabel ? {
                   label: state.filterValues.integrationTypeLabel,
                   value: state.filterParams.integrationType
                   } : undefined }
            options={state?.integrationTypeList}
            onChange={(value: any) => {
                setValues({ integrationTypeLabel: value.label }, { integrationType: value.value});
            }}
          />
        </div>
        <div className="form-group">
          <EditText
            label={"Company Name"}
            placeholder={"Enter Company Name"}
            maxLength={50}
            value={state?.filterValues && state?.filterValues.companyName}
            onChange={(text: any) => {
                setValues({ companyName: text }, { companyName: text.trim() });
            }}
          />
        </div>
        <div className="form-group">
          <EditText
            label={"PAN Number"}
            placeholder={"Enter PAN Number"}
            maxLength={50}
            value={state?.filterValues && state?.filterValues.panNumber}
            onChange={(text: any) => {
                setValues({ panNumber: text }, { panNumber: text.trim() });
            }}
          />
        </div>
      </div>
    </FilterContainer>
  );
}



export default ContactsListingFilter