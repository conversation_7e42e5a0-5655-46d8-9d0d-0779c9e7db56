.Contact_container{
    padding: 0;
    :global{
        .autocomplete-wrap label{
            color: #083654;
            font-size: 13px;
        }
        .input-wrap label{
            font-size: 13px;
        }
    }
    .uploadFile{
        :global{
            label{
                font-size: 13px;
            }
        }
    }
    .Contact_info{
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 16px;
    }
    .email_confirm{
        font-size: 12px;
        color: #768a9e;
        margin-top: 4px;
        font-style: italic;
    }
    .info_container{
        padding: 12px;
        :global .MuiCardContent-root{
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
    }
    .card_container{
        padding: 8px 16px;
        border-radius: 8px;
        border: solid 1px #DFE2E6;
        margin-bottom: 16px;
    }
    .info_wrap{
        display: flex;
        gap: 10px;
        flex-wrap: nowrap;
    }
    .editIcon{
        padding:4px;
        box-shadow: none;
        height: auto;
        :global .MuiSvgIcon-root{
            font-size: 20px;
            color: #F3933D;
            margin-right: 0;
        }
    }
    .icon{
        font-size: 16px;
        color: #F3933D;
    }
    .text_info{
        width: auto;
        display: flex;
        gap: 4px;
        align-items: center;
        font-size: 14px;
        color: #6E6E6E;
        white-space: nowrap;
    }
    .name{
        font-size: 16px;
        color: #121212;
        margin: 0;
    }
    .type{
        font-size: 12px;
        color: #F3933D;
        font-weight: 500;
        margin-left: 14px;
        padding: 2px 12px;
        border-radius: 20px;
        background-color: #f3923d30;
    }
    .heading{
        margin: 10px 0;
        font-size: 14px;
        font-weight: 500;
        color: #121212;
    }
    .fund_account{
        padding: 8px 12px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        :global {
            .MuiCardContent-root{
                display: flex;
                flex-direction: column;
                gap: 12px;
            }
            .MuiBox-root{
                border: none;
            }
            .MuiTab-wrapper{
                display: inline;
            }
        }
        .tabs{
            margin: 0;
            :global {
                .MuiTabs-flexContainer{
                        display: flex;
                        background-color: #FEF4EB;
                        padding: 2px;
                        border: solid 2px #eae1d9;
                        border-radius: 3px;
                        font-size: 14px;
                    .MuiButtonBase-root{
                        flex: 1;
                        color: #F3933D;
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        min-height: 42px;
                        border-radius: 3px;
                        img,
                        .MuiSvgIcon-root{
                            margin-right: 10px;
                            margin-bottom: 0;
                        }
                        @media (max-width: 767px) {
                            font-size: 11px;
                        }
                    }
                    .Mui-selected{
                        color: #fff;
                        background-color: #F3933D;
                    }
                }
                .MuiTabs-indicator{
                    display: none;
                }
            }
        }        
    }
    .accountForm{
        display: flex;
        flex-direction: column;
        gap: 8px;
        .form_Wrap{
            background-color: #F5F5F5D6;
            border-radius: 6px;
            border: solid 1px #E0E4E6;
            padding: 12px;
            box-shadow: none;
            display: grid;
            gap: 8px;  
            :global{
                .input-wrap{
                    display: grid;
                    grid-template-columns: 1fr 2fr;
                    gap: 12px;
                    margin-bottom: 8px;
                    label{
                        margin-bottom: 0;
                        max-width: 130px;
                        span{
                            font-size: 12px;
                            font-weight: normal;
                        }
                    }
                    .MuiInputBase-input{
                        background-color: #fff;
                    }
                    @media (max-width: 767px) {
                        grid-template-columns: 1fr !important;
                        gap: 0;
                        margin-bottom: 3px;
                        label{
                            margin-bottom: 4px;
                            max-width: 100%;
                        }
                    }
                    label.error, .MuiFormHelperText-root{
                        margin-top: 1px !important;
                    }
                }
            }
        }
        .addAccount{
            color: #006cc9;
            font-size: 12px;
            padding: 6px 8px;
            height: auto;
        }
    }
    .upiForm{
        margin: 0;
        .form_Wrap{
            :global{
                .input-wrap{
                    display: grid;
                    grid-template-columns: auto 1fr;
                }
            }
        }
    }
}