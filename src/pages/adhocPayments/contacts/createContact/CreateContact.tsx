import React from 'react'
import ModalContainer from '../../../../modals/ModalContainer';
import { Box, Paper, Tab, Tabs, Typography } from '@material-ui/core';
import Styles from './CreateContact.module.scss'
import { AccountBalanceOutlined, CallOutlined, EditOutlined, KeyboardBackspace, MailOutline, PublishOutlined } from '@material-ui/icons';
import AutoComplete from '../../../../component/widgets/AutoComplete';
import EditText from '../../../../component/widgets/EditText';
import FileUploader from '../../../../component/fileUpload/FileUploader';
import { OverflowTip } from '../../../../component/widgets/tooltip/OverFlowToolTip';
import { isMobile } from '../../../../base/utility/ViewUtils';
import Button from '../../../../component/widgets/button/Button';
import { isNullValue } from '../../../../base/utility/StringUtils';
import NumberEditText from '../../../../component/widgets/NumberEditText';


interface ContactModalProps {
    isOpen: boolean;
    onClose: () => void;
    isContact: boolean;
    handleSubmitSuccess: () => void;
}

const CreateContact = (props: ContactModalProps) => {
    const { isOpen, onClose, isContact, handleSubmitSuccess } = props;
    const [step, setStep] = React.useState(0);
    
    const handlePrimaryClick = () => {
        if (isContact && step < 2) {
          setStep(prev => prev + 1); // Go to next step
        } else {
          handleSubmitSuccess(); // Final save
        }
      };
      
      const handleBackClick = () => {
        if (step > 0) {
          setStep(prev => prev - 1); // Go to previous step
        }
      };
      const [tabValue, setTabValue] = React.useState(0);
      const handleTabChange = (event: any, newValue: number) => {
        setTabValue(newValue);
        if (newValue === 0) {
          
        } else {
          
        }
      };
    return (
        <ModalContainer
            title={
                isContact && step === 0 ?
                "Add New Contact" : 
                <span>Add New Contact <small className={'text-muted ml-1'}>({'Razorpay'})</small></span>
            }
            styleName={'modal-contact'}
            open={isOpen}
            onApply={handlePrimaryClick}
            onClose={onClose}
            onClear={handleBackClick}
            actionButtonStyle="justify-content-between"
            primaryButtonTitle={isContact && step < 2 ? "Next" : "Save"}
            primaryButtonStyle="btn-blue ms-auto"
            primaryButtonLeftIcon={<KeyboardBackspace style={{transform: 'rotate(180deg)' }} />}
            secondaryButtonTitle="Back"
            secondaryButtonStyle={isContact && step > 0 ? "btn-grey" : "d-none"}
            secondaryButtonLeftIcon={<KeyboardBackspace />}      
            secondaryButtonDisable={isContact && step > 0 ? false : true}
        >
            <Box className={Styles.Contact_container}>
                {/* Contact Integration Type */}
                {isContact && step === 0 && (
                    <Box className={Styles.Contact_info}>
                        <AutoComplete
                            label={"Integration Type"}
                            mandatory={true}
                            placeHolder={"Select Integration Type"}
                            options={[
                                { label: "Razorpay", value: "Razorpay" },
                                { label: "Entity Payments", value: "Entity Payments" }
                            ]}
                            value={0}
                            onChange={()=>{}}
                        />
                    </Box>
                )}
                {/* Contact Info */}
                {isContact && step === 1 && (
                    <Box className={Styles.Contact_info}>
                        <EditText
                            label={"Name"}
                            mandatory={true}
                            value={''}
                            placeholder={"Enter Name"}
                            maxLength={100}
                            onChange={()=>{}}
                        />
                        <AutoComplete
                            label={"Contact Type"}
                            mandatory={true}
                            placeHolder={"Contact Type"}
                            options={[
                                { label: "Customer", value: "Customer" },
                                { label: "Vendor", value: "Vendor" },
                                { label: "Employee", value: "Employee" },
                            ]}
                            value={0}
                            onChange={()=>{}}
                        />
                        <EditText
                            label={"Phone"}
                            mandatory={true}
                            value={''}
                            placeholder={"Enter Phone"}
                            maxLength={10}
                            onChange={()=>{}}
                        />
                        <EditText
                            label={"Company Name"}
                            mandatory={true}
                            value={''}
                            placeholder={"Enter Company Name"}
                            maxLength={100}
                            onChange={()=>{}}
                        />
                        <EditText
                            label={"PAN Number"}
                            mandatory={true}
                            value={''}
                            placeholder={"Enter PAN Number"}
                            maxLength={10}
                            onChange={()=>{}}
                        />
                        <EditText
                            label={"Address"}
                            mandatory={true}
                            value={''}
                            placeholder={"Enter Address"}
                            maxLength={100}
                            onChange={()=>{}}
                        />
                        <EditText
                            label={"Email"}
                            mandatory={true}
                            value={''}
                            placeholder={"Enter Email"}
                            maxLength={100}
                            onChange={()=>{}}
                        />
                        <FileUploader
                            label={"Upload Document"}
                            styleName={Styles.uploadFile}
                            uploadedDocuments={[]}
                            maxAllowedFilesCount={10}
                            totalSize={0}
                            onFilesUpload={() => {
                                return Promise.resolve({ success: true, data: [] });
                            }}
                            onFileDeleted={() => {
                                return Promise.resolve({ success: true, data: [] });
                            }}
                            uploadLoading={false}
                        />
                    </Box>
                )}
                {/* Account Info */}
                {isContact && step === 2 && (
                <>
                    <div className={Styles.card_container}> 
                        <Box className={`${Styles.info_wrap} row`}>
                            <div className="col d-flex align-items-center">
                            <Box component="h2" className={Styles.name}>
                                <OverflowTip
                                    text={'Deepak Kumar'}
                                    elementStyle={
                                        isMobile ? { maxWidth: "210px", minWidth: "auto" } : {}
                                    }
                                />
                            </Box>
                            <span className={Styles.type}>{'Vendor'}</span>
                            </div>
                            {isContact && (
                            <div className="col-auto">
                                <Button
                                    buttonStyle={Styles.editIcon}
                                    leftIcon={<EditOutlined />}
                                    onClick={() => {}}
                                />
                            </div>
                            )}
                        </Box>
                        <Box className={Styles.info_wrap}>
                            <Typography component={"p"} className={Styles.text_info}>
                                <CallOutlined className={Styles.icon} /> +91 {'9876543210'}
                            </Typography>
                            {!isNullValue("<EMAIL>") &&(<div className={Styles.text_info}>
                                <OverflowTip
                                    cellIcon={<MailOutline className={`${Styles.icon} mr-1`} />}
                                    elementStyle={
                                        isMobile ? { maxWidth: "190px", minWidth: "190px" } : {}
                                    }
                                    text={"<EMAIL>"}
                                />
                            </div>)}
                        </Box>
                    </div>   
                    <Typography component={"h3"} className={Styles.heading}>
                        Add Fund Account
                    </Typography>
                    <div className={`${Styles.card_container} ${Styles.fund_account}`}>
                        <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                            <h3 className={Styles.heading} style={{ fontWeight: "normal" }}>
                                Select Account Type
                            </h3>
                            <Tabs
                                className={Styles.tabs}
                                value={tabValue}
                                onChange={handleTabChange}
                                aria-label="tabs"
                            >
                                <Tab
                                label="UPI"
                                icon={
                                    <img
                                    src={
                                        tabValue === 0
                                        ? "/images/upi-active.svg"
                                        : "/images/upi.svg"
                                    }
                                    alt="upi"
                                    />
                                }
                                />
                                <Tab label="Bank Account" icon={<AccountBalanceOutlined />} />
                            </Tabs>
                        </Box>    
                        <Box>
                        {tabValue === 0 && (
                            <Box
                                className={`${Styles.upiForm} ${Styles.accountForm}`}
                                component={"div"}
                            >
                            <Paper className={Styles.form_Wrap} 
                                style={{ 
                                padding: "12px",
                                gridTemplateColumns: "1fr auto"
                                }}
                            >
                                <EditText
                                    label={"UPI ID"}
                                    mandatory={true}
                                    value={'deepak@ybl'}
                                    placeholder={"UPI ID"}
                                    maxLength={60}
                                    onChange={()=>{}}
                                    error={""}
                                />
                                <Box 
                                className="upload-btn"
                                component={"div"}
                                >
                                <Box className="upload-inner" component={"div"}
                                    style={{
                                        background: "#fff",
                                        border: "solid 2px #ebeff3",
                                        height: "47px"
                                    }}
                                >
                                    <PublishOutlined className="upload-icon" />
                                    <span className="title">QR Upload</span>
                                    <input type="file" className="upload-input" accept="image/*" onChange={()=>{}} />
                                </Box>
                                </Box>
                            </Paper>
                            </Box>
                        )}
                        {tabValue === 1 && (
                            <Box className={Styles.accountForm} component={"div"}>
                            <Paper className={Styles.form_Wrap} style={{ padding: "12px" }}>
                                <NumberEditText
                                    label={"Account Number"}
                                    value={''}
                                    placeholder={"Enter Account Number"}
                                    maxLength={18}
                                    decimalSeparator={false}
                                    decimalScale={0}
                                    onChange={()=>{}}
                                    error={""}
                                    showFormattedValue={false}
                                />
                                <NumberEditText
                                    label={"Confirm Account No"}
                                    value={''}
                                    placeholder={"Confirm Account Number"}
                                    maxLength={18}
                                    decimalSeparator={false}
                                    decimalScale={0}
                                    showFormattedValue={true}
                                    onChange={()=>{}}
                                    error={""}
                                />
                                <EditText
                                    label={"IFSC Code"}
                                    value={''}
                                    placeholder={"IFSC Code"}
                                    maxLength={11}
                                    onChange={()=>{}}
                                    error={""}
                                />
                                <EditText
                                    label={"Beneficiary Name"}
                                    value={"Deepak Kumar"}
                                    placeholder={"Beneficiary Name"}
                                    maxLength={60}
                                    onChange={()=>{}}
                                    error={""}
                                />
                            </Paper>
                            </Box>
                        )}
                        </Box>                                            
                    </div>
                </>
                )}
            </Box>
        </ModalContainer>
    )
}

export default CreateContact