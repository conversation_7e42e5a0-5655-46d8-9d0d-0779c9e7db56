import ContactListingTypes from "./ContactListingTypes";

export const toggleFilter = () => ({
    type: ContactListingTypes.TOGGLE_FILTER,
});

export const toggleModal = () => ({
    type: ContactListingTypes.TOGGLE_MODAL,
});

export const setSelectedElement = (value: any) => ({
    type: ContactListingTypes.SELECTED_ELEMENT,
    value,
});

export const setSelectedTab = (value: any) => ({
    type: ContactListingTypes.SELECTED_TAB_INDEX,
    value
});
export const setIsContactDetailOpen = (value: any) => ({
    type: ContactListingTypes.OPEN_CONTACT_DETAILS,
    value
});

export const setResponse = (response: any) => ({
    type: ContactListingTypes.SET_RESPONSE,
    response,
});

export const setCurrentPage = (value: any) => ({
    type: ContactListingTypes.SET_CURRENT_PAGE,
    value
});

export const refreshList = () => ({
    type: ContactListingTypes.REFRESH_LIST,
});

export const setRowPerPage = (value: any) => ({
    type: ContactListingTypes.SET_ROW_PER_PAGE,
    value
});

export const showLoading = () => ({
    type: ContactListingTypes.SHOW_LOADING,
});

export const hideLoading = () => ({
    type: ContactListingTypes.HIDE_LOADING,
});

export const toggleBulkUpload = () => ({
    type: ContactListingTypes.TOGGLE_BULK_UPLOAD,
});

export const setCheckedListResponse = (response: any) => ({
    type: ContactListingTypes.SET_CHECKED_RESPONSE,
    response,
});

export const setWalletType = (value: any) => ({
    type: ContactListingTypes.SET_WALLET_TYPE,
    value
})

export const handleViewDocumentModal = (isOpen: boolean, documentLinks: any) => ({
    type: ContactListingTypes.HANDLE_DOCUMENT_MODAL,
    isOpen,
    documentLinks
})
