import { createReducer } from "reduxsauce";
import { contactListingStatusArray, rowsPerPageOptions } from "../../../../../base/constant/ArrayList";
import ContactTypes from "./ContactListingTypes";
import { isMobile } from "../../../../../base/utility/ViewUtils";

interface ContactListingState {
    openFilter: boolean,
    openBulkUpload: boolean,
    selectedItem: any,
    pagination: any,
    listData: any,
    openModal: boolean,
    selectedTabIndex: number,
    selectedTabName: any
    currentPage: number,
    refreshList: boolean,
    loading: boolean,
    pageSize: number,
    isContactDetailsOpen: Boolean,
    selectedRowIndex: number
}

export const CONTACT_LISTING_STATE: ContactListingState = {
    openFilter: false,
    openBulkUpload: false,
    selectedItem: undefined,
    pagination: undefined,
    listData: undefined,
    openModal: false,
    currentPage: 1,
    selectedTabIndex: 0,
    selectedTabName: contactListingStatusArray[0],
    refreshList: false,
    loading: false,
    pageSize: rowsPerPageOptions[0],
    isContactDetailsOpen: false,
    selectedRowIndex:0
}

const toggleFilterReducer = (state = CONTACT_LISTING_STATE) => ({
    ...state,
    openFilter: !state.openFilter
});

const toggleModalReducer = (state = CONTACT_LISTING_STATE) => ({
    ...state,
    openModal: !state.openModal
});

const setSelectedElementReducer = (state = CONTACT_LISTING_STATE, action: any) => ({
    ...state,
    selectedItem: action.value
});

const setResponseReducer = (state = CONTACT_LISTING_STATE, action: any) => ({
    ...state,
    pagination: action.response && action.response.pagination,
    listData: isMobile ?
        (state.listData ? [...state.listData, ...action.response && action.response.data] : action.response && action.response.data)
        : action.response && action.response.data,
});

const setCurrentPageReducer = (state = CONTACT_LISTING_STATE, action: any) => ({
    ...state,
    currentPage: action.value
});

const setIsContactDetailsOpen = (state = CONTACT_LISTING_STATE, action: any) => ({
    ...state,
    isContactDetailsOpen: action.value
});

const refreshListReducer = (state = CONTACT_LISTING_STATE) => ({
    ...state,
    refreshList: !state.refreshList,
    currentPage: 1,
    listData: undefined,
});

const setRowPerPageReducer = (state = CONTACT_LISTING_STATE, action: any) => ({
    ...state,
    pageSize: action.value,
    currentPage: 1,
    listData: undefined,
});

const showLoadingReducer = (state = CONTACT_LISTING_STATE) => ({
    ...state,
    loading: true
});

const hideLoadingReducer = (state = CONTACT_LISTING_STATE) => ({
    ...state,
    loading: false
});

const toggleBulkUploadReducer = (state = CONTACT_LISTING_STATE) => ({
    ...state,
    openBulkUpload: !state.openBulkUpload
});

const setSelectedTabReducer = (state = CONTACT_LISTING_STATE, action: any) => ({
    ...state,
    selectedTabIndex: action.value,
    selectedTabName: contactListingStatusArray[action.value],
    listData: undefined,
});

const setCheckedListResponse = (state = CONTACT_LISTING_STATE, action: any) => ({
    ...state,
    listData: action.response
})


const ACTION_HANDLERS = {
    [ContactTypes.TOGGLE_FILTER]: toggleFilterReducer,
    [ContactTypes.TOGGLE_MODAL]: toggleModalReducer,
    [ContactTypes.SELECTED_ELEMENT]: setSelectedElementReducer,
    [ContactTypes.SET_RESPONSE]: setResponseReducer,
    [ContactTypes.SET_CURRENT_PAGE]: setCurrentPageReducer,
    [ContactTypes.REFRESH_LIST]: refreshListReducer,
    [ContactTypes.SET_ROW_PER_PAGE]: setRowPerPageReducer,
    [ContactTypes.SHOW_LOADING]: showLoadingReducer,
    [ContactTypes.HIDE_LOADING]: hideLoadingReducer,
    [ContactTypes.TOGGLE_BULK_UPLOAD]: toggleBulkUploadReducer,
    [ContactTypes.SELECTED_TAB_INDEX]: setSelectedTabReducer,
    [ContactTypes.SET_CHECKED_RESPONSE]: setCheckedListResponse,
    [ContactTypes.OPEN_CONTACT_DETAILS]: setIsContactDetailsOpen,
    

}

export default createReducer(CONTACT_LISTING_STATE, ACTION_HANDLERS);