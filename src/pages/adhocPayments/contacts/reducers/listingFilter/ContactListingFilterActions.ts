import ContactListingFilterTypes from "./ContactListingFilterTypes";

export const setFilterValues = (value: any) => ({
    type: ContactListingFilterTypes.SET_FILTER_VALUES,
    value,
});

export const setFilterParams = (value: any) => ({
    type: ContactListingFilterTypes.SET_FILTER_PARAMS,
    value,
})

export const setcontactTypeList = (value: any) => ({
    type: ContactListingFilterTypes.SET_CONTACT_TYPE_LIST,
    value,
})

export const setIntegrationTypeList = (value: any) => ({
    type: ContactListingFilterTypes.SET_INTEGRATION_TYPE_LIST,
    value,
})

export const setIsFilterChanged = (value: any) => ({
    type: ContactListingFilterTypes.SET_IS_FILTER_CHANGED,
    value,
})

export const setError = (value: any) => ({
    type: ContactListingFilterTypes.SET_ERROR,
    value,
})

export const resetState = () => ({
    type: ContactListingFilterTypes.CLEAR_STATE,
})