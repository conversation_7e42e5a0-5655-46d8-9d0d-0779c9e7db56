import { OptionType } from "../../../../../component/widgets/widgetsInterfaces";
import { createReducer } from "reduxsauce";
import ContactListingFilterTypes from "./ContactListingFilterTypes";

export interface ContactListingFilterState {
    filterValues: any,
    filterParams: any,
    error: any,
    isFilterChanged: boolean,
    contactTypeList: Array<OptionType>,
    integrationTypeList: Array<OptionType>,
}

export const CONTACT_LISTING_FILTER_STATE: ContactListingFilterState = {
    filterValues: {},
    filterParams: {},
    error: {},
    isFilterChanged: false,
    contactTypeList: [],
    integrationTypeList: [],
}

const setFilterValuesReducer = (state = CONTACT_LISTING_FILTER_STATE, action: any) => ({
    ...state,
    filterValues: action.value
})

const setFilterParamsReducer = (state = CONTACT_LISTING_FILTER_STATE, action: any) => ({
    ...state,
    filterParams: action.value
})

const setContactTypeListReducer = (state = CONTACT_LISTING_FILTER_STATE, action: any) => ({
    ...state,
    contactTypeList: action.value
})

const setIntegrationTypeListReducer = (state = CONTACT_LISTING_FILTER_STATE, action: any) => ({
    ...state,
    integrationTypeList: action.value
})

const setIsFilterChangedReducer = (state = CONTACT_LISTING_FILTER_STATE, action: any) => ({
    ...state,
    isFilterChanged: action.value
})

const setErrorReducer = (state = CONTACT_LISTING_FILTER_STATE, action: any) => ({
    ...state,
    error: action.value
})

const resetStateReducer = () => CONTACT_LISTING_FILTER_STATE;

const ACTION_HANDLERS = {
    [ContactListingFilterTypes.SET_FILTER_VALUES]: setFilterValuesReducer,
    [ContactListingFilterTypes.SET_FILTER_PARAMS]: setFilterParamsReducer,
    [ContactListingFilterTypes.SET_CONTACT_TYPE_LIST]: setContactTypeListReducer,
    [ContactListingFilterTypes.SET_INTEGRATION_TYPE_LIST]: setIntegrationTypeListReducer,
    [ContactListingFilterTypes.SET_IS_FILTER_CHANGED]: setIsFilterChangedReducer,
    [ContactListingFilterTypes.SET_ERROR]: setErrorReducer,
    [ContactListingFilterTypes.CLEAR_STATE]: resetStateReducer
}

export default createReducer(CONTACT_LISTING_FILTER_STATE, ACTION_HANDLERS);