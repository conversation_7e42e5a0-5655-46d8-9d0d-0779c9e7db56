import React from 'react';
import TableList from '../../../component/widgets/tableView/TableList';
import { logsListingTableColumns } from '../../../templates/LogsListingTemplate';
import PageContainer from '../../../component/pageContainer/PageContainer';
import Filter from '../../../component/filter/Filter';
import { FilterList } from '@material-ui/icons';
import FilterContainer from '../../../modals/FilterModal/FilterContainer';
import EditText from '../../../component/widgets/EditText';
import AutoComplete from '../../../component/widgets/AutoComplete';
import { OptionType } from '../../../component/widgets/widgetsInterfaces';
import { useHistory } from 'react-router';
import { logDetailsRoute } from '../../../base/constant/RoutePath';

const LogsListing = () => {
    const history = useHistory();
    const [filterState, setFilterState] = React.useState(false);
    const data = [
        {
            requestId: 'dab685b80ab64980bbbd0e40c77171fd',
            jobType: 'Payables',
            status: 'Completed',
            jobCreatedAt: '23-03-2022 12:45:00',
        },
        {
            requestId: 'dab685b80ab64980bbbd0e40c77171fg',
            jobType: 'Contacts',
            status: 'Failed',
            jobCreatedAt: '23-03-2022 12:45:00',
        },
        {
            requestId: 'dab685b80ab64980bbbd0e40c77171fh',
            jobType: 'Contacts',
            status: 'Completed',
            jobCreatedAt: '23-03-2022 12:45:00',
        },
        {
            requestId: 'dab685b80ab64980bbbd0e40c77171fi',
            jobType: 'Payables',
            status: 'Failed',
            jobCreatedAt: '23-03-2022 12:45:00',
        },
        {
            requestId: 'dab685b80ab64980bbbd0e40c77171fj',
            jobType: 'Contacts',
            status: 'Failed',
            jobCreatedAt: '23-03-2022 12:45:00',
        },
    ]
    return (
        <>
            <Filter
                pageTitle={"CSV Upload Logs"}
                buttonTitle={"Filter"}
                buttonStyle={"btn-blue "}
                leftIcon={<FilterList />}
                onClick={() => setFilterState(true)}
            />
            <FilterContainer
                open={filterState}
                onClose={() => setFilterState(false)}
                onClear={() => setFilterState(false)}
                onApply={() => setFilterState(false)}
            >
                <div className="filter-form-row">
                    <div className="form-group">
                        <EditText
                            label={'Request ID'}
                            placeholder={'Enter Request ID'}
                            value={''}
                            error={''}
                            maxLength={50}
                            onChange={(text: any) => {
                            }}
                        />
                    </div>
                    <div className="form-group">
                        <AutoComplete
                            label={'Job Type'}
                            placeHolder={'Select Job Type'}
                            value={''}
                            options={[
                                { label: 'Payables', value: 'Payables' } as OptionType, 
                                { label: 'Contacts', value: 'Contacts' } as OptionType
                            ]}
                            onChange={(value: any) => {
                            }}
                        />
                    </div>
                </div>
            </FilterContainer>
            <PageContainer loading={false} listData={data}>
                <TableList
                    tableColumns={logsListingTableColumns(onClickDetailsButton)}
                    currentPage={0}
                    rowsPerPage={25}
                    rowsPerPageOptions={[]}
                    listData={data}
                    onChangePage={() => { }}
                    onRowsPerPageChange={() => { }}
                />
            </PageContainer>
        </>
    )
    function onClickDetailsButton(element: any) {
        history.push({
            pathname: logDetailsRoute,
        });
    }
}

export default LogsListing