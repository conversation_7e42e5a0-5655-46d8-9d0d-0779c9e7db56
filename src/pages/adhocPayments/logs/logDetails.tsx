import React from 'react'
import Filter from '../../../component/filter/Filter'
import { KeyboardBackspace } from '@material-ui/icons'
import { isMobile } from '../../../base/utility/ViewUtils'
import { useHistory } from 'react-router'

const logDetails = () => {
    const history = useHistory();
    return (
        <>
            <Filter
                pageTitle={"CSV Upload Log Details"}
                buttonTitle={isMobile ? " " : "Back"}
                buttonStyle={isMobile ? "btn-detail-mob" : "btn-detail btn-rounded"}
                leftIcon={<KeyboardBackspace />}                
                onClick={() => {
                        history.goBack()
                    }
                }
            />
            
        </>
    )
}

export default logDetails