.payable_bill_details{
    :global .filter-panel{
        padding: 12px 24px;
    }
    .bill_detail{
        background: rgb(237 237 237 / 31%);
        border: 1px solid rgb(112 112 112 / 12%);
        border-radius: 2px;
        padding: 15px 12px;
        margin: 12px;
        .bill_form_box{
            background: #FFF;
            border: 1px solid #08365421;
            border-radius: 6px;
            padding: 15px;
            :global .input-wrap{
                display: flex;
                align-items: center;
                label{
                    flex: 0 0 41.666667%;
                    max-width: 41.666667%;
                    justify-content: flex-end;
                    padding-right: 12px;
                    margin: 0;
                    color: rgb(51 51 51 / 85%);
                    flex-direction: column;
                    align-items: flex-end !important;
                    span + span{
                        font-size: 11px;
                        color: rgb(43 43 43 / 45%);
                    }
                }
                .MuiFormControl-root{ 
                    .MuiInputBase-root{
                        border: 1px solid #77777798;
                        height: 35px;
                        border-radius: 0;
                    }
                }
            }
            .tds_text{
                font-size: 11px;
                letter-spacing: 0px;
                color: rgb(43 43 43 / 55%);
                font-style: italic;
                margin: 0;
            }
        }
        .amount_table{
            margin: 0;
            td{padding: 2px 5px;}
            .amount_label{
                font-size: 12px;
                color: rgb(43 43 43 / 78%);
                text-align: right;
            }
            .amount_value{
                font-size: 14px;
                color: #121212;
                font-weight: 500;
                text-align: left;
                width: 25%;
            }
            .total_label{
                font-size: 14px;
                color: #121212;
            }
        }
    }
    .payment_method{
        padding: 15px 12px;
        background: #FFFFFF;    
        box-shadow: 0px 2px 6px #0000001F;
        border-top: 1px solid rgb(112 112 112 / 35%);
        .payment_method_box{
            background: #0836540D;
            border: 1px solid #7070702E;
            border-radius: 5px;
            padding: 15px;
            :global .autocomplete-wrap{
                label{
                    color: #101010;
                }
            }
        }
    }
    .alerts_wrapper{
        padding: 15px;
    }
    .bill_btns_wrap{
        padding: 20px 15px;
        gap: 10px;
        .btn_bill{
            flex: 1;
            :global .btn{
                width: 100%;
            }
        }
    }
}
