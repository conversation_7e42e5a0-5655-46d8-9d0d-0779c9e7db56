import React from 'react'
import Filter from '../../../component/filter/Filter'
import Styles from './PayableBillDetails.module.scss'
import NumberEditText from '../../../component/widgets/NumberEditText'
import AutoComplete from '../../../component/widgets/AutoComplete'
import Button from '../../../component/widgets/button/Button'
import { CheckCircle, Close } from '@material-ui/icons'
import EditText from '../../../component/widgets/EditText'
import { Alert } from '@material-ui/lab'


const PayableBillDetails = () => {
  return (
    <div className={Styles.payable_bill_details}>
      <Filter
        pageTitle="Payable"
      />
      <div className={Styles.bill_detail}>
        <div className={Styles.bill_form_box}>
          <div className="mb-2">
            <NumberEditText
              label={'Bill Amount'}
              placeholder={'Enter Bill Amount'}
              value={''}
              error={''}
              maxLength={50}
              onChange={() => {

              }}
            />
          </div>
          <div className="mb-2">
            <NumberEditText
              label={'GST'}
              placeholder={'Enter GST'}
              value={''}
              error={''}
              maxLength={50}
              subtitle="(Bill Amount GST)"
              onChange={() => {

              }}
            />
          </div>
          <div className="mb-2">
            <NumberEditText
              label={'Base Amount'}
              placeholder={'Enter Base Amount'}
              value={''}
              error={''}
              maxLength={50}
              onChange={() => {

              }}
            />
          </div>
          <div className="mb-2">
            <EditText
              label={'PAN Card Number'}
              placeholder={'Enter PAN Card Number'}
              value={''}
              error={''}
              maxLength={50}
              onChange={() => {

              }}
            />
          </div>
          <div>
            <EditText
              label={'TDS Rate (%)'}
              placeholder={'Enter TDS Rate (%)'}
              value={''}
              error={''}
              maxLength={50}
              onChange={() => {

              }}
            />
            <div className='row justify-content-end no-gutters'>
              <div className="col-md-7"><p className={Styles.tds_text}>TDS will be calculated on base amount</p></div>
            </div>
          </div>
        </div>
        <div className="mt-2">
          <table className={` ${Styles.amount_table} table table-borderless`}>
            <tbody>
              <tr>
                <td className={`${Styles.amount_label} text-end`}>Base Amount:</td>
                <td className={`${Styles.amount_value} text-end`}>₹5000</td>
              </tr>
              <tr>
                <td className={`${Styles.amount_label} text-end`}>GST:</td>
                <td className={`${Styles.amount_value} text-end`}>₹ 0.00</td>
              </tr>
              <tr>
                <td className={`${Styles.amount_label} text-end`}>TDS:</td>
                <td className={`${Styles.amount_value} text-end`}>- ₹ 50</td>
              </tr>
              <tr>
                <td colSpan={2}>
                  <hr className="mt-2 mb-2" />
                </td>
              </tr>
              <tr>
                <td className={`${Styles.amount_label} ${Styles.total_label}`}>Payable Amount:</td>
                <td className={`${Styles.amount_value} orange-text`}>₹ 4950</td>
              </tr>
            </tbody>
          </table>
        </div>

      </div>

      {/* payment method */}
      <div className={Styles.payment_method}>
        <div className={Styles.payment_method_box}>
          <div className="mb-3">
            <AutoComplete
              label={'Payment Method'}
              mandatory
              placeHolder={'Select Payment Method'}
              options={[]}
              value={''}
              error={''}
              onChange={() => {

              }}
            />
          </div>
          <div className="mb-3">
            <AutoComplete
              label={'Bank'}
              mandatory
              placeHolder={'Select Bank'}
              options={[]}
              value={''}
              error={''}
              onChange={() => {

              }}
            />
          </div>
          <div>
            <AutoComplete
              label={'Payment Mode'}
              mandatory
              placeHolder={'Select Payment Mode'}
              options={[]}
              value={''}
              error={''}
              onChange={() => {

              }}
            />
          </div>
        </div>
      </div>
      
      {/* alerts */}
      <div className={Styles.alerts_wrapper}>
        <Alert severity="warning" icon={false} className='orange-text justify-content-center pt-1 pb-1'>2 Approvals required for this Payable</Alert>
        <Alert severity="success" 
          className='green-text justify-content-center mb-1 pt-1 pb-1'
          iconMapping={{ success: <CheckCircle fontSize="inherit" /> }}
          >First level Approbval done by Manjo Kumar</Alert>
        <Alert severity="warning" icon={false} className='orange-text justify-content-center pt-1 pb-1'>
           2nd level Approval Pending...</Alert>
      </div>

      {/* buttons */}
      <div className={`${Styles.bill_btns_wrap} d-flex justify-content-between`}>
        <Button
          title='Cancel'
          buttonStyle="btn-detail mr-2 mb-2"
          buttonContainerStyle={Styles.btn_bill}
          leftIcon={<Close />}
          onClick={() => { }}
        />
        <Button
          title='Reject'
          buttonStyle="btn-red--reject mr-2 mb-2"
          buttonContainerStyle={Styles.btn_bill}
          leftIcon={<Close />}
          onClick={() => { }}
        />
        <Button
          title='Approve'
          buttonStyle="btn-blue"
          buttonContainerStyle={Styles.btn_bill}
          leftIcon={<CheckCircle />}
          onClick={() => { }}
        />
      </div>

    </div>
  )
}

export default PayableBillDetails