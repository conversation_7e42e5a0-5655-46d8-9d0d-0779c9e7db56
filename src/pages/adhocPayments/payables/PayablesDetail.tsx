import React from 'react'
import Filter from '../../../component/filter/Filter';
import { isMobile } from '../../../base/utility/ViewUtils';
import { KeyboardBackspace } from '@material-ui/icons';
import { useHistory } from 'react-router';
import { Card, CardContent } from '@material-ui/core';
import Information from '../../../component/information/Information';
import PageContainer from '../../../component/pageContainer/PageContainer';
import CardContentSkeleton from '../../../component/widgets/cardContentSkeleton/CardContentSkeleton';
import { accountNoTitle, addressTitle, bankTitle, beneficiaryNameTitle, billNumberTitle, contactEmailTitle, contactNameTitle, contactPhoneTitle, contactTypeTitle, documentTitle, ifscTtitle, narrationTitle, panCardNumberTitle, paymentCategoryTitle, paymentModeTitle, payoutIdTitle, referenceIdTitle, statusTitle, utrNoLabel } from '../../../base/constant/MessageUtils';
import { InfoTooltip } from '../../../component/widgets/tooltip/InfoTooltip';
import { OverflowTip } from '../../../component/widgets/tooltip/OverFlowToolTip';
import TableList from '../../../component/widgets/tableView/TableList';
import { payablesDetailTableColumns } from '../../../templates/PayablesDetailTemplate';
import './PayablesDetail.scss'
import PayableBillDetails from './PayableBillDetails';

const PayablesDetail = () => {
    const history = useHistory();
     const [loading, setLoading] = React.useState<boolean>(false);

    return (
        <div className='payables-detail-wrapper'>
            <div className="container-fluid">
                <div className='row main-section'>
                    <div className="page-section col">
                        <Filter
                            pageTitle={
                                <>
                                    <div className="d-flex align-item-center legacy-heading">
                                        <div className="legacy-currency">
                                            <span>₹</span>
                                        </div>
                                        <div className="legacy-balance">
                                            <span className="legacy-name">Request Amount</span>
                                            <p className="m-0 legacy-price">₹ 34000.00</p>
                                        </div>
                                    </div>
                                </>
                            }
                            buttonStyle={isMobile ? "btn-detail-mob" : "btn-detail btn-rounded"}
                            buttonTitle={isMobile ? " " : "Back"}
                            leftIcon={<KeyboardBackspace />}
                            //   disable={pollingLoader}
                            onClick={() => {
                                history.goBack();
                            }}
                        >
                        </Filter>
                        <PageContainer>
                            {loading ? (
                                <CardContentSkeleton
                                    row={3}
                                    column={3}
                                />
                            ) : (
                                    <>
                                        <Card className="card-wrapper request-listing--orderDetails">
                                            <div className="bpcl-wallet--heading">
                                                <div className="id-wrap d-md-flex">
                                                    <span className="title-label">Contact Details</span>
                                                </div>
                                            </div>
                                            <CardContent>
                                                <div className="row">
                                                    <div className="col-md-3 card-group col-6">
                                                        <Information
                                                            title={contactNameTitle}
                                                            // text="Vijay Kumar"
                                                            customView={
                                                                <OverflowTip
                                                                    text={'Vijay Kumarkfrkfmkrlmfklrmfklmrkf'}
                                                                />
                                                            }
                                                        />
                                                    </div>
                                                    <div className="col-md-3 card-group col-6">
                                                        <Information
                                                            title={contactPhoneTitle}
                                                            text="9876543210"
                                                        />
                                                    </div>
                                                    <div className="col-md-3 card-group col-6">
                                                        <Information
                                                            title={contactTypeTitle}
                                                            text="Vendor"
                                                        />
                                                    </div>
                                                    <div className="col-md-3 card-group col-6">
                                                        <Information
                                                            title={contactEmailTitle}
                                                            // text="<EMAIL>"
                                                            customView={
                                                                <InfoTooltip
                                                                    title={'<EMAIL>'}
                                                                    placement={"top"}
                                                                    disableInMobile={"false"}
                                                                    infoText={'<EMAIL>'}
                                                                />
                                                            }
                                                        />
                                                    </div>
                                                    <div className="col-md-3 card-group col-6">
                                                        <Information
                                                            title={addressTitle}
                                                            // text="AB-15/9, Ram chowk, New Delhi-110045"
                                                            customView={
                                                                <InfoTooltip
                                                                    title={'AB-15/9, Ram chowk, New Delhi-110045'}
                                                                    placement={"top"}
                                                                    disableInMobile={"false"}
                                                                    infoText={'AB-15/9, Ram chowk, New Delhi-110045'}
                                                                />
                                                            }
                                                        />
                                                    </div>
                                                    <div className="col-md-3 card-group col-6">
                                                        <Information
                                                            title={beneficiaryNameTitle}
                                                            // text="Vijay Kumar"
                                                            customView={
                                                                <InfoTooltip
                                                                    title={'Vijay Kumar'}
                                                                    placement={"top"}
                                                                    disableInMobile={"false"}
                                                                    infoText={'Vijay Kumar'}
                                                                />
                                                            }
                                                        />
                                                    </div>
                                                    <div className="col-md-3 card-group col-6">
                                                        <Information
                                                            title={accountNoTitle}
                                                            text="************"
                                                        />
                                                    </div>
                                                    <div className="col-md-3 card-group col-6">
                                                        <Information
                                                            title={ifscTtitle}
                                                            text="IPOS0000001"
                                                        />
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                        
                                        {/* Payable Details */}
                                        <Card className="card-wrapper request-listing--orderDetails">
                                            <div className="bpcl-wallet--heading">
                                                <div className="id-wrap d-md-flex">
                                                    <span className="title-label">Payable Details</span>
                                                </div>
                                            </div>
                                            <CardContent>
                                                <div className="row">
                                                    <div className="col-md-6">
                                                        <div className="row">
                                                            <div className="col-md-6 card-group col-6">
                                                                <Information
                                                                    title={billNumberTitle}
                                                                    customView={
                                                                        <InfoTooltip
                                                                            title={'054236'}
                                                                            placement={"top"}
                                                                            disableInMobile={"false"}
                                                                            infoText={'054236'}
                                                                        />
                                                                    }
                                                                />
                                                            </div>
                                                            <div className="col-md-6 card-group col-6">
                                                                <Information
                                                                    title={narrationTitle}
                                                                    // text="<EMAIL>"
                                                                    customView={
                                                                        <InfoTooltip
                                                                            title={'Camions Logistics'}
                                                                            placement={"top"}
                                                                            disableInMobile={"false"}
                                                                            infoText={'Camions Logistics'}
                                                                        />
                                                                    }
                                                                />
                                                            </div>
                                                            <div className="col-md-6 card-group col-6">
                                                                <Information
                                                                    title={paymentCategoryTitle}
                                                                    text="Vendor"
                                                                />
                                                            </div>
                                                            <div className="col-md-6 card-group col-6">
                                                                <Information
                                                                    title={panCardNumberTitle}
                                                                    text="**********"
                                                                />
                                                            </div>
                                                            <div className="col-md-6 card-group col-6">
                                                                <Information
                                                                    title={referenceIdTitle}
                                                                    // text="AB12345678"
                                                                    customView={
                                                                        <InfoTooltip
                                                                            title={'AB12345678'}
                                                                            placement={"top"}
                                                                            disableInMobile={"false"}
                                                                            infoText={'AB12345678'}
                                                                        />
                                                                    }
                                                                />
                                                            </div>
                                                            <div className="col-md-6 card-group col-6">
                                                                <Information
                                                                    title={documentTitle}
                                                                    customView={<span>View</span>}
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="col-md-6">
                                                        <div className="table-detail-listing">
                                                            <TableList
                                                                tableColumns={payablesDetailTableColumns()}
                                                                currentPage={0}
                                                                rowsPerPage={0}
                                                                rowsPerPageOptions={[]}
                                                                listData={[1]}
                                                                onChangePage={() => { }}
                                                                onRowsPerPageChange={() => { }}
                                                            /> 
                                                        </div> 
                                                        <div className="total_wrap">
                                                            <div className="container">
                                                                <div className="row">
                                                                    <div className="col">
                                                                        <h6 className='title d-flex align-items-center'>
                                                                            Payable Amount<span className='divider'></span>
                                                                        </h6>
                                                                    </div>
                                                                    <div className="col-auto d-flex align-items-center ps-1 flex-wrap">
                                                                        <div className='vl_list'>
                                                                            <label className='label'>Base Amount:</label>
                                                                            <span className='value'>₹ 5000</span>
                                                                        </div>
                                                                        <div className='vl_list'>
                                                                            <span className='value'>+</span>
                                                                        </div>
                                                                        <div className='vl_list'>
                                                                            <label className='label'>GST:</label>
                                                                            <span className='value gst_input'>₹ 5000</span>
                                                                        </div>
                                                                        <div className='vl_list'>
                                                                            <span className='value'>-</span>
                                                                        </div>
                                                                        <div className='vl_list'>
                                                                            <label className='label'>TDS:</label>
                                                                            <span className='value gst_input'>₹ 50</span>
                                                                        </div>
                                                                        <div className='vl_list'>
                                                                            <span className='value'>=</span>
                                                                        </div>
                                                                        <div className='vl_list'>
                                                                            <span className='value orange-text'>₹ 5450</span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div> 
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>

                                        {/* Payout Details */}
                                        <Card className="card-wrapper request-listing--orderDetails">
                                            <div className="bpcl-wallet--heading">
                                                <div className="id-wrap d-md-flex">
                                                    <span className="title-label">Payout Details</span>
                                                </div>
                                            </div>
                                            <CardContent>
                                                <div className="row">
                                                    <div className="col-md-3 card-group col-6">
                                                        <Information
                                                            title={payoutIdTitle}
                                                            // text="Vijay Kumar"
                                                            customView={
                                                                <InfoTooltip
                                                                    title={'pout_K9qEXRma1TpMIY'}
                                                                    placement={"top"}
                                                                    disableInMobile={"false"}
                                                                    infoText={'pout_K9qEXRma1TpMIY'}
                                                                />
                                                            }
                                                        />
                                                    </div>
                                                    <div className="col-md-3 card-group col-6">
                                                        <Information
                                                            title={statusTitle}
                                                            text="Processing"
                                                        />
                                                    </div>
                                                    <div className="col-md-3 card-group col-6">
                                                        <Information
                                                            title={utrNoLabel}
                                                            text="NA"
                                                        />
                                                    </div>
                                                    <div className="col-md-3 card-group col-6">
                                                        <Information
                                                            title={bankTitle}
                                                            text="Bank Name"
                                                        />
                                                    </div>
                                                    <div className="col-md-3 card-group col-6">
                                                        <Information
                                                            title={paymentModeTitle}
                                                            text="Payment Mode"
                                                        />
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    </>
                                )
                            }
                            
                        </PageContainer>
                    </div>
                    <div className="drawer-section col-auto">
                        <PayableBillDetails/>
                    </div>
                </div>
            </div>
        </div>

    )
}

export default PayablesDetail