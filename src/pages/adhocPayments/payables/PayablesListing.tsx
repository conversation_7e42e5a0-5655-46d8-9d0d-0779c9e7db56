import React, { useEffect, useReducer } from 'react'
import TableList from '../../../component/widgets/tableView/TableList'
import Filter from '../../../component/filter/Filter'
import { FilterList } from '@material-ui/icons'
import { makeStyles, Tab, Tabs } from '@material-ui/core'
import { isMobile } from '../../../base/utility/ViewUtils'
import { TabPanel } from '../../../component/tabs/TabPanel'
import PageContainer from '../../../component/pageContainer/PageContainer'
import { payableStatusEnum, paybalesListingStatusArray, rowsPerPageOptions } from '../../../base/constant/ArrayList'
import { payablesListingTableColumns } from '../../../templates/PayablesListingTemplate'
import "./PayablesListing.scss";
import Button from '../../../component/widgets/button/Button'
import PayblesFilter from './PayblesFilter'
import { payablesDetailsRoute } from '../../../base/constant/RoutePath'
import PayableListingReducer, { PAYABLE_LISTING_STATE } from './reducers/payableListingReducer/PayableListingReducer'
import { hideLoading, refreshList, setAllRequestsToBeApproved, setCurrentPage, setIsPayableDetailOpen, setRequestToBeApproved, setResponse, setRowPerPage, setSelectedTab, showLoading, toggleFilter } from './reducers/payableListingReducer/PayableListingActions'
import { getAdvanceFilterChips, useQuery } from '../../../base/utility/Routerutils'
import { useHistory, useParams } from 'react-router'
import { payablesListingRoute } from '../../../base/constant/RoutePath'
import { isObjectEmpty } from '../../../base/utility/StringUtils'
import { useSearchParams } from '../../../base/hooks/useSearchParams'
import { payableFilters } from '../../../base/moduleUtility/FilterUtils'
import { getPayableTabStatus } from '../adhocPaymentUtility/adhocPaymentUtility'
import Chips from '../../../component/chips/Chips'

const useStyles = makeStyles({
  indicator: {
    background: "none",
  },
  tabs: {
    "& button[aria-selected='true']": {
      margin: "0 20px",
      borderBottom: "3px solid #F7931E",
    },
  },
});

const PayablesListing = () => {
  const classes = useStyles();
  const [state = PAYABLE_LISTING_STATE, dispatch] = useReducer(PayableListingReducer, PAYABLE_LISTING_STATE);
  const history = useHistory();
  const params = useQuery();
  const { id } = useParams<any>();
  const [filterState, addFiltersQueryParams, removeFiltersQueryParams] = useSearchParams(payableFilters)
  
  const data = [
  {
  "paymentId": "P-1D-1752045907-419743",
  "narration": "Camions Logistics",
  "paymentCategory": "Vendor",
  "requestAmount": "5000.00",
  "contactName": "Manoj Sharma",
  "contactNumber": "9876543210",
  "integrationType": "Razorpay",
  "raisedBy": "<EMAIL>",
  "raisedAt": "23-03-2022 12:4"
},
{
  "paymentId": "P-1D-1752045907-419744",
  "narration": "Camions Logistics",
  "paymentCategory": "Vendor",
  "requestAmount": "5000.00",
  "contactName": "Manoj Sharma",
  "contactNumber": "9876543210",
  "integrationType": "Razorpay",
  "raisedBy": "<EMAIL>",
  "raisedAt": "23-03-2022 12:4"
},
{
  "paymentId": "P-1D-1752045907-419745",
  "narration": "Camions Logistics",
  "paymentCategory": "Vendor",
  "requestAmount": "5000.00",
  "contactName": "Manoj Sharma",
  "contactNumber": "9876543210",
  "integrationType": "Razorpay",
  "raisedBy": "<EMAIL>",
  "raisedAt": "23-03-2022 12:4"
}]
  const handleOpenFilter = () => {
    dispatch(toggleFilter())
  };
  const handleCloseFilter = () => {
    dispatch(toggleFilter());
  };

  const handleRowSelect = (element: any, checked: boolean) => {
        dispatch(setRequestToBeApproved(element, checked));
    }

    const handleAllRowsSelect = (checked: boolean) => {
        dispatch(setAllRequestsToBeApproved(checked))
        
    }

    const responsedata = {
        code: 200,
        message: "Successful",
        // details: {
          pagination: {
            count: data.length,
          },
          data: data,
        // },
      };
      
      function getResponseAfter3Sec() {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(responsedata);
        }, 3000); 
      });
    }
    
      useEffect(() => {
    
        const getList = async () => {
          let queryParams: any = {
            page: state.currentPage,
            size: state.pageSize,
            payableStatus: id
              ? getPayableTabStatus(paybalesListingStatusArray.indexOf(id))
              : payableStatusEnum.PENDING,
          };
          if (!isObjectEmpty(filterState.params)) {
            queryParams = Object.assign(queryParams, filterState.params);
          }
          if (queryParams && queryParams.queryFieldLabel) {
            delete queryParams["queryFieldLabel"];
          }
          dispatch(setSelectedTab(id ? paybalesListingStatusArray.indexOf(id) : 0));
          dispatch(showLoading());
          try {
            const response = await getResponseAfter3Sec();        
            if (response) {
              dispatch(setResponse(response));          
            }
          } catch (error) {
            console.error("Error fetching request list:", error);
          } finally {
            dispatch(hideLoading());
          }
        };
    
        getList();
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [state.refreshList, state.currentPage, state.pageSize, history.location.search, id]);

  return (
    <div className="payables-listing-wrapper">
      <PayblesFilter
        open={state.openFilter}
        filterChips={filterState.chips}
        filterParams={filterState.params}
        onClose={handleCloseFilter}
        onApplyFilter={(filterChips: any, filterParams: any) => {
          dispatch(refreshList());
          dispatch(toggleFilter());
          addFiltersQueryParams(filterChips, filterParams);
        }}
      />
      <Filter
        pageTitle={"Payables Listing"}
        buttonTitle="Filter"
        buttonStyle={"btn-blue"}
        leftIcon={<FilterList />}
        onClick={handleOpenFilter}
      >
        {state.selectedTabName === paybalesListingStatusArray[0] &&
          state.selectedRequestsToBeApproved.requestsList.length > 0 && (
            <Button
              buttonStyle="btn-orange"
              title="Approve"
              count={state.selectedRequestsToBeApproved.requestsList.length}
            />
          )}
      </Filter>
      <div className="tab-nav">
        {
          <div className="d-flex filter-tab">
            <div className="filter-mob">
              <Tabs
                value={state.selectedTabIndex}
                className={classes.tabs}
                classes={{ indicator: classes.indicator }}
                onChange={(event: any, newValue: any) => {
                  if (newValue !== state.selectedTabIndex) {
                    dispatch(setSelectedTab(newValue));
                    dispatch(setCurrentPage(1));
                    dispatch(setRowPerPage(rowsPerPageOptions[0]));
                    history.replace({
                      pathname:
                        payablesListingRoute +
                        paybalesListingStatusArray[newValue],
                      search: params.toString(),
                    });
                  }
                }}
                variant="scrollable"
                scrollButtons={isMobile ? "on" : "off"}
              >
                {paybalesListingStatusArray.map((element, index) => (
                  <Tab key={index} label={element} />
                ))}
              </Tabs>
            </div>
          </div>
        }

        <TabPanel value={0} index={0}>
          <PageContainer loading={state.loading} listData={state.listData}>
            {!isObjectEmpty(getAdvanceFilterChips(filterState.chips)) &&
              Object.keys(getAdvanceFilterChips(filterState.chips)).map(
                (element: any, index: any) => (
                  <Chips
                    key={index}
                    label={filterState.chips[element]}
                    onDelete={() => {
                      dispatch(refreshList());
                      removeFiltersQueryParams([element]);
                    }}
                  />
                )
              )}

            <TableList
              tableColumns={
                state.selectedTabName === paybalesListingStatusArray[0]
                  ? payablesListingTableColumns(
                      state.selectedTabName,
                      onClickDetailsButton,
                      handleRowSelect,
                      handleAllRowsSelect,
                      state
                    )
                  : payablesListingTableColumns(
                      state.selectedTabName,
                      onClickDetailsButton
                    )
              }
              currentPage={state.currentPage}
              rowsPerPage={state.pageSize}
              rowsPerPageOptions={rowsPerPageOptions}
              listData={state.listData}
              totalCount={state.pagination && state.pagination.count}
              onChangePage={(event: any, page: number) => {
                dispatch(setCurrentPage(page));
              }}
              onRowsPerPageChange={(event: any) => {
                dispatch(setRowPerPage(event.target.value));
              }}
            />
          </PageContainer>
        </TabPanel>
      </div>
    </div>
  )

   function onClickDetailsButton(element: any) {
     dispatch(setIsPayableDetailOpen(true));
      history.push({
        pathname: payablesDetailsRoute,
      });
    }
}

export default PayablesListing