import React, { useEffect, useReducer } from 'react'
import FilterContainer from '../../../modals/FilterModal/FilterContainer'
import EditText from '../../../component/widgets/EditText';
import AutoComplete from '../../../component/widgets/AutoComplete';
import PayableListingFilterReducer, { PAYABLE_LISTING_FILTER_STATE } from './reducers/payableListingFilterReducer/PayableListingFilterReducer';
import { resetState, setError, setFilterParams, setFilterValues, setIsFilterChanged } from './reducers/payableListingFilterReducer/PayableListingFilterActions';
import { isObjectEmpty } from '../../../base/utility/StringUtils';

interface PayablesListingFiltersProps {
    open: boolean,
    onClose: any,
    onApplyFilter: any,
    filterChips: any,
    filterParams: any
}

const PayblesFilter = (props: PayablesListingFiltersProps) => {
    const { open, onClose, onApplyFilter, filterChips, filterParams } = props;
    const [state = PAYABLE_LISTING_FILTER_STATE, dispatch] = useReducer(PayableListingFilterReducer, PAYABLE_LISTING_FILTER_STATE);

    function setValues(chips: any, params?: any) {
    dispatch(setFilterValues({ ...state.filterValues, ...chips }));
    dispatch(setError({}));
    if (params) {
      dispatch(setFilterParams({ ...state.filterParams, ...params }));
    }
    dispatch(setIsFilterChanged(true));
  }

  function onApply() {
    if (!isObjectEmpty(state.filterParams)) {
      if (state.isFilterChanged) {
        dispatch(setError({}));
        onApplyFilter(state.filterValues, state.filterParams);
      } else {
        dispatch(setError({}));
        onClose();
      }
    } else {
      dispatch(
        setError(
          { paymentId: "Enter Payment Id" }
        )
      );
    }
  }

    useEffect(() => {
      if (open) {
        dispatch(setFilterValues(filterChips));
        dispatch(setFilterParams(filterParams));
        dispatch(setIsFilterChanged(false));
      }
    }, [open, filterChips, filterParams]);
  
    return (
        <div className='payables-filter'>
            <FilterContainer
                open={open}
                onClose={() => {
                    onClose();
                    dispatch(setError({}));
                }}
                onClear={() => {
                    dispatch(resetState());
                }}
                onApply={onApply}
            >
                <div className="filter-form-row">
                    <div className="form-group">
                        <EditText
                            label={'Payment ID'}
                            placeholder={'Enter Payment ID'}
                            value={state?.filterValues && state?.filterValues.paymentId}
                            error={state.error.paymentId}
                            maxLength={50}
                            onChange={(text: any) => {
                                setValues({ paymentId: text }, { paymentId: text.trim() });
                            }}
                        />
                    </div>
                    <div className="form-group">
                        <EditText
                            label={'Reference ID'}
                            placeholder={'Enter Reference ID'}
                            value={state?.filterValues && state?.filterValues.referenceId}
                            error={state.error.referenceId}
                            maxLength={50}
                            onChange={(text: any) => {
                                setValues({ referenceId: text }, { referenceId: text.trim() });
                            }}
                        />
                    </div>
                    <div className="form-group">
                        <AutoComplete
                            label={'Payment Category'}
                            placeHolder={'Select Payment Category'}
                            options={state.paymentCategoryList}
                            value={state?.filterValues && state?.filterValues.paymentCategory}
                            error={state.error.paymentCategory}
                            onChange={(value: any) => {
                                setValues({ paymentCategory: value.label }, { paymentCategory: value.label.trim() });
                            }}
                        />
                    </div>
                    <div className="form-group">
                        <AutoComplete
                            label={'Integration Type'}
                            placeHolder={'Select Integration Type'}
                            options={state.integrationTypeList}
                            value={state?.filterValues && state?.filterValues.integrationType}
                            error={state.error.integrationType}
                            onChange={(value: any) => {
                                setValues({ integrationType: value.label }, { integrationType: value.label.trim() });
                            }}
                        />
                    </div>
                    <div className="form-group">
                        <AutoComplete
                            label={'Contact Name'}
                            placeHolder={'Select Contact Name'}
                            options={state.contactNameList}
                            value={state?.filterValues && state?.filterValues.contactName}
                            error={state.error.contactName}
                            onChange={(value: any) => {
                                setValues({ contactName: value.label }, { contactName: value.label.trim() });
                            }}
                        />
                    </div>
                    <div className="form-group">
                        <AutoComplete
                            label={'Contact Number'}
                            placeHolder={'Select Contact Number'}
                            options={state.contactNumberList}
                            value={state?.filterValues && state?.filterValues.contactNumber}
                            error={state.error.contactNumber}
                            onChange={(value: any) => {
                                setValues({ contactNumber: value.label }, { contactNumber: value.label.trim() });
                            }}
                        />
                    </div>
                    <div className="form-group">
                        <EditText
                            label={'Raised By'}
                            placeholder={'Raised By'}
                            value={state?.filterValues && state?.filterValues.raisedBy}
                            error={state.error.raisedBy}
                            maxLength={50}
                            onChange={(text: any) => {
                                setValues({ raisedBy: text }, { raisedBy: text.trim() });
                            }}
                        />
                    </div>
                </div>
            </FilterContainer>
        </div>
    )
}

export default PayblesFilter