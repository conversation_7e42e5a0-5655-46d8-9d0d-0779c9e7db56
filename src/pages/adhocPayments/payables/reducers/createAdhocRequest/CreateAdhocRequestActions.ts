import { CreateAdhocRequestState } from "./CreateAdhocRequestReducer";
import CreateAdhocRequestActionTypes from "./CreateAdhocRequestActionTypes";

export const setFormData = (
    field: keyof CreateAdhocRequestState['formData'],
    value: unknown
) => ({
    type: CreateAdhocRequestActionTypes.SET_FORM_DATA,
    field,
    value,
});

export const setValue = (
    field: Exclude<keyof CreateAdhocRequestState, 'formData' | 'errors'>,
    value: unknown
) => ({
    type: CreateAdhocRequestActionTypes.SET_VALUE,
    valueType: field,
    value,
});

export const setErrors = (
    errors: Partial<Record<keyof CreateAdhocRequestState['formData'], string | undefined>>
) => ({
    type: CreateAdhocRequestActionTypes.SET_ERRORS,
    errors,
});