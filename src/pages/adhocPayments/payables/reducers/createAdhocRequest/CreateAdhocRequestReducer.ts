import CreateAdhocRequestActionTypes from "./CreateAdhocRequestActionTypes";
import { createReducer } from "reduxsauce";
import { OptionType } from "../../../../../component/widgets/widgetsInterfaces";

export type UploadDocumentType = {
    files: File[];
    totalSize: number;
    totalFilesCount: number;
}
export type CreateAdhocRequestState = {
    formData: {
        contact: OptionType | undefined;
        paymentMethod: OptionType | undefined;
        amount: string;
        billNo: string;
        narration: string;
        paymentCategory: OptionType | undefined;
        panNumber: string;
        referenceId: string;
        documents: UploadDocumentType;
    },
    contacts: OptionType[];
    paymentMethods: OptionType[];
    isContactLoading: boolean;
    isPaymentMethodLoading: boolean;
    isDocumentUploading: boolean;
    isCreateRequestLoading: boolean;
    showDownloadCsvSampleModal: boolean;
    isBulkCreateLoading: boolean;
    errors: Partial<Record<keyof CreateAdhocRequestState['formData'], string | undefined>>;
}

export const CREATE_ADHOC_REQUEST_STATE: CreateAdhocRequestState = {
    formData: {
        contact: undefined,
        paymentMethod: undefined,
        amount: '',
        billNo: '',
        narration: '',
        paymentCategory: undefined,
        panNumber: '',
        referenceId: '',
        documents: {
            files: [],
            totalSize: 0,
            totalFilesCount: 0,
        },
    },
    contacts: [],
    paymentMethods: [],
    isContactLoading: false,
    isPaymentMethodLoading: false,
    isDocumentUploading: false,
    isCreateRequestLoading: false,
    showDownloadCsvSampleModal: false,
    isBulkCreateLoading: false,
    errors: {},
};

const setFormDataReducer = (
    state = CREATE_ADHOC_REQUEST_STATE,
    action: {
        type: string
        field: keyof CreateAdhocRequestState['formData'],
        value: unknown
    }
) => {
    const { field, value } = action;

    if (field === 'contact') {
        return {
            ...state,
            paymentMethods: [],
            formData: {
                ...state.formData,
                [field]: value as OptionType | undefined,
                paymentMethod: undefined, // Reset payment method when contact changes
            },
            errors: {
                ...state.errors,
                contact: undefined,
            },
        };
    } else {
        return {
            ...state,
            formData: {
                ...state.formData,
                [action.field]: action.value,
            },
            errors: {
                ...state.errors,
                [action.field]: undefined,
            },
        };
    }
};

// const showOrHideDownloadCsvSampleModalReducer = (state = CREATE_ADHOC_REQUEST_STATE, action: any) => ({
//     ...state,
//     showDownloadCsvSampleModal: action.value,
// });

const setValueReducer = (
    state = CREATE_ADHOC_REQUEST_STATE,
    action: {
        type: string
        valueType: Exclude<keyof CreateAdhocRequestState, 'formData' | 'errors'>;
        value: unknown
    }
) => ({
    ...state,
    [action.valueType]: action.value,
})

const setErrorsReducer = (
    state = CREATE_ADHOC_REQUEST_STATE,
    action: {
        type: string
        errors: Partial<Record<keyof CreateAdhocRequestState['formData'], string | undefined>>
    }
) => ({
    ...state,
    errors: action.errors,
})

const ACTION_HANDLERS = {
    [CreateAdhocRequestActionTypes.SET_FORM_DATA]: setFormDataReducer,
    [CreateAdhocRequestActionTypes.SET_VALUE]: setValueReducer,
    [CreateAdhocRequestActionTypes.SET_ERRORS]: setErrorsReducer,
}

const CreateAdhocRequestReducer = createReducer(CREATE_ADHOC_REQUEST_STATE, ACTION_HANDLERS);

export default CreateAdhocRequestReducer;