import ContactListingFilterTypes from "./PayableListingFilterTypes";

export const setFilterValues = (value: any) => ({
    type: ContactListingFilterTypes.SET_FILTER_VALUES,
    value,
});

export const setFilterParams = (value: any) => ({
    type: ContactListingFilterTypes.SET_FILTER_PARAMS,
    value,
})

export const setPaymentCategoryList = (value: any) => ({
    type: ContactListingFilterTypes.SET_PAYMENT_CATEGORY_LIST,
    value,
})

export const setIntegrationTypeList = (value: any) => ({
    type: ContactListingFilterTypes.SET_INTEGRATION_TYPE_LIST,
    value,
})

export const setContactNumberList = (value: any) => ({
    type: ContactListingFilterTypes.SET_CONTACT_NUMBER_LIST,
    value,
})

export const setContactNameList = (value: any) => ({
    type: ContactListingFilterTypes.SET_CONTACT_NAME_LIST,
    value,
})

export const setIsFilterChanged = (value: any) => ({
    type: ContactListingFilterTypes.SET_IS_FILTER_CHANGED,
    value,
})

export const setError = (value: any) => ({
    type: ContactListingFilterTypes.SET_ERROR,
    value,
})

export const resetState = () => ({
    type: ContactListingFilterTypes.CLEAR_STATE,
})