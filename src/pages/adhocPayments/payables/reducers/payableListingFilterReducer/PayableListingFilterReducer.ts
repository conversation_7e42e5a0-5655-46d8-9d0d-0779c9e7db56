import { OptionType } from "../../../../../component/widgets/widgetsInterfaces";
import { createReducer } from "reduxsauce";
import PayableListingFilterTypes from "./PayableListingFilterTypes";

export interface PayableListingFilterState {
    filterValues: any,
    filterParams: any,
    error: any,
    isFilterChanged: boolean,
    paymentCategoryList: Array<OptionType>,
    integrationTypeList: Array<OptionType>,
    contactNumberList: Array<OptionType>,
    contactNameList: Array<OptionType>,
}

export const PAYABLE_LISTING_FILTER_STATE: PayableListingFilterState = {
    filterValues: {},
    filterParams: {},
    error: {},
    isFilterChanged: false,
    paymentCategoryList: [],
    integrationTypeList: [],
    contactNumberList: [],
    contactNameList: [],
}

const setFilterValuesReducer = (state = PAYABLE_LISTING_FILTER_STATE, action: any) => ({
    ...state,
    filterValues: action.value
})

const setFilterParamsReducer = (state = PAYABLE_LISTING_FILTER_STATE, action: any) => ({
    ...state,
    filterParams: action.value
})

const setPaymentCategoryListReducer = (state = PAYABLE_LISTING_FILTER_STATE, action: any) => ({
    ...state,
    paymentCategoryList: action.value
})

const setIntegrationTypeListReducer = (state = PAYABLE_LISTING_FILTER_STATE, action: any) => ({
    ...state,
    integrationTypeList: action.value
})

const setContactNumberListReducer = (state = PAYABLE_LISTING_FILTER_STATE, action: any) => ({
    ...state,
    contactNumberList: action.value
})

const setcontactNameListReducer = (state = PAYABLE_LISTING_FILTER_STATE, action: any) => ({
    ...state,
    contactNameList: action.value
})

const setIsFilterChangedReducer = (state = PAYABLE_LISTING_FILTER_STATE, action: any) => ({
    ...state,
    isFilterChanged: action.value
})

const setErrorReducer = (state = PAYABLE_LISTING_FILTER_STATE, action: any) => ({
    ...state,
    error: action.value
})

const resetStateReducer = () => PAYABLE_LISTING_FILTER_STATE;

const ACTION_HANDLERS = {
    [PayableListingFilterTypes.SET_FILTER_VALUES]: setFilterValuesReducer,
    [PayableListingFilterTypes.SET_FILTER_PARAMS]: setFilterParamsReducer,
    [PayableListingFilterTypes.SET_PAYMENT_CATEGORY_LIST]: setPaymentCategoryListReducer,
    [PayableListingFilterTypes.SET_INTEGRATION_TYPE_LIST]: setIntegrationTypeListReducer,
    [PayableListingFilterTypes.SET_CONTACT_NUMBER_LIST]: setContactNumberListReducer,
    [PayableListingFilterTypes.SET_CONTACT_NAME_LIST]: setcontactNameListReducer,
    [PayableListingFilterTypes.SET_IS_FILTER_CHANGED]: setIsFilterChangedReducer,
    [PayableListingFilterTypes.SET_ERROR]: setErrorReducer,
    [PayableListingFilterTypes.CLEAR_STATE]: resetStateReducer
}

export default createReducer(PAYABLE_LISTING_FILTER_STATE, ACTION_HANDLERS);