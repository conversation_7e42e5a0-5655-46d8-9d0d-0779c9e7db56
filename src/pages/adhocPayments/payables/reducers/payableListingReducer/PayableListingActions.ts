import PayableListingTypes from "./PayableListingTypes";

export const toggleFilter = () => ({
    type: PayableListingTypes.TOGGLE_FILTER,
});

export const toggleModal = () => ({
    type: PayableListingTypes.TOGGLE_MODAL,
});

export const setSelectedElement = (value: any) => ({
    type: PayableListingTypes.SELECTED_ELEMENT,
    value,
});

export const setSelectedTab = (value: any) => ({
    type: PayableListingTypes.SELECTED_TAB_INDEX,
    value
});
export const setIsPayableDetailOpen = (value: any) => ({
    type: PayableListingTypes.OPEN_PAYABLE_DETAILS,
    value
});

export const setResponse = (response: any) => ({
    type: PayableListingTypes.SET_RESPONSE,
    response,
});

export const setCurrentPage = (value: any) => ({
    type: PayableListingTypes.SET_CURRENT_PAGE,
    value
});

export const refreshList = () => ({
    type: PayableListingTypes.REFRESH_LIST,
});

export const setRowPerPage = (value: any) => ({
    type: PayableListingTypes.SET_ROW_PER_PAGE,
    value
});

export const showLoading = () => ({
    type: PayableListingTypes.SHOW_LOADING,
});

export const hideLoading = () => ({
    type: PayableListingTypes.HIDE_LOADING,
});

export const toggleBulkUpload = () => ({
    type: PayableListingTypes.TOGGLE_BULK_UPLOAD,
});

export const handleViewDocumentModal = (isOpen: boolean, documentLinks: any) => ({
    type: PayableListingTypes.HANDLE_DOCUMENT_MODAL,
    isOpen,
    documentLinks
})

export const setRequestToBeApproved = (element: any, isChecked: boolean) => ({
    type: PayableListingTypes.SET_REQUEST_TO_BE_APPROVED,
    element,
    isChecked
})

export const setAllRequestsToBeApproved = (isChecked: boolean) => ({
    type: PayableListingTypes.SET_ALL_REQUESTS_TO_BE_APPROVED,
    isChecked
})

export const resetReactiveRepairListingState = () => ({
    type: PayableListingTypes.RESET_WHEN_MOVE_TO_ENROUTE,
});