import { createReducer } from "reduxsauce";
import { paybalesListingStatusArray, rowsPerPageOptions } from "../../../../../base/constant/ArrayList";
import { isMobile } from "../../../../../base/utility/ViewUtils";
import PayableListingTypes from "./PayableListingTypes";

export interface SelectedRows {
    payableId: string | null,
    requestsList: Array<any>,
    isAllSelected: boolean
}

interface PayableListingState {
    openFilter: boolean,
    openBulkUpload: boolean,
    selectedItem: any,
    pagination: any,
    listData: any,
    openModal: boolean,
    selectedTabIndex: number,
    selectedTabName: any
    currentPage: number,
    refreshList: boolean,
    loading: boolean,
    pageSize: number,
    isPayableDetailsOpen: Boolean,
    selectedRowIndex: number,
    selectedRequestsToBeApproved: SelectedRows,
}

export const PAYABLE_LISTING_STATE: PayableListingState = {
    openFilter: false,
    openBulkUpload: false,
    selectedItem: undefined,
    pagination: undefined,
    listData: undefined,
    openModal: false,
    currentPage: 1,
    selectedTabIndex: 0,
    selectedTabName: paybalesListingStatusArray[0],
    refreshList: false,
    loading: false,
    pageSize: rowsPerPageOptions[0],
    isPayableDetailsOpen: false,
    selectedRowIndex:0,
    selectedRequestsToBeApproved: {
        payableId: null,
        requestsList: [],
        isAllSelected: false
    },
}

const toggleFilterReducer = (state = PAYABLE_LISTING_STATE) => ({
    ...state,
    openFilter: !state.openFilter
});

const toggleModalReducer = (state = PAYABLE_LISTING_STATE) => ({
    ...state,
    openModal: !state.openModal
});

const setSelectedElementReducer = (state = PAYABLE_LISTING_STATE, action: any) => ({
    ...state,
    selectedItem: action.value
});

const setResponseReducer = (state = PAYABLE_LISTING_STATE, action: any) => ({
    ...state,
    pagination: action.response && action.response.pagination,
    listData: isMobile ?
        (state.listData ? [...state.listData, ...action.response && action.response.data] : action.response && action.response.data)
        : action.response && action.response.data,
});

const setCurrentPageReducer = (state = PAYABLE_LISTING_STATE, action: any) =>  ({
    ...state,
    currentPage: action.value,
    selectedRequestsToBeApproved: {
        payableId: null,
        requestsList: [],
        isAllSelected: false
    }
});

const setIsPayablesDetailsOpen = (state = PAYABLE_LISTING_STATE, action: any) => ({
    ...state,
    isPayableDetailsOpen: action.value
});

const refreshListReducer = (state = PAYABLE_LISTING_STATE) => ({
    ...state,
    refreshList: !state.refreshList,
    currentPage: 1,
    listData: undefined,
});

const setRowPerPageReducer = (state = PAYABLE_LISTING_STATE, action: any) => ({
    ...state,
    pageSize: action.value,
    currentPage: 1,
    listData: undefined,
    selectedRequestsToBeApproved: {
        payableId: null,
        requestsList: [],
        isAllSelected: false
    }
});

const showLoadingReducer = (state = PAYABLE_LISTING_STATE) => ({
    ...state,
    loading: true
});

const hideLoadingReducer = (state = PAYABLE_LISTING_STATE) => ({
    ...state,
    loading: false
});

const toggleBulkUploadReducer = (state = PAYABLE_LISTING_STATE) => ({
    ...state,
    openBulkUpload: !state.openBulkUpload
});

const setSelectedTabReducer = (state = PAYABLE_LISTING_STATE, action: any) => ({
    ...state,
    selectedTabIndex: action.value,
    selectedTabName: paybalesListingStatusArray[action.value],
    listData: undefined,
});

const setRequestToBeApprovedReducer = (state = PAYABLE_LISTING_STATE, action: any) => {
    const { payableId, requestsList } = state.selectedRequestsToBeApproved;
    const { element, isChecked } = action;
    if (element.isChecked === isChecked) return state;

    const updatedListData = state.listData.map((item: any) =>
        item.paymentId === element.paymentId ? { ...item, isChecked: isChecked } : item
    );

    if (isChecked) {
        return {
            ...state,
            selectedRequestsToBeApproved: {
                ...state.selectedRequestsToBeApproved,
                requestsList: [...requestsList, element],
                isAllSelected: (requestsList.length + 1) === state.listData.length
            },
            listData: updatedListData
        };
    } else {
        const filteredRequestsList = requestsList.filter((request: any) => request.paymentId !== element.paymentId);

        return {
            ...state,
            selectedRequestsToBeApproved: {
                ...state.selectedRequestsToBeApproved,
                requestsList: filteredRequestsList.length > 0 ? filteredRequestsList : [],
                payableId: filteredRequestsList.length > 0 ? payableId : null,
                isAllSelected: false
            },
            listData: updatedListData
        };
    }
}

const setAllRequestsToBeApprovedReducer = (state = PAYABLE_LISTING_STATE, action: any) => {
    const { payableId, isAllSelected } = state.selectedRequestsToBeApproved;
    const { isChecked } = action;
    if (isAllSelected === isChecked) return state;

    const updatedListData = state.listData.map((item: any) =>({ ...item, isChecked: isChecked } ));

    return {
                ...state,
                selectedRequestsToBeApproved: {
                    payableId:  isChecked ? payableId : null,
                    requestsList: isChecked ? updatedListData : [],
                    isAllSelected: isChecked
                },
                listData: updatedListData
            };
}

const resetPayableListingStateReducer = (state = PAYABLE_LISTING_STATE) => ({
    ...state,
    pageSize: 25,
    currentPage: 1,
    listData: undefined,
    selectedRequestsToBeApproved: {
        payableId: null,
        requestsList: [],
        isAllSelected: false
    }
})

const ACTION_HANDLERS = {
    [PayableListingTypes.TOGGLE_FILTER]: toggleFilterReducer,
    [PayableListingTypes.TOGGLE_MODAL]: toggleModalReducer,
    [PayableListingTypes.SELECTED_ELEMENT]: setSelectedElementReducer,
    [PayableListingTypes.SET_RESPONSE]: setResponseReducer,
    [PayableListingTypes.SET_CURRENT_PAGE]: setCurrentPageReducer,
    [PayableListingTypes.REFRESH_LIST]: refreshListReducer,
    [PayableListingTypes.SET_ROW_PER_PAGE]: setRowPerPageReducer,
    [PayableListingTypes.SHOW_LOADING]: showLoadingReducer,
    [PayableListingTypes.HIDE_LOADING]: hideLoadingReducer,
    [PayableListingTypes.TOGGLE_BULK_UPLOAD]: toggleBulkUploadReducer,
    [PayableListingTypes.SELECTED_TAB_INDEX]: setSelectedTabReducer,
    [PayableListingTypes.OPEN_PAYABLE_DETAILS]: setIsPayablesDetailsOpen,
    [PayableListingTypes.SET_REQUEST_TO_BE_APPROVED]: setRequestToBeApprovedReducer,
    [PayableListingTypes.SET_ALL_REQUESTS_TO_BE_APPROVED]: setAllRequestsToBeApprovedReducer,
    [PayableListingTypes.RESET_WHEN_MOVE_TO_ENROUTE]: resetPayableListingStateReducer,

}

export default createReducer(PAYABLE_LISTING_STATE, ACTION_HANDLERS);