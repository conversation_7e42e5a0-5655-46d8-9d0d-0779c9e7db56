import { createTypes } from 'reduxsauce';

export default createTypes<any>(`
    TOGGLE_FILTER
    TOGGLE_MODAL
    SELECTED_ELEMENT
    SET_RESPONSE
    SET_CURRENT_PAGE
    REFRESH_LIST
    SET_ROW_PER_PAGE
    SHOW_LOADING
    HIDE_LOADING
    TOGGLE_BULK_UPLOAD
    SELECTED_TAB_INDEX
    HANDLE_DOCUMENT_MODAL
    OPEN_PAYABLE_DETAILS
    SET_REQUEST_TO_BE_APPROVED
    SET_ALL_REQUESTS_TO_BE_APPROVED
    RESET_WHEN_MOVE_TO_ENROUTE
`);