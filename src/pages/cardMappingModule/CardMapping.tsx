import { AddCircle, FilterList } from "@material-ui/icons";
import React, { useEffect, useReducer, useState } from "react";
import { shallowEqual, useDispatch, useSelector } from "react-redux";
import { cardMappingArray, cardMappingEnum, headerMenuButtons, rowsPerPageOptions } from "../../base/constant/ArrayList";
import { useSearchParams } from "../../base/hooks/useSearchParams";
import { setAutoCompleteListWithoutLabelAndValue } from "../../base/moduleUtility/DataUtils";
import { cardMappingFilters } from "../../base/moduleUtility/FilterUtils";
import { isNullValue, isObjectEmpty } from "../../base/utility/StringUtils";
import { isMobile } from "../../base/utility/ViewUtils";
import Chips from "../../component/chips/Chips";
import Filter from "../../component/filter/Filter";
import PageContainer from "../../component/pageContainer/PageContainer";
import Button from "../../component/widgets/button/Button";
import CardList from "../../component/widgets/cardlist/CardList";
import TableList from "../../component/widgets/tableView/TableList";
import { OptionType } from "../../component/widgets/widgetsInterfaces";
import WarningModal from "../../modals/warningModal/WarningModal";
import { pollStart, setHeaderMenu, showAlert } from "../../redux/actions/AppActions";
import { createCardMapping, createJioBPCardMapping, deleteCardMapping, deleteJioBPCardMapping, getCardMappingListing, getVehicleNumberList, updateCardMapping, updateJioBPCardMapping } from "../../serviceActions/CardMappingServiceActions";
import { orchestrationToken } from "../../serviceActions/OpsWorkflowServiceAction";
import { JioBPCardMappingTableColumns, cardMappingTableColumns } from "../../templates/CardMappingTemplate";
import CardMappingFilters from "./cardMappingModals/CardMappingFilters";
import CreateMappingModal from "./cardMappingModals/createMappingModal/CreateMappingModal";
import { hideLoading, refreshList, setCurrentPage, setResponse, setRowPerPage, setSelectedTab, showLoading, toggleFilter } from "./cardMappingRedux/CardMappingActions";
import CardMappingReducer, { CARD_MAPPING_STATE } from "./cardMappingRedux/CardMappingReducer";
import { Tab, Tabs, makeStyles } from "@material-ui/core";
import { cardMappingRoute } from "../../base/constant/RoutePath";
import { TabPanel } from "../../component/tabs/TabPanel";
import { useHistory, useParams } from "react-router-dom";
import { useQuery } from "../../base/utility/Routerutils";

const useStyles = makeStyles({
    indicator: {
        background: "none",
    },
    tabs: {
        "& button[aria-selected='true']": {
            margin: "0 20px",
            borderBottom: "3px solid #F7931E",
        },
    },
});

function CardMapping() {
    const appDispatch = useDispatch();
    const classes = useStyles();
    const history = useHistory();
    const params = useQuery();
    const { id } = useParams<any>();
    const [state = CARD_MAPPING_STATE, dispatch] = useReducer(CardMappingReducer, CARD_MAPPING_STATE);
    const rolesList = useSelector((state: any) => state.appReducer.rolesList, shallowEqual);
    const [createMappingModal, setCreateMappingModal] = React.useState<boolean>(false);
    const [filterState, addFiltersQueryParams, removeFiltersQueryParams] = useSearchParams(cardMappingFilters);
    const [vehicleNumber, setVehicleNumber] = React.useState<string>("");
    const [cardPan, setCardPan] = React.useState<string>("");
    const [openWarningModal, setOpenWarningModal] = React.useState<boolean>(false);
    const [isEdit, setIsEdit] = React.useState<boolean>(false);
    const [vehicleNumberList, setVehicleNumberList] = React.useState<Array<OptionType> | undefined>(undefined);
    const [pollingLoader, setPollingLoader] = React.useState<boolean>(false);
    const [actionButtons, setActionButtons] = React.useState<any>({});
    const [cardNumber, setCardNumber] = useState<string>("");
    const [isJioBPSubmitBtnLoading, setIsJioBPSubmitBtnLoading] = useState<boolean>(false);
    
    appDispatch(setHeaderMenu(headerMenuButtons[3]));

    useEffect(() => {
        const getData = async () => {
            let promiseArr = [appDispatch(getVehicleNumberList())]
            Promise.all(promiseArr).then((response: any) => {
                if (response && response[0] && response[0].vehicles_list) {
                    if (response && response[0]) {
                        setVehicleNumberList(setAutoCompleteListWithoutLabelAndValue(response[0].vehicles_list))
                    } else {
                        setVehicleNumberList(undefined)
                    }
                }
            })
        }
        getData();
        // eslint-disable-next-line
    }, [])


    useEffect(() => {
        const getList = async () => {
            let queryParams: any = {
                page: state.currentPage,
                size: state.pageSize,
            };
            if (!isObjectEmpty(filterState.params)) {
                queryParams = Object.assign(queryParams, filterState.params);
            }
            if (queryParams && queryParams.queryFieldLabel) {
                delete queryParams["queryFieldLabel"];
            }
            dispatch(showLoading());
            dispatch(setSelectedTab(id ? cardMappingArray.indexOf(id) : 0))
            appDispatch(getCardMappingListing(queryParams, id)).then((response: any) => {
                if (response) {
                    dispatch(setResponse(response));
                }
                dispatch(hideLoading());
            });
        };
        getList();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [state.refreshList, state.currentPage, state.pageSize, state.selectedTabIndex, history.location.search, id]);

    useEffect(()=>{
        if (rolesList) {
            const isCardMappingEditor = rolesList.indexOf("card-mapping-editor")
            if (isCardMappingEditor >= 0) {
              setActionButtons({
                ...actionButtons,
                create: true,
                edit: true,
                delete: true
              })
            } else {
              setActionButtons({
                ...actionButtons,
                create: false,
                edit: false,
                delete: false
              })
            }
        }
    },[rolesList])

    const stopPollingLoader = () => {
        setPollingLoader(false);
    }

    const fetchPollingData = (params: any) => {
        return appDispatch(orchestrationToken({ orchestrationId: params.orchestrationId })).then((response: any) => {
            return response;
        })
    }

    const stopPollingData = () => {
        setPollingLoader(false);
        setCreateMappingModal(false);
        dispatch(refreshList());
    }


    return (
        <>
            <CardMappingFilters
                open={state.openFilter}
                filerChips={filterState.chips}
                filerParams={filterState.params}
                onApplyFilter={(filterChips: any, filterParams: any) => {
                    dispatch(refreshList());
                    dispatch(toggleFilter());
                    addFiltersQueryParams(filterChips, filterParams);
                }}
                onClose={() => {
                    dispatch(toggleFilter());
                }}
                vehicleNumberList={vehicleNumberList}
                selectedCardMappingTab={id}
            />

            <CreateMappingModal
                open={createMappingModal}
                title={isEdit ? "Update Mapping" : "Create Mapping"}
                onClose={() => {
                    setCreateMappingModal(false);
                }}
                onSuccess={(queryParams: any) => {
                    isEdit ? handleUpdateCardMapping(queryParams) : handleCreateCardMapping(queryParams);

                }}
                loading={pollingLoader || isJioBPSubmitBtnLoading}
                vehicleNumber={vehicleNumber}
                cardPan={cardPan}
                isEdit={isEdit}
                vehicleNumberList={vehicleNumberList}
                selectedCardMappingTab={id}
                cardNumber={cardNumber}
            />

            <WarningModal
                open={openWarningModal}
                onClose={() => {
                    setOpenWarningModal(false);
                }}
                warningMessage={"Are you sure want to delete?"}
                primaryButtonTitle={"Delete"}
                secondaryuttonTitle={"Cancel"}
                primaryButtonStyle={"btn-detail"}
                onSuccess={() => {
                    setOpenWarningModal(false);
                    let queryParams: any = {
                        vehicleNumber: vehicleNumber
                    };
                    if (isNullValue(id) || (id === cardMappingEnum.IOCL_MAPPING)) {
                        appDispatch(deleteCardMapping(queryParams)).then((response: any) => {
                            if (response.code === 200) {
                                appDispatch(showAlert(response.message, true));
                                dispatch(refreshList());
                            }
                        });
                    } else if (id === cardMappingEnum.JIOBP_MAPPING) {
                        appDispatch(deleteJioBPCardMapping(queryParams)).then((response: any) => {
                            if (response) {
                                appDispatch(showAlert(response.message, true));
                                dispatch(refreshList());
                            }
                        })
                    }
                }}
            />

            <div className="filter-wrap">
                {<Filter
                    pageTitle={"Card Mapping"}
                >
                    <Button
                        buttonStyle={"btn-orange"}
                        title={(isNullValue(id) || id === cardMappingEnum.IOCL_MAPPING) ? "Create IOCL Mapping" : "Create JioBP Mapping"}
                        leftIcon={<AddCircle />}
                        onClick={() => {
                            setIsEdit(false);
                            setVehicleNumber("");
                            if (isNullValue(id) || id === cardMappingEnum.IOCL_MAPPING) {
                                setCardPan("");
                            } else if (id === cardMappingEnum.JIOBP_MAPPING) {
                                setCardNumber("");
                            }
                            setCreateMappingModal(true);
                        }}
                        disable={!actionButtons?.['create']}
                    />
                    {<Button
                        buttonStyle={"btn-blue "}
                        title={isMobile ? " " : "Filter"}
                        leftIcon={<FilterList />}
                        onClick={() => {
                            dispatch(toggleFilter());
                        }}
                    />}
                </Filter>}
            </div>
            <div className="bill-tab tab-nav">
                {
                    <>
                        <div className="tab-nav--wallet">
                            <Tabs
                                value={state.selectedTabIndex}
                                className={classes.tabs}
                                classes={{ indicator: classes.indicator }}
                                onChange={(event: any, newValue: any) => {
                                    if (newValue !== state.selectedTabIndex) {
                                        dispatch(setSelectedTab(newValue));
                                        dispatch(setCurrentPage(1));
                                        dispatch(setRowPerPage(rowsPerPageOptions[0]));
                                        handleDeleteChips();
                                        history.replace({
                                            pathname: cardMappingRoute + cardMappingArray[newValue],
                                        });
                                    }
                                }}
                                variant="scrollable"
                                scrollButtons={isMobile ? "on" : "off"}
                            >
                                {cardMappingArray.map((element, index) => (
                                    <Tab key={index} label={element} />
                                ))}
                            </Tabs>
                        </div>
                    </>
                }

                <TabPanel value={state.selectedTabIndex} index={state.selectedTabIndex}>
                    {renderPageContent()}
                </TabPanel>
            </div>
        </>
    );

    function renderPageContent() {
        if (isNullValue(id) || id === cardMappingEnum.IOCL_MAPPING) {
            return (
                <PageContainer
                    loading={state.loading}
                    listData={state.listData}>
                    {!isObjectEmpty(filterState.chips) && Object.keys(filterState.chips).map((element) => (
                        <Chips
                            label={filterState.chips[element]}
                            onDelete={() => {
                                dispatch(refreshList());
                                removeFiltersQueryParams([element])
                            }}
                        />
                    ))}

                    {isMobile ? (
                        <CardList
                            listData={state.listData}
                            tableColumns={cardMappingTableColumns(onEdit, onDelete, actionButtons)}
                            isNextPage={state.pagination && state.pagination.next}
                            onReachEnd={() => {
                                dispatch(setCurrentPage(state.pagination.next));
                            }}
                        />
                    ) : (
                        <TableList
                            tableColumns={cardMappingTableColumns(onEdit, onDelete, actionButtons)}
                            currentPage={state.currentPage}
                            rowsPerPage={state.pageSize}
                            rowsPerPageOptions={rowsPerPageOptions}
                            totalCount={state.pagination && state.pagination.count}
                            listData={state.listData}
                            onChangePage={(event: any, page: number) => {
                                dispatch(setCurrentPage(page));
                            }}
                            onRowsPerPageChange={(event: any) => {
                                dispatch(setRowPerPage(event.target.value));
                            }}
                        />
                    )}
                </PageContainer>
            )
        } else if (id === cardMappingEnum.JIOBP_MAPPING) {
            return (
                <PageContainer
                    loading={state.loading}
                    listData={state.listData}>
                    {!isObjectEmpty(filterState.chips) && Object.keys(filterState.chips).map((element) => (
                        <Chips
                            label={filterState.chips[element]}
                            onDelete={() => {
                                dispatch(refreshList());
                                removeFiltersQueryParams([element])
                            }}
                        />
                    ))}

                    {isMobile ? (
                        <CardList
                            listData={state.listData}
                            tableColumns={JioBPCardMappingTableColumns(onEdit, onDelete, actionButtons)}
                            isNextPage={state.pagination && state.pagination.next}
                            onReachEnd={() => {
                                dispatch(setCurrentPage(state.pagination.next));
                            }}
                        />
                    ) : (
                        <TableList
                            tableColumns={JioBPCardMappingTableColumns(onEdit, onDelete, actionButtons)}
                            currentPage={state.currentPage}
                            rowsPerPage={state.pageSize}
                            rowsPerPageOptions={rowsPerPageOptions}
                            totalCount={state.pagination && state.pagination.count}
                            listData={state.listData}
                            onChangePage={(event: any, page: number) => {
                                dispatch(setCurrentPage(page));
                            }}
                            onRowsPerPageChange={(event: any) => {
                                dispatch(setRowPerPage(event.target.value));
                            }}
                        />
                    )}
                </PageContainer>
            )
        }
    }

    function onEdit(element: any) {
        setCreateMappingModal(true);
        setVehicleNumber(element.vehicleNumber);
        if (isNullValue(id) || (id === cardMappingEnum.IOCL_MAPPING)) {
            setCardPan(element.cardPan);
        } else if (id === cardMappingEnum.JIOBP_MAPPING) {
            setCardNumber(element.cardNo);
        }
        setIsEdit(true);
    }

    function onDelete(element: any) {
        setOpenWarningModal(true);
        setVehicleNumber(element.vehicleNumber);
        if (isNullValue(id) || (id === cardMappingEnum.IOCL_MAPPING)) {
            setCardPan(element.cardPan);
        } else if (id === cardMappingEnum.JIOBP_MAPPING) {
            setCardNumber(element.cardNo);
        }
        setIsEdit(false);
    }

    function handleCreateCardMapping(queryParams: any) {
        if (isNullValue(id) || id === cardMappingEnum.IOCL_MAPPING) {
            setPollingLoader(true);
            appDispatch(createCardMapping(queryParams)).then((response: any) => {
                if (response && response.code === 200) {
                    let orchestrationQueryParams: any = {
                        orchestrationId: response.details.orchestrationId,
                    }
                    appDispatch(pollStart({
                        params: orchestrationQueryParams,
                        asyncFetch: fetchPollingData,
                        stopPollingData: stopPollingData,
                        stopPollingLoader: stopPollingLoader,
                        requestType: "CreateMapping"
                    }));
                } else {
                    setPollingLoader(false);
                }
            })
        } else if (id === cardMappingEnum.JIOBP_MAPPING) {
            setIsJioBPSubmitBtnLoading(true);
            appDispatch(createJioBPCardMapping(queryParams)).then((response: any) => {
                if (response) {
                    setIsJioBPSubmitBtnLoading(false);
                    setCreateMappingModal(false);
                    response.message && appDispatch(showAlert(response.message, true));
                    dispatch(refreshList());
                } else {
                    setIsJioBPSubmitBtnLoading(false);
                }
            })
        }
    }

    function handleUpdateCardMapping(queryParams: any) {
        if (isNullValue(id) || id === cardMappingEnum.IOCL_MAPPING) {
            setPollingLoader(true);
            appDispatch(updateCardMapping(queryParams)).then((response: any) => {
                if (response && response.code === 200) {
                    let orchestrationQueryParams: any = {
                        orchestrationId: response.details.orchestrationId,
                    }
                    appDispatch(pollStart({
                        params: orchestrationQueryParams,
                        asyncFetch: fetchPollingData,
                        stopPollingData: stopPollingData,
                        stopPollingLoader: stopPollingLoader,
                        requestType: "UpdateMapping"
                    }));
                }else{
                    setPollingLoader(false);
                }
            })
        } else if (id === cardMappingEnum.JIOBP_MAPPING) {
            setIsJioBPSubmitBtnLoading(true);
            appDispatch(updateJioBPCardMapping(queryParams)).then((response: any) => {
                if (response) {
                    setIsJioBPSubmitBtnLoading(false);
                    setCreateMappingModal(false);
                    response.message && appDispatch(showAlert(response.message, true));
                    dispatch(refreshList());
                } else {
                    setIsJioBPSubmitBtnLoading(false);
                }
            })
        }
    }

    function handleDeleteChips() {
        !isObjectEmpty(filterState.chips) && removeFiltersQueryParams([...Object.keys(filterState.chips)]);
    }
}


export default CardMapping