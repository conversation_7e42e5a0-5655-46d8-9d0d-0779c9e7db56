import React, { useEffect } from "react";
import { cardNumberLabel, cardPanLabel, vehicleNumberLabel } from "../../../base/constant/MessageUtils";
import { isFilterNullValue, isNullValue, isObjectEmpty } from "../../../base/utility/StringUtils";
import AutoComplete from "../../../component/widgets/AutoComplete";
import EditText from "../../../component/widgets/EditText";
import { OptionType } from "../../../component/widgets/widgetsInterfaces";
import FilterContainer from "../../../modals/FilterModal/FilterContainer";
import { cardMappingEnum } from "../../../base/constant/ArrayList";

interface CardMappingFiltersProps {
    open: boolean,
    onClose: any,
    onApplyFilter: any
    filerChips: any,
    filerParams: any,
    vehicleNumberList: Array<OptionType> | undefined,
    selectedCardMappingTab: any,
}

function CardMappingFilters(props: CardMappingFiltersProps) {
    const { open, onClose, onApplyFilter, filerParams, filerChips, vehicleNumberList, selectedCardMappingTab } = props;
    const [filterValues, setFilterValues] = React.useState<any | undefined>({});
    const [filterParams, setFilterParams] = React.useState<any | undefined>({});
    const [error, setError] = React.useState<any>({});
    const [isFilterChanged, setIsFilterChanged] = React.useState<boolean>(false);

    useEffect(() => {
        if (open) {
            setFilterValues(filerChips);
            setFilterParams(filerParams);
            setIsFilterChanged(false);
        }
        // eslint-disable-next-line
    }, [open]);

    return (
        <FilterContainer
            open={open}
            onClose={() => {
                onClose();
                setError({});
            }}
            onClear={() => {
                setFilterValues({});
                setFilterParams({});
                setError({});
            }}
            onApply={onApply}
        >
            <div className="filter-form-row">
                <div className="form-group">
                    <AutoComplete
                        label={vehicleNumberLabel}
                        placeHolder={vehicleNumberLabel}
                        isClearable
                        value={filterValues.vehicleNumberLabel ? {
                            label: filterValues.vehicleNumberLabel,
                            value: filterParams.vehicleNumber
                        } : undefined}
                        options={vehicleNumberList}
                        onChange={(item: OptionType) => {
                            setValues({ vehicleNumberLabel: item?.label }, { vehicleNumber: item?.value })
                        }}
                    />
                </div>

                <div className="form-group">
                    <EditText
                        label={(isNullValue(selectedCardMappingTab) || selectedCardMappingTab === cardMappingEnum.IOCL_MAPPING) ? cardPanLabel : cardNumberLabel}
                        placeholder={(isNullValue(selectedCardMappingTab) || selectedCardMappingTab === cardMappingEnum.IOCL_MAPPING) ? cardPanLabel : cardNumberLabel}
                        value={(isNullValue(selectedCardMappingTab) || selectedCardMappingTab === cardMappingEnum.IOCL_MAPPING) ? filterValues.cardPan : filterValues.cardNo}
                        error={(isNullValue(selectedCardMappingTab) || selectedCardMappingTab === cardMappingEnum.IOCL_MAPPING) ? error.cardPan: error.cardNo}
                        maxLength={20}
                        onChange={(text: any) => {
                            const re = /^\d+$/;
                            if (text === '' || (re.test(text))) {
                                if (isNullValue(selectedCardMappingTab) || selectedCardMappingTab === cardMappingEnum.IOCL_MAPPING) {
                                    setValues({ cardPan: text }, { cardPan: text });
                                } else if (selectedCardMappingTab === cardMappingEnum.JIOBP_MAPPING) {
                                    setValues({ cardNo: text }, { cardNo: text });
                                }
                            }
                        }}
                    />
                </div>

            </div>
        </FilterContainer>
    );

    function setValues(chips: any, params?: any) {
        setFilterValues({
            ...filterValues,
            ...chips
        });
        setError({});
        params && setFilterParams({
            ...filterParams,
            ...params
        });
        setIsFilterChanged(true);
    }

    function onApply() {
        if (isNullValue(selectedCardMappingTab) || selectedCardMappingTab === cardMappingEnum.IOCL_MAPPING) {
            if (!isFilterNullValue(filterValues.cardPan) && isNullValue(filterParams.cardPan)) {
                setError({ cardPan: 'Enter valid Card Pan' });
                return;
            }
        } else if (selectedCardMappingTab === cardMappingEnum.JIOBP_MAPPING) {
            if (!isFilterNullValue(filterValues.cardNo) && isNullValue(filterParams.cardNo)) {
                setError({ cardNo: 'Enter valid Card Number' });
                return;
            }
        }

        if (!isObjectEmpty(filterParams)) {
            if (isFilterChanged) {
                setError({});
                onApplyFilter(filterValues, filterParams);
            } else {
                setError({});
                onClose();
            }
        } else {
            if (isNullValue(selectedCardMappingTab) || selectedCardMappingTab === cardMappingEnum.IOCL_MAPPING) {
                setError({ cardPan: 'Enter valid Card Pan' });
            } else if (selectedCardMappingTab === cardMappingEnum.JIOBP_MAPPING) {
                setError({ cardNo: 'Enter valid Card Number' });
            }
        }
    }
}

export default CardMappingFilters;
