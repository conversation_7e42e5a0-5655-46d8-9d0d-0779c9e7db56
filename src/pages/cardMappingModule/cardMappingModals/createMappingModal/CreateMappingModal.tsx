import { CheckCircle } from "@material-ui/icons";
import React, { useEffect } from "react";
import { cardNumberLabel, cardPanLabel, vehicleNumberLabel } from "../../../../base/constant/MessageUtils";
import { setAutoCompleteListWithoutLabelAndValue } from "../../../../base/moduleUtility/DataUtils";
import { isNullValue } from "../../../../base/utility/StringUtils";
import AutoComplete from "../../../../component/widgets/AutoComplete";
import EditText from "../../../../component/widgets/EditText";
import { OptionType } from "../../../../component/widgets/widgetsInterfaces";
import ModalContainer from "../../../../modals/ModalContainer";
import "./CreateMappingModal.scss";
import { cardMappingEnum } from "../../../../base/constant/ArrayList";

interface CreateMappingModalProps {
    open: boolean
    title: string
    onClose: any
    onSuccess: any
    loading: boolean
    vehicleNumber?: string
    cardPan?: string
    isEdit: boolean
    vehicleNumberList: Array<OptionType> | undefined
    selectedCardMappingTab: any,
    cardNumber?: string,
}

function CreateMappingModal(props: CreateMappingModalProps) {
    const { open, title, onClose, onSuccess, loading, vehicleNumber, cardPan, isEdit, vehicleNumberList, selectedCardMappingTab, cardNumber } = props;
    const [userParams, setUserParams] = React.useState<any>({});
    const [error, setError] = React.useState<any>({});

    useEffect(()=>{
        if(open && !isEdit){
            clearData();
        }
        if(isEdit){
            if (isNullValue(selectedCardMappingTab) || selectedCardMappingTab === cardMappingEnum.IOCL_MAPPING) {
                setUserParams({
                    ...userParams,
                    cardPan: cardPan,
                    vehicleNumber: vehicleNumber
                })
            } else if (selectedCardMappingTab === cardMappingEnum.JIOBP_MAPPING) {
                setUserParams({
                    ...userParams,
                    cardNumber: cardNumber,
                    vehicleNumber: vehicleNumber
                })
            }
            
        }
    },[open])

    return (
        <ModalContainer
            title={title}
            styleName={"update-mobileNumber-modal"}
            secondaryButtonTitle={"Submit"}
            secondaryButtonStyle={"btn-blue"}
            secondaryButtonDisable={!((userParams.cardPan || userParams.cardNumber) && userParams.vehicleNumber && (userParams.cardPan?.length === 16 || userParams.cardNumber?.length === 16) )}
            open={open}
            onApply={() => {
            }}
            secondaryButtonLeftIcon={<CheckCircle />}
            onClose={() => {
                if(!loading){
                    onClose();
                }
            }}
            onClear={() => {
                if (validateData()) {
                    let queryParams: any = {};
                    if (isNullValue(selectedCardMappingTab) || selectedCardMappingTab === cardMappingEnum.IOCL_MAPPING) {
                        queryParams = {
                            cardPan: userParams.cardPan,
                            vehicleNumber: !isEdit ? userParams.vehicleNumber.value : userParams.vehicleNumber,
                        }
                    } else if (selectedCardMappingTab === cardMappingEnum.JIOBP_MAPPING) {
                        queryParams = {
                            cardNo: userParams.cardNumber,
                            vehicleNumber: !isEdit ? userParams.vehicleNumber.value : userParams.vehicleNumber,
                        }
                    }
                    onSuccess(queryParams);
                    setError({});
                }
            }}
            secondaryButtonLoading={loading}
        >

            <div className="custom-form-row row driver-number">
                <div className="form-group col-12 label-down">
                    <AutoComplete
                        label={vehicleNumberLabel}
                        placeHolder={vehicleNumberLabel}
                        isClearable
                        mandatory
                        isDisabled={isEdit || loading}
                        value={isEdit ? setAutoCompleteListWithoutLabelAndValue([userParams.vehicleNumber]) : userParams.vehicleNumber}
                        options={vehicleNumberList}
                        onChange={(item: OptionType) => {
                            setError({});
                            setUserParams({
                                ...userParams,
                                vehicleNumber: item
                            })
                        }}
                    />
                </div>

                <div className="form-group col-12 label-down">
                    <EditText
                        label={(isNullValue(selectedCardMappingTab) || selectedCardMappingTab === cardMappingEnum.IOCL_MAPPING) ? cardPanLabel : cardNumberLabel}
                        placeholder={(isNullValue(selectedCardMappingTab) || selectedCardMappingTab === cardMappingEnum.IOCL_MAPPING) ? cardPanLabel : cardNumberLabel}
                        mandatory
                        disabled={loading}
                        maxLength={16}
                        error={error.cardPan}
                        value={(isNullValue(selectedCardMappingTab) || selectedCardMappingTab === cardMappingEnum.IOCL_MAPPING) ? userParams.cardPan : userParams.cardNumber}
                        onChange={(text: string) => {
                            const re = /^\d+$/;
                            if (text === '' || (re.test(text))) {
                                setError({});
                                if (isNullValue(selectedCardMappingTab) || (selectedCardMappingTab === cardMappingEnum.IOCL_MAPPING)) {
                                    setUserParams({
                                        ...userParams,
                                        cardPan: text
                                    });
                                } else if (selectedCardMappingTab === cardMappingEnum.JIOBP_MAPPING) {
                                    setUserParams({
                                        ...userParams,
                                        cardNumber: text
                                    });
                                }
                                
                            }
                        }}
                    />
                </div>
            </div>
        </ModalContainer >
    );

    function clearData() {
        setUserParams({});
        setError({});
    }

    function validateData() {
        if (isNullValue(selectedCardMappingTab) || selectedCardMappingTab === cardMappingEnum.IOCL_MAPPING) {
            if(isNullValue(userParams.cardPan)) {
                setError({
                    cardPan: "Enter Card Pan"
                })
                return false;
            }
        } else if (selectedCardMappingTab === cardMappingEnum.JIOBP_MAPPING) {
            if (isNullValue(userParams.cardNumber)) {
                setError({
                    cardPan: "Enter Card Number"
                })
                return false;
            }
        }
        return true;
    }

}

export default CreateMappingModal;
