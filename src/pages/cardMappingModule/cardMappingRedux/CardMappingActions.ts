import CardMappingTypes from "./CardMappingTypes";

export const toggleFilter = () => ({
    type: CardMappingTypes.TOGGLE_FILTER,
});

export const toggleModal = () => ({
    type: CardMappingTypes.TOGGLE_MODAL,
});

export const setSelectedElement = (value: any) => ({
    type: CardMappingTypes.SELECTED_ELEMENT,
    value,
});

export const setResponse = (response: any) => ({
    type: CardMappingTypes.SET_RESPONSE,
    response,
});

export const setCurrentPage = (value: any) => ({
    type: CardMappingTypes.SET_CURRENT_PAGE,
    value
});

export const refreshList = () => ({
    type: CardMappingTypes.REFRESH_LIST,
});

export const setRowPerPage = (value: any) => ({
    type: CardMappingTypes.SET_ROW_PER_PAGE,
    value
});

export const showLoading = () => ({
    type: CardMappingTypes.SHOW_LOADING,
});

export const hideLoading = () => ({
    type: CardMappingTypes.HIDE_LOADING,
});

export const setSelectedTab = (value: any) => ({
    type: CardMappingTypes.SELECTED_TAB_INDEX,
    value
})