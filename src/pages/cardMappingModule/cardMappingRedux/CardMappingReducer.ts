import { createReducer } from "reduxsauce";
import { cardMappingArray, rowsPerPageOptions } from "../../../base/constant/ArrayList";
import { isMobile } from "../../../base/utility/ViewUtils";
import CardMappingTypes from "./CardMappingTypes";

interface CardMappingState {
    openFilter: boolean,
    selectedItem: any,
    pagination: any,
    listData: any,
    openModal: boolean,
    currentPage: number,
    refreshList: boolean,
    loading: boolean,
    pageSize: number,
    filterParams: any,
    filterChips: any,
    selectedTabIndex: number,
    selectedTabName: any,
}

export const CARD_MAPPING_STATE: CardMappingState = {
    openFilter: false,
    selectedItem: undefined,
    pagination: undefined,
    listData: undefined,
    openModal: false,
    currentPage: 1,
    refreshList: false,
    loading: false,
    pageSize: rowsPerPageOptions[0],
    filterParams: {},
    filterChips: {},
    selectedTabIndex: 0,
    selectedTabName: cardMappingArray[0],
}

const toggleFilterReducer = (state = CARD_MAPPING_STATE) => ({
    ...state,
    openFilter: !state.openFilter
});

const toggleModalReducer = (state = CARD_MAPPING_STATE) => ({
    ...state,
    openModal: !state.openModal
});

const setSelectedElementReducer = (state = CARD_MAPPING_STATE, action: any) => ({
    ...state,
    selectedItem: action.value
});

const setResponseReducer = (state = CARD_MAPPING_STATE, action: any) => ({
    ...state,
    pagination: action.response && action.response.pagination,
    listData: isMobile ?
        (state.listData ? [...state.listData, ...action.response && action.response.data] : action.response && action.response.data)
        : action.response && action.response.data,
    loading: false,
});

const setCurrentPageReducer = (state = CARD_MAPPING_STATE, action: any) => ({
    ...state,
    currentPage: action.value
});

const refreshListReducer = (state = CARD_MAPPING_STATE) => ({
    ...state,
    refreshList: !state.refreshList,
    currentPage: 1,
    listData: undefined,
});

const setRowPerPageReducer = (state = CARD_MAPPING_STATE, action: any) => ({
    ...state,
    pageSize: action.value,
    currentPage: 1,
    listData: undefined,
});

const showLoadingReducer = (state = CARD_MAPPING_STATE) => ({
    ...state,
    loading: true
});

const hideLoadingReducer = (state = CARD_MAPPING_STATE) => ({
    ...state,
    loading: false
});

const setSelectedTabReducer = (state = CARD_MAPPING_STATE, action: any) => ({
    ...state,
    selectedTabIndex: action.value,
    selectedTabName: cardMappingArray[action.value],
    listData: undefined,
});


const ACTION_HANDLERS = {
    [CardMappingTypes.TOGGLE_FILTER]: toggleFilterReducer,
    [CardMappingTypes.TOGGLE_MODAL]: toggleModalReducer,
    [CardMappingTypes.SELECTED_ELEMENT]: setSelectedElementReducer,
    [CardMappingTypes.SET_RESPONSE]: setResponseReducer,
    [CardMappingTypes.SET_CURRENT_PAGE]: setCurrentPageReducer,
    [CardMappingTypes.REFRESH_LIST]: refreshListReducer,
    [CardMappingTypes.SET_ROW_PER_PAGE]: setRowPerPageReducer,
    [CardMappingTypes.SHOW_LOADING]: showLoadingReducer,
    [CardMappingTypes.HIDE_LOADING]: hideLoadingReducer,
    [CardMappingTypes.SELECTED_TAB_INDEX] : setSelectedTabReducer,
}

export default createReducer(CARD_MAPPING_STATE, ACTION_HANDLERS);