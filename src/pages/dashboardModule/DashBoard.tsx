import { Collapse } from '@material-ui/core';
import Card from '@material-ui/core/Card/Card';
import moment from 'moment';
import Numeral from 'numeral';
import React, { useEffect, useReducer } from 'react';
import { useDispatch } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { dashboardNumberColors, dashboardPercentageColors, headerMenuButtons, requestListingStatusArray, Wallet, wallets, walletStatusCodeArray, walletStatusListEnum } from '../../base/constant/ArrayList';
import { useSearchParams } from '../../base/hooks/useSearchParams';
import { dashboardFilters } from '../../base/moduleUtility/FilterUtils';
import { convertDateToServerFromDate, convertDateToServerToDate, getPastDate } from '../../base/utility/DateUtils';
import { isNullValue, isObjectEmpty } from '../../base/utility/StringUtils';
import { isMobile } from '../../base/utility/ViewUtils';
import DashboardCard from '../../component/dashboardCard/DashboardCard';
import DashboardContent from '../../component/dashboardCard/DashboardContent';
import Filter from '../../component/filter/Filter';
import SearchFilterBar from '../../component/filter/SearchFilterBar';
import PageContainer from '../../component/pageContainer/PageContainer';
import CardContentSkeleton from "../../component/widgets/cardContentSkeleton/CardContentSkeleton";
import { setHeaderMenu } from '../../redux/actions/AppActions';
import { getPayments } from '../../serviceActions/DashboardServiceActions';
import './Dashboard.scss';
import { DashboardGraph } from './DashboardGraph';
import { NewWalletProps } from './DashboardProps';
import { hideLoading, refreshList, showLoading } from './dashboardRedux/DashboardActions';
import DashboardReducer, { DASHBOARD_STATE } from './dashboardRedux/DashboardReducer';


function DashBoard() {
    const history = useHistory();
    const appDispatch = useDispatch();
    const [state = DASHBOARD_STATE, dispatch] = useReducer(DashboardReducer, DASHBOARD_STATE);
    const [paymentsDashboard, setPaymentsDashboard] = React.useState<any>({});
    const [walletDetails, setWalletDetails] = React.useState<Array<string>>([]);
    const [walletLabel, setWalletLabel] = React.useState<any>(Object.values(walletStatusListEnum)[0]);
    const [walletArr, setWalletArr] = React.useState<Array<NewWalletProps>>([]);
    const [walletPercentages, setWalletPercentages] = React.useState<Array<any>>([]);
    const [filterState, addFiltersQueryParams] = useSearchParams(dashboardFilters);
    const [activeCard, setActiveCard] = React.useState<any>(Object.values(walletStatusListEnum)[0]);
    const [statusLabel, setStatusLabel] = React.useState<string>(requestListingStatusArray[0]);
    const [graphWalletPercentages, setGraphWalletPercentages] = React.useState<Array<any>>([]);
    const [totalAmount, setTotalAmount] = React.useState<any>(0);
    appDispatch(setHeaderMenu(headerMenuButtons[0]));

    useEffect(() => {
        const getPaymentsList: any = async () => {
            let queryParams: any = {
                fromDateTime: convertDateToServerFromDate(getPastDate(moment(new Date()).add(1, "day"), 1, "weeks")),
                toDateTime: convertDateToServerToDate(new Date()),
            };

            if (!isObjectEmpty(filterState.chips)) {
                queryParams = Object.assign(filterState.chips);
            }

            if (queryParams.fromDate && queryParams.toDate) {
                queryParams.fromDateTime = queryParams.fromDate;
                queryParams.toDateTime = queryParams.toDate;
                delete queryParams.fromDate;
                delete queryParams.toDate;
            }

            dispatch(showLoading())
            appDispatch(getPayments(queryParams)).then((response: any) => {
                if (response) {
                    setPaymentsDashboard(response);
                }
                dispatch(hideLoading());
            })
        }
        getPaymentsList();
        // eslint-disable-next-line
    }, [state.refreshList, history.location.search])

    useEffect(() => {
        const getStatus: any = () => {
            const newArr: Array<any> = [];
            Object.values(walletStatusListEnum).forEach((element: any, index: number) => {
                if (paymentsDashboard[element]) {
                    newArr.push({ statusPercent: paymentsDashboard[element].statusPercent, statusTotal: paymentsDashboard[element].statusTotal, label: requestListingStatusArray[index], labelCode: walletStatusCodeArray[index] })
                } else {
                    newArr.push({ statusPercent: 0.00, statusTotal: 0.00, label: requestListingStatusArray[index], labelCode: walletStatusCodeArray[index] })
                }
            })
            setWalletDetails(newArr);
        }
        getStatus();
    }, [paymentsDashboard]);

    useEffect(() => {
        const getWallets: any = () => {
            const newWallet: Array<NewWalletProps> = [];
            const tempPercentages: Array<any> = [];
            wallets.forEach((wallet: Wallet) => {
                let tempWalletLabel = walletLabel;
                let tempWallet = paymentsDashboard[tempWalletLabel];
                if (tempWallet && tempWallet[wallet.name]) {
                    const tempWalletAmount = tempWallet[wallet.amount] ? tempWallet[wallet.amount] : 0.00;
                    const tempWalletPercent = tempWallet[wallet.percent] ? tempWallet[wallet.percent] : 0.00;
                    newWallet.push({ numberOfPayments: tempWallet[wallet.name], amount: tempWalletAmount, label: wallet.label, percent: tempWalletPercent });
                    tempPercentages.push(tempWalletPercent);
                } else {
                    newWallet.push({ numberOfPayments: 0.00, amount: 0.00, label: wallet.label, percent: 0.00 });
                    tempPercentages.push(0.00)
                }
            })
            setWalletArr(newWallet);
            setWalletPercentages(tempPercentages);
            setGraphWalletPercentages(tempPercentages)
        }
        getWallets();
    }, [paymentsDashboard, walletLabel]);

    useEffect(()=>{
        if(!isObjectEmpty(paymentsDashboard)){
            if(paymentsDashboard?.[activeCard] && paymentsDashboard?.[activeCard]?.statusAmount){
                const tempAmount = paymentsDashboard?.[activeCard]?.statusAmount.toFixed();
                setTotalAmount(tempAmount);
            }else{
                setTotalAmount("0");
            }
        }
    },[activeCard, paymentsDashboard])

    return (
        <div className="dashboard--wrapper">

            <Filter
                pageTitle={isMobile ? " " : "Legacy Payment Report"}
            >
                {<SearchFilterBar
                    filterParams={filterState.chips}
                    fromDate={filterState.chips && filterState.chips.fromDateTime}
                    toDate={filterState.chips && filterState.chips.toDateTime}
                    onApply={(dates: any) => {
                        dispatch(refreshList())
                        addFiltersQueryParams(filterState.params, { ...filterState.chips, ...dates })
                    }}
                />
                }

            </Filter>
            {!isMobile ? <PageContainer>
                {state.loading ? <CardContentSkeleton row={2} column={6} /> :
                    <div>
                        {isMobile && <p className="legacy-payment__title">Legacy payment Report</p>}
                        <div className='dashboard-status'>
                            {walletDetails.map((item: any, index: number) => {
                                const { label, labelCode, statusTotal, statusPercent } = item;
                                return (
                                    <DashboardCard
                                        key={index}
                                        statusName={label}
                                        numberOfPayments={statusTotal}
                                        statusPercent={statusPercent}
                                        index={index}
                                        statusCode={labelCode}
                                        activeCard={activeCard}
                                        onClick={(labelCode: string, statusName: string) => {
                                            setWalletLabel(labelCode);
                                            setActiveCard(labelCode);
                                            setStatusLabel(statusName);
                                        }}
                                        percentageColor={dashboardPercentageColors[index]}
                                    />
                                )
                            })}
                        </div>
                    </div>
                }

                {state.loading ? <CardContentSkeleton row={4} column={4} /> :
                    <Card className='legacy-report'>
                        <div className="graph-wallet">
                            {walletArr?.map((item: any, index: any) => {
                                const { numberOfPayments, amount, label } = item;
                                return (
                                    <DashboardContent
                                        key={index}
                                        walletName={label}
                                        walletAmount={amount}
                                        walletNumberOfPayments={numberOfPayments}
                                        statusLabel={statusLabel}
                                        numberColor={dashboardNumberColors[index]}
                                    />
                                )
                            })
                            }
                        </div>

                        <div className='graph-chart'>
                            <div className="total-status-amt">
                                {`Total ${statusLabel}: `}
                                <span>
                                  ₹{!isNullValue(totalAmount) ? Numeral(totalAmount).format("0,0") : "0"}
                                </span>
                            </div>
                            <DashboardGraph
                                percentage={walletPercentages}
                            />
                        </div>
                    </Card>
                }
            </PageContainer> :
                <PageContainer>
                    {state.loading ? <CardContentSkeleton row={2} column={6} /> :
                        <div>
                            {isMobile && <p className="legacy-payment__title">Legacy payment Report</p>}
                            <div className='dashboard-status'>
                                {walletDetails.map((item: any, index: number) => {
                                    const { label, labelCode, statusTotal, statusPercent } = item;     
                                    return (
                                        <DashboardCard
                                            key={index}
                                            statusName={label}
                                            numberOfPayments={statusTotal}
                                            statusPercent={statusPercent}
                                            index={index}
                                            statusCode={labelCode}
                                            activeCard={activeCard}
                                            onClick={(labelCode: string, statusName: string) => {
                                                setWalletLabel(labelCode);
                                                setActiveCard(labelCode);
                                                setStatusLabel(statusName);
                                            }}
                                            percentageColor={dashboardPercentageColors[index]}
                                        >
                                            <Collapse in={(activeCard === labelCode)} >
                                                {state.loading ? <CardContentSkeleton row={4} column={4} /> :
                                                    <Card className='legacy-report'>
                                                        <div className="graph-wallet">
                                                            {walletArr?.map((item: any, index: any) => {
                                                                const { numberOfPayments, amount, label } = item;
                                                                return (
                                                                    <DashboardContent
                                                                        key={index}
                                                                        walletName={label}
                                                                        walletAmount={amount}
                                                                        walletNumberOfPayments={numberOfPayments}
                                                                        statusLabel={statusLabel}
                                                                        numberColor={dashboardNumberColors[index]}
                                                                    />
                                                                )
                                                            })
                                                            }
                                                        </div>

                                                        <div className='graph-chart'>
                                                            <div className='total-status-amt'>
                                                                {`Total ${statusLabel}: `}
                                                                <span>
                                                                    ₹{!isNullValue(totalAmount) ? Numeral(totalAmount).format("0,0") : "0"}
                                                                </span>
                                                            </div>
                                                            <DashboardGraph
                                                                percentage={graphWalletPercentages}
                                                            />
                                                        </div>
                                                    </Card>
                                                }
                                            </Collapse>
                                        </DashboardCard>
                                    )
                                })}
                            </div>
                        </div>
                    }
                </PageContainer>}
        </div>
    );
}

export default DashBoard;
