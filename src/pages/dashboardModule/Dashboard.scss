.dashboard--wrapper {
  .legacy-payment__title {
    color: #083654;
    font-size: 14px;
    margin-bottom: 9px;
  }
  .filter-panel {
    .MuiTypography-h6 {
      @media screen and (max-width: 767px) {
        display: none;
      }
    }
  }
  .dashboard-status {
    display: flex;
    width: 100%;
    position: relative;

    @media screen and (max-width: 991px) {
      flex-wrap: wrap;
    }

    @media screen and (max-width: 767px) {
      flex-direction: column;
      margin-right: 0;
    }
  }
  .custom-date-picker {
    .MuiInput-root {
      border-radius: 20px;
      @media screen and (max-width: 767px) {
        border-radius: 4px;
      }
    }
    .MuiInputBase-input {
      margin-left: 7px;
    }
  }
  .legacy-report {
    margin: 25px 0px;
    border-radius: 8px !important;
    box-shadow: 0px 1px 3px #00000029 !important;
    display: flex;
    @media screen and (max-width: 767px) {
      flex-direction: column;
    }

    .graph-wallet {
      width: 55%;
      display: flex;
      flex-wrap: wrap;

      @media screen and (max-width: 767px) {
        width: 100%;
      }
      > div {
        border-bottom: 1px solid #eaeff3;
        width: 25%;
        text-align: center;
        align-items: center;
        display: flex;
        justify-content: center;
        border-right: 1px solid #eaeff3;

        @media screen and (max-width: 767px) {
          width: 50%;
        }
      }
    }
    .graph-chart {
      width: 45%;
      align-items: center;
      justify-content: center;
      display: flex;
      flex-direction: column;
      padding: 0 30px;
      .graph-wrap {
          margin: 10px 0;
          @media screen and (min-width:768px){
            width: 270px;
          }
          label { display: none; }
        .container-fluid {
          padding: 0;
          @media screen and (max-width: 767px) {
            width: 150px;
            height: 150px;
          }
        }
      }
      @media screen and (max-width: 767px) {
        width: 100%;
      }
    }
  }

  .number-wallet {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    padding: 60px 0;

    h5 {
      margin-bottom: 0;
      color: #006cc9;
      font-size: 26px;
      @media screen and (max-width:767px) {
        font-size: 14px;
      }
    }
    p {
      margin: 2px 0 0;
      color: #133751;
      line-height: 18px;
      font-size: 13px;
      @media screen and (max-width: 767px) {
        font-size: 11px;
        line-height: 16px;
      }
    }
    .currency-wrap {
      margin-top: 25px;
      border: 1px solid #7070702c;
      border-radius: 6px;
      width: 120px;
      @media screen and (max-width: 767px) {
        margin-top: 10px;
      }
      p {
        text-align: center;
        color: #133751ce;
        font-size: 15px;
        padding: 4px 0;
        letter-spacing: 0.3px;
        @media screen and (max-width: 767px) {
          padding: 7px 6px;
          font-weight: 500;
          font-size: 13px;
        }
      }
      span {
        margin-right: 3px;
      }
    }
    @media screen and (max-width: 767px) {
      padding: 15px 3px;
    }
  }
}
.total-status-amt{
    background: #FCFCFC;
    border: 1px solid #7070702C;
    width: 100%;
    padding: 10px 17px;
    font-size: 16px;
    text-align: center;
    border-radius: 6px;
    color: #123751;
    margin: 10px 0;
    @media screen and (max-width: 767px) {
      background: #fff;
    }
    span{
      font-size: 20px;
      color: #133751CE;
      font-weight: bold;
    }
}
