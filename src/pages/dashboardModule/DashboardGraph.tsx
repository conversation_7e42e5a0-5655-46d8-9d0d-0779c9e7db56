import React from "react";
import <PERSON><PERSON><PERSON> from "../../component/widgets/charts/PieChart";
import "./DashboardGraph.scss";

interface DashboardGraphProps {
    percentage?: any;
    responsive?: any
}

export function DashboardGraph(props: DashboardGraphProps) {
    const { percentage, responsive } = props;

    const walletLegend = [
        { title: "BPCL Wallet", BackgroundCol: "#006CC9", percentValue: percentage[0] },
        { title: "Camions Razor Pay", BackgroundCol: "#5DB3AA", percentValue: percentage[1] },
        { title: "Camions Wallet", BackgroundCol: "#55c9ca", percentValue: percentage[2] },
        { title: "Continental Petroleums", BackgroundCol: "#748E9F", percentValue: percentage[3] },
        { title: "GoBoLT Fuel Pump", BackgroundCol: "#96BA42", percentValue: percentage[4] },
        { title: "GoBoLT Wallet (MCPL)", BackgroundCol: "#083654", percentValue: percentage[5] },
        { title: "Happay Wallet", BackgroundCol: "#F7931E", percentValue: percentage[6] },
        { title: "IOCL Wallet", BackgroundCol: "#B72EAC", percentValue: percentage[7] },
        { title: "JioBP Wallet", BackgroundCol: "#555BE5", percentValue: percentage[8] },
        { title: "Hub AdBlue", BackgroundCol: "#ED1B00", percentValue: percentage[9] },
        { title: "Om Petro Mart (Diesel/BPCL)", BackgroundCol: "#1FC900", percentValue: percentage[10] },
        { title: "Om Petro Mart (Cash/Happay)", BackgroundCol: "#E3B600", percentValue: percentage[11] },
        { title: "OPS Wallet", BackgroundCol: "#d57979", percentValue: percentage[12] },
    ];
    function getLabels() {
        return [];
    }
    function getData() {
        return percentage;
    }
    function getBackgroundColorList() {
        return ["#006CC9", "#5DB3AA", "#748E9F", "#96BA42", "#55c9ca", "#083654", "#F7931E", "#B72EAC", "#555BE5", "#ED1B00", "#1FC900", "#E3B600", "#d57979"]
    }

    return (
        <>
            <div className="dashboard-graph d-flex align-items-center">
                <div className="container-fluid">
                    <div className="row dashboard-graph--items">
                        <div className="col-md-6 text-center">
                            <PieChart
                                data={{
                                    labels: getLabels(),
                                    datasets: [
                                        {
                                            data: getData(),
                                            backgroundColor: getBackgroundColorList(),
                                        },
                                    ],
                                }}
                                options={{
                                    maintainAspectRatio: true,
                                    legend: {
                                        display: false,
                                    },
                                    responsive: responsive
                                }}
                                height={300}
                            />
                        </div>
                        <div className="col-md-6 d-flex align-items-center">
                            <ul className="dashboard-graph-list">
                                {walletLegend.map((item, index) => (
                                    <li key={index} className="dashboard-graph-list--item">
                                        <span
                                            style={{ background: `${item.BackgroundCol}` }}
                                            className="dashboard-graph-list--span"
                                        ></span>
                                        {item.title}
                                        <span className="dashboard-graph-list--percent">{item.percentValue}%</span>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
