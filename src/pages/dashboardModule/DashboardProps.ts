export interface CancelledProps {
    bpcl?: number;
    bpclAmount?:number;
    bpclPercent?:number;
    happay?: number;
    happayAmount?: number;
    happayPercent?: number;
    mcpl?: number;
    mcplAmount?: number;
    mcplPercent?: number;
    omPetroMartCash?: number;
    omPetroMartCashAmount?: number;
    omPetroMartCashPercent?: number;
    omPetroMartDiesel?: number;
    omPetroMartDieselAmount?: number;
    omPetroMartDieselPercent?: number;
    statusTotal?: number;
    statusAmount?: number;
    statusPercent?: number;
}
export interface InvalidPendingProps {
    bpcl?: number;
    bpclAmount?: number;
    bpclPercent?: number;
    happay?: number;
    happayAmount?: number;
    happayPercent?: number;
    mcpl?: number;
    mcplAmount?: number;
    mcplPercent?: number;
    omPetroMartCash?: number;
    omPetroMartCashAmount?: number;
    omPetroMartCashPercent?: number;
    omPetroMartDiesel?: number;
    omPetroMartDieselAmount?: number;
    omPetroMartDieselPercent?: number;
    statusTotal?: number;
    statusAmount?: number;
    statusPercent?: number;
}
export interface InvalidReconciledProps {
    bpcl?: number;
    bpclAmount?: number;
    bpclPercent?: number;
    happay?: number;
    happayAmount?: number;
    happayPercent?: number;
    mcpl?: number;
    mcplAmount?: number;
    mcplPercent?: number;
    omPetroMartCash?: number;
    omPetroMartCashAmount?: number;
    omPetroMartCashPercent?: number;
    omPetroMartDiesel?: number;
    omPetroMartDieselAmount?: number;
    omPetroMartDieselPercent?: number;
    statusTotal?: number;
    statusAmount?: number;
    statusPercent?: number;
}
export interface PaidProps {
    bpcl?: number;
    bpclAmount?: number;
    bpclPercent?: number;
    happay?: number;
    happayAmount?: number;
    happayPercent?: number;
    mcpl?: number;
    mcplAmount?: number;
    mcplPercent?: number;
    omPetroMartCash?: number;
    omPetroMartCashAmount?: number;
    omPetroMartCashPercent?: number;
    omPetroMartDiesel?: number;
    omPetroMartDieselAmount?: number;
    omPetroMartDieselPercent?: number;
    statusTotal?: number;
    statusAmount?: number;
    statusPercent?: number;
}
export interface PendingProps {
    bpcl?: number;
    bpclAmount?: number;
    bpclPercent?: number;
    happay?: number;
    happayAmount?: number;
    happayPercent?: number;
    mcpl?: number;
    mcplAmount?: number;
    mcplPercent?: number;
    omPetroMartCash?: number;
    omPetroMartCashAmount?: number;
    omPetroMartCashPercent?: number;
    omPetroMartDiesel?: number;
    omPetroMartDieselAmount?: number;
    omPetroMartDieselPercent?: number;
    statusTotal?: number;
    statusAmount?: number;
    statusPercent?: number;
}
export interface ReconciledProps {
    bpcl?: number;
    bpclAmount?: number;
    bpclPercent?: number;
    happay?: number;
    happayAmount?: number;
    happayPercent?: number;
    mcpl?: number;
    mcplAmount?: number;
    mcplPercent?: number;
    omPetroMartCash?: number;
    omPetroMartCashAmount?: number;
    omPetroMartCashPercent?: number;
    omPetroMartDiesel?: number;
    omPetroMartDieselAmount?: number;
    omPetroMartDieselPercent?: number;
    statusTotal?: number;
    statusAmount?: number;
    statusPercent?: number;
}
export interface RejectedProps {
    bpcl?: number;
    bpclAmount?: number;
    bpclPercent?: number;
    happay?: number;
    happayAmount?: number;
    happayPercent?: number;
    mcpl?: number;
    mcplAmount?: number;
    mcplPercent?: number;
    omPetroMartCash?: number;
    omPetroMartCashAmount?: number;
    omPetroMartCashPercent?: number;
    omPetroMartDiesel?: number;
    omPetroMartDieselAmount?: number;
    omPetroMartDieselPercent?: number;
    statusTotal?: number;
    statusAmount?: number;
    statusPercent?: number;
}
export interface PaymentsDashboardProps {
    cancelled?: CancelledProps,
    invalidPending?: InvalidPendingProps,
    invalidReconciled?: InvalidReconciledProps,
    paid?: PaidProps,
    pending?: PendingProps,
    reconciled?: ReconciledProps,
    rejected?: RejectedProps
}

export interface NewWalletProps {
    numberOfPayments: number;
    amount: number;
    label: string;
    percent: number;
}