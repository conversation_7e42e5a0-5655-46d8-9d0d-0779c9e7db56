import DashboardTypes from "./DashboardTypes";

export const toggleFilter = () => ({
    type: DashboardTypes.TOGGLE_FILTER,
});

export const toggleModal = () => ({
    type: DashboardTypes.TOGGLE_MODAL,
});

export const setSelectedElement = (value: any) => ({
    type: DashboardTypes.SELECTED_ELEMENT,
    value,
});

export const setResponse = (response: any) => ({
    type: DashboardTypes.SET_RESPONSE,
    response,
});

export const setCurrentPage = (value: any) => ({
    type: DashboardTypes.SET_CURRENT_PAGE,
    value
});

export const refreshList = () => ({
    type: DashboardTypes.REFRESH_LIST,
});

export const setRowPerPage = (value: any) => ({
    type: DashboardTypes.SET_ROW_PER_PAGE,
    value
});

export const showLoading = () => ({
    type: DashboardTypes.SHOW_LOADING,
});

export const hideLoading = () => ({
    type: DashboardTypes.HIDE_LOADING,
});