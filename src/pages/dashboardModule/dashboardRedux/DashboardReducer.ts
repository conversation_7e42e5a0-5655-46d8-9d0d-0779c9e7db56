import { createReducer } from "reduxsauce";
import { rowsPerPageOptions } from "../../../base/constant/ArrayList";
import { isMobile } from "../../../base/utility/ViewUtils";

import DashboardTypes from "./DashboardTypes";

interface DashboardState {
    openFilter: boolean,
    selectedItem: any,
    pagination: any,
    listData: any,
    openModal: boolean,
    currentPage: number,
    refreshList: boolean,
    loading: boolean,
    pageSize: number,
    filterParams: any,
    filterChips: any,
}

export const DASHBOARD_STATE: DashboardState = {
    openFilter: false,
    selectedItem: undefined,
    pagination: undefined,
    listData: undefined,
    openModal: false,
    currentPage: 1,
    refreshList: false,
    loading: false,
    pageSize: rowsPerPageOptions[0],
    filterParams: {},
    filterChips: {},
}

const toggleFilterReducer = (state = DASHBOARD_STATE) => ({
    ...state,
    openFilter: !state.openFilter
});

const toggleModalReducer = (state = DASHBOARD_STATE) => ({
    ...state,
    openModal: !state.openModal
});

const setSelectedElementReducer = (state = DASHBOARD_STATE, action: any) => ({
    ...state,
    selectedItem: action.value
});

const setResponseReducer = (state = DASHBOARD_STATE, action: any) => ({
    ...state,
    listData: isMobile ?
        (state.listData ? [...state.listData, ...action.response && action.response] : action.response && action.response)
        : action.response && action.response,
    // pagination: action.response && action.response.pagination,
    loading: false,
});

const setCurrentPageReducer = (state = DASHBOARD_STATE, action: any) => ({
    ...state,
    currentPage: action.value
});

const refreshListReducer = (state = DASHBOARD_STATE) => ({
    ...state,
    refreshList: !state.refreshList,
    currentPage: 1,
    listData: undefined,
});

const setRowPerPageReducer = (state = DASHBOARD_STATE, action: any) => ({
    ...state,
    pageSize: action.value,
    currentPage: 1,
    listData: undefined,
});

const showLoadingReducer = (state = DASHBOARD_STATE) => ({
    ...state,
    loading: true
});

const hideLoadingReducer = (state = DASHBOARD_STATE) => ({
    ...state,
    loading: false
});


const ACTION_HANDLERS = {
    [DashboardTypes.TOGGLE_FILTER]: toggleFilterReducer,
    [DashboardTypes.TOGGLE_MODAL]: toggleModalReducer,
    [DashboardTypes.SELECTED_ELEMENT]: setSelectedElementReducer,
    [DashboardTypes.SET_RESPONSE]: setResponseReducer,
    [DashboardTypes.SET_CURRENT_PAGE]: setCurrentPageReducer,
    [DashboardTypes.REFRESH_LIST]: refreshListReducer,
    [DashboardTypes.SET_ROW_PER_PAGE]: setRowPerPageReducer,
    [DashboardTypes.SHOW_LOADING]: showLoadingReducer,
    [DashboardTypes.HIDE_LOADING]: hideLoadingReducer,
}

export default createReducer(DASHBOARD_STATE, ACTION_HANDLERS);