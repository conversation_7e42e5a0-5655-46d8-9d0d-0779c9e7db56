import { CheckCircle, KeyboardBackspace } from "@material-ui/icons";
import React, { useEffect, useReducer } from "react";
import { useDispatch } from "react-redux";
import { useHistory } from "react-router";
import { bulkApproveWalletList, headerMenuButtons, rowsPerPageOptions } from "../../base/constant/ArrayList";
import { isMobile } from "../../base/utility/ViewUtils";
import Filter from "../../component/filter/Filter";
import PageContainer from "../../component/pageContainer/PageContainer";
import AutoComplete from "../../component/widgets/AutoComplete";
import Button from "../../component/widgets/button/Button";
import TableList from "../../component/widgets/tableView/TableList";
import { OptionType } from "../../component/widgets/widgetsInterfaces";
import { setHeaderMenu, showAlert } from "../../redux/actions/AppActions";
import { bulkApprove, getRequestList } from "../../serviceActions/RequestServiceAction";
import { requestBulkApproveTableColumns } from "../../templates/RequestListingTemplate";
import { refreshList, setResponse } from "../requestModule/requestModuleRedux/RequestActions";
import "./BulkApprovedList.scss";
import { setCheckedListResponse, setCurrentPage, setRowPerPage, setWalletType } from "./requestModuleRedux/RequestActions";
import RequestReducer, { REQUEST_MODULE_STATE } from "./requestModuleRedux/RequestReducer";

function BulkApproveList() {
    const appDispatch = useDispatch();
    const history = useHistory();
    const [allValue, setAllValue] = React.useState<any>(false)
    const [state = REQUEST_MODULE_STATE, dispatch] = useReducer(RequestReducer, REQUEST_MODULE_STATE);
    const [loading, setLoading] = React.useState<any>(false);

    appDispatch(setHeaderMenu(headerMenuButtons[1]));

    useEffect(() => {
        const getList = async () => {
            setLoading(true);
            setAllValue(false);
            let queryParams: any = {
                page: state.currentPage,
                size: state.pageSize,
                paymentStatus: "PENDING",
                walletCode: state.walletType.value,
            }

            appDispatch(getRequestList(queryParams)).then((response: any) => {
                if (response) {
                    dispatch(setResponse(response));
                }
                setLoading(false);
            });
        }
        getList();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [state.currentPage, state.pageSize, history.location.search, state.walletType, state.refreshList]);


    return (
        <div className="bulk-approve--wrapper">
            <div className="filter-wrap">
                <Filter
                    pageTitle={"Bulk Approve"}
                >
                    <div className="filter-wrap--right">
                        {!isMobile && <AutoComplete
                            label={"Select Wallet:"}
                            placeHolder={"Select Field"}
                            value={state.walletType}
                            options={bulkApproveWalletList}
                            onChange={(value: OptionType) => {
                                dispatch(setWalletType(value));
                            }}
                        />}
                        <Button
                            buttonStyle={isMobile ? "btn-detail-mob " : "btn-detail "}
                            title={isMobile ? " " : "Back"}
                            leftIcon={<KeyboardBackspace />}
                            onClick={() => {
                                history.goBack();
                            }}
                        />
                        <Button
                            buttonStyle={"btn-blue "}
                            title={"Approve"}
                            leftIcon={<CheckCircle />}
                            disable
                            loading={loading}
                            onClick={() => {
                                setLoading(true);
                                let paymentIdList: any = [];
                                state.listData && state.listData.forEach((item: any) => {
                                    if (item.isCheckboxChecked) {
                                        const tempobj: any = {
                                            paymentId: item.paymentId
                                        }
                                        paymentIdList.push(tempobj);
                                    }
                                })
                                const params: any = {
                                    paymentRequests: paymentIdList,
                                    // approvedBy:"message"
                                }
                                appDispatch(bulkApprove(params)).then((response: any) => {
                                    if (response) {
                                        response.message && appDispatch(showAlert(response.message, "false"));
                                        dispatch(refreshList());
                                    }
                                    setLoading(false);
                                })
                            }}
                        />
                    </div>
                </Filter>
                {isMobile && <div className="bulk-mobile"><AutoComplete
                    label={"Select Wallet:"}
                    placeHolder={"Select Field"}
                    value={state.walletType}
                    options={bulkApproveWalletList}
                    onChange={(value: OptionType) => {
                        dispatch(setWalletType(value));
                    }}
                /></div>}
            </div>
            <div>
                <PageContainer
                    loading={state.loading}
                    listData={state.listData}
                >
                    <TableList
                        tableColumns={requestBulkApproveTableColumns(handleChecks, handleAllChecks, allValue)}
                        currentPage={state.currentPage}
                        rowsPerPage={state.pageSize}
                        rowsPerPageOptions={rowsPerPageOptions}
                        totalCount={state.pagination && state.pagination.count}
                        listData={state.listData}
                        onChangePage={(event: any, page: number) => {
                            dispatch(setCurrentPage(page));
                        }}
                        onRowsPerPageChange={(event: any) => {
                            dispatch(setRowPerPage(event.target.value));
                        }}
                    />
                </PageContainer>
            </div>
        </div >
    );

    function handleChecks(paymentId: any, checked: any) {
        let checkArray: any = [];
        let checkedCounts: any = 0;
        checkArray = state.listData && state.listData.map((item: any) => {
            let itemList: any = item;
            if (item.isCheckboxChecked) {
                checkedCounts++;
            }
            if (item.paymentId === paymentId) {
                itemList.isCheckboxChecked = checked;
                if (checked) {
                    checkedCounts++;
                }
            }
            return itemList;
        })
        if (checked) {
            if (checkedCounts === (state.listData && state.listData.length)) {
                setAllValue(true);
            }
        } else {
            setAllValue(false);
        }
        dispatch(setCheckedListResponse(checkArray));
    }

    function handleAllChecks(checked: any) {
        let checkArray: any = [];
        checkArray = state.listData && state.listData.map((item: any) => {
            return {
                ...item,
                isCheckboxChecked: checked
            };
        })
        dispatch(setCheckedListResponse(checkArray));
        setAllValue(checked)
    }
}

export default BulkApproveList;
