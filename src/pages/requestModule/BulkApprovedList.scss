.bulk-approve--wrapper {
  .filter-wrap {
    .MuiTypography-h6 {
      @media screen and (max-width: 767px) {
        font-size: 16px;
        font-weight: 400;
      }
    }
    .btn-blue {
      opacity: 1;
    }
    .autocomplete-wrap {
      display: flex;
      align-items: center;
      margin-right: 20px;
      label{
        margin: 0 10px ;
        color:#768A9E;
        @media screen and (max-width:767px){
          font-size: 12px;
        }
      }
      .select-container {
        width: 220px;
        .select__single-value {
          @media screen and (max-width: 767px) {
            font-size: 13px;
          }
        }
       .select__control{
        min-height: 40px;
       }
      }
    }
    .filter-wrap--right {
      display: flex;
      align-items: center;
    }

    .bulk-mobile {
      background-color: #fff;
      padding: 7px 16px;
      .autocomplete-wrap {
        justify-content: flex-end;
        margin-right: 0;
      }
    }
  }
  .table-list-view {
     .MuiTableRow-root{
        .MuiTableCell-head{
            font-size: 14px;
            padding-bottom: 0;
        }
        .MuiTableCell-root {
          @media screen and (max-width: 767px) {
            font-size: 15px;
        }
      }
     }
  }
}
