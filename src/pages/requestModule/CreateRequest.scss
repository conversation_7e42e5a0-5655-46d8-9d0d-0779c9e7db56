.wallet-ui {
  height: calc(100vh - 68px);
  overflow-y: auto;
  .bpcl-amount {
    ul {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      @media screen and (max-width: 767px) {
        justify-content: space-between;
      }
    }
    li {
      margin: 20px 0 0 20px;
      @media screen and (max-width: 767px) {
        margin: 0;
      }
    }
    p {
      font-size: 18px;
      color: #768a9e;
      margin-bottom: 0;
      margin-right: 25px;
      font-weight: 500;
      @media screen and (max-width: 767px) {
        font-size: 14px;
        margin-right: 0;
      }
      span {
        color: #083654;
        font-size: 20px;
        font-weight: 500;
        @media screen and (max-width: 767px) {
          display: block;
        }
      }
    }
  }
  .trip-text {
    .trip-status {
      color: #f7931e;
    }
  }
  .shipment-img {
    position: absolute;
    width: 97%;
    z-index: 1;
    bottom: 0;
    @media screen and (max-width: 767px) {
      width: auto;
      img {
        width: 100%;
      }
    }
  }

  .trip-check {
    margin-left: 3px;
    @media screen and (max-width: 767px) {
      margin-left: 0;
      align-items: center;
    }
    .MuiFormControlLabel-root {
      @media screen and (max-width: 767px) {
        margin: 0;
      }
    }
    .MuiRadio-colorSecondary.Mui-checked {
      color: #f7931e;
    }
    svg {
      width: 28px;
      height: 28px;
    }
  }
  .trip-checked {
    border: 1px solid #dfe4e6;
    margin: 6px 4px;
    padding: 10px;
    border-radius: 5px;
    @media screen and (max-width: 767px) {
      margin: 6px 0;
      padding: 0;
    }

    .btn-outline {
      border: 1px solid #12548d;
      border-radius: 5px;
      margin-left: 12px;
    }
    .btn {
      color: #0663b3;
      font-size: 12px;
      padding: 7px 12px;
      width: 68px;
      height: 22px;
      margin-top: -8px;
      @media screen and (max-width: 767px) {
        margin-top: 0;
        width: 46px;
        font-size: 10px;
        height: 20px;
      }
    }
    .form-group {
      margin-bottom: 0;
    }
    .checked-status {
      width: 4%;
      @media screen and (max-width: 767px) {
        width: 100%;
        background-color: #eaeff3af;
      }
      .payment-info {
        .flex-grow-1 {
          display: flex;
        }
        .info-heading,
        .vehicle-info {
          font-size: 14px;
          color: #083654;
          margin-bottom: 0;
        }

        .vehicle-info {
          margin-left: 5px;
          font-weight: 500;
        }
      }
    }
    .vehicle-trip {
      width: 96%;
      @media screen and (max-width: 767px) {
        width: 100%;
        padding: 10px 10px 0;
      }
    }
  }
  .request-not-found {
    .data-not-found {
      max-width: initial;
      padding: 50px 0;
      .img-fluid {
        height: auto;
      }

      .content {
        max-width: initial;
        h4 {
          margin-top: 10px;
        }
        p {
          margin-bottom: 0;
        }
      }
    }
  }
}
.bpcl-vehicle {
  .card-wrapper {
    &.MuiCard-root {
      .MuiCardContent-root {
        width: 100%;
        padding: 0 16px;
        &:last-child {
          padding: 8px 15px 15px;
        }
      }
    }
  }
}
.trip-checked.trip-checked-active {
  border: 1px solid #f7931e;
}

.tick-icon {
  position: absolute;
  top: 33px;
  right: 65px;
}
.bpcl-wallet {
  .MuiFormControl-root {
    width: 100%;
  }
  &--tooltip {
    text-align: left;
    button {
      height: 15px;
      border: 1px solid #f78d1e83;
      border-radius: 3px;
      background-color: #f78c1e17;
      width: 50px;
      margin-left: 8px;
      span {
        color: #f7921e;
        font-size: 8px;
      }
    }
  }

  &--heading {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e9eff4;
    margin: 6px 20px;
    padding-top: 5px;
    padding-bottom: 5px;
    &-right {
      display: flex;
      align-items: center;
      padding: 10px 0;
      p {
        margin-bottom: 0;
        color: #083654;
        font-size: 16px;
        margin-left: 5px;
      }
      span {
        color: #6f8193;
        font-size: 14px;
        font-weight: 300;
      }
      button {
        height: 30px;
        border: 1px solid #12548d;
        border-radius: 3px;
        margin-left: 5px;
        span {
          color: #12548d;
          font-size: 13px;
          font-weight: 500;
        }
        &:hover {
          background-image: linear-gradient(to left, #006cc9, #145288, #006cc9);
          span {
            color: white;
          }
        }
      }
    }
  }
  h6.bpcl-wallet--heading-left {
    border-bottom: none;
  }
}

#payment-not-found{
  height: 100px;
  margin-bottom: 5px;
}
.hide-label span {
  visibility: hidden;
}

// .payout-wrap
.payout-wrap{
   .select__menu{
    padding: 4px;
    .select__option.select__option--is-focused{
      background: rgba(249, 239, 230, 0.7);
    }
    .select__option.select__option--is-selected{
      background: #F9EFE6;
      border: 1px solid #F3933D;
      color: inherit;
    }
  }
}

// payout-contact-wrap
.payout-contact-wrap{
  .payout-contact-list{
    display: flex;
      gap: 0px 30px;
      li{
        font-size: 14px;
        color: #333333;
        max-width: 170px;
        flex: 1;
        &:first-child{font-weight: 500;}
        svg{
          font-size: 16px;
          color: #F3933D;
          margin-right: 2px;
        }
        .MuiChip-root{
          background: rgba(243, 147, 61, 0.1);
          border-radius: 13px;
          font-size: 13px;
          letter-spacing: 0px;
          color: #F3933D;
          font-weight: 500;
          height: 24px;
        }
      }
  }
  .select-container{
    .select__value-container--has-value{
      min-height: 44px;
    }
  }
}

// payout-payment-wrap 
.payout-payment-wrap{
  .btn{
      width: 100%;
      background: #1A6CC919;
      border-radius: 3px;
      margin-bottom: 4px;
      letter-spacing: 0px;
      color: #0F6FDB;
  }
  // .select-container .select__value-container{padding: 0;}
  .select-container .select__single-value{margin: 0 -7px;}
  .MuiList-padding,.MuiListItem-gutters {
    padding: 0;
  }
  .MuiListItemAvatar-root {
      min-width: 50px;
      .MuiAvatar-root{
        color: #EB882F;
        background: #F5F5F5;
        border-radius: 3px;
        .MuiSvgIcon-root{font-size: 20px;}
      }
  }
  .MuiListItemText-primary{
    font-size: 14px;
    letter-spacing: 0px;
    color: #333333C7;
    span{font-weight: bold;}
  }
  .select__menu{
    padding: 4px;
   .select__option{
      padding: 4px;
   }
  }
  .select__option.select__option--is-selected{
    .MuiListItemAvatar-root .MuiAvatar-root{
      background: #fff;
    }
  }
}


@media(max-width:767px){
  .payout-contact-wrap{
    .payout-contact-list{
        display: grid;
        gap: 0;
        grid-template-columns: 1fr auto;
        li{
            align-items: center;
            color: #333;
            display: grid !important;
            font-size: 14px;
            grid-auto-flow: column;
            grid-template-columns: auto 1fr;
            width: 100%;
          font-size: 12px;
          &:nth-child(even){
            text-align: right;
          }
           .MuiChip-root {
              font-size: 11px;
              height: 18px;
          }
        }
       
    }
      .select-container{
        .select__value-container--has-value{
          padding: 2px 4px;
        }
      }
  }
}
