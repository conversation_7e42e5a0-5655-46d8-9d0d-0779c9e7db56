import { CircularProgress,Card, CardContent, FormControl, FormControlLabel, Radio, RadioGroup, Chip, List, ListItem, ListItemAvatar, Avatar, ListItemText, Box } from "@material-ui/core";
import { AccountBalance, AccountBalanceWalletRounded, AddCircle, CheckCircle, EmailOutlined, KeyboardBackspace, LocalPhoneOutlined, PublishOutlined } from "@material-ui/icons";
import Numeral from 'numeral';
import React, { useEffect, useReducer, useCallback } from "react";
import { useDispatch } from "react-redux";
import { walletList, walletListEnum } from "../../base/constant/ArrayList";
import { MCPL_TOTAL_REQUEST_LIMIT, ADBLUE_TOTAL_REQUEST_LIMIT, FUEL_QUANTITY_LIMIT, OM_PETRO_MART_CASH_LIMIT, OM_PETRO_MART_DIESEL_LIMIT, FUEL_RATE_LIMIT, ADBLUE_QUANTITY_LIMIT, ADBLUE_RATE_LIMIT, CONTINENTAL_PETROLEUMS_TOTAL_REQUEST_LIMIT, GOBOLT_CASH_LIMIT } from "../../base/constant/limits";
import { accidentSettlemantAmountLabel, adblueAmountLabel, adBlueLimitMessage, adblueQuantityLabel, adblueRatePerLitreLabel, advanceAmountLabel, challanAmountLabel, continentalPetroleumsMessage, customerNameLabel, destinationLabel, driverMobileNumberLabel, driverNameLabel, foodingAmountLabel, fuelAmountLabel, fuelQuantityLabel, fuelRatePerLitreLabel, gbCodeLabel, goboltCashLimitMessage, loadingAmountLabel, maintainceAmountLabel, mcplLimitMessage, omPetroMartCashLimitMessage, omPetroMartDieselLimitMessage, orderCodeLabel, originLabel, remarksLabel,tollAmountLabel, tripCodeLabel, tripStatusLabel, unloadingAmountLabel, utrNoLabel } from "../../base/constant/MessageUtils";
import { requestListingRoute } from "../../base/constant/RoutePath";
import { setAutoCompleteListWithoutLabelAndValue, setAutoCompleteListWithData } from "../../base/moduleUtility/DataUtils";
import { isNullValue, isNullValueOrZero, isObjectEmpty, convertToTitleCase } from "../../base/utility/StringUtils";
import { isMobile } from "../../base/utility/ViewUtils";
import DataNotFound from "../../component/error/DataNotFound";
import Filter from "../../component/filter/Filter";
import Information from "../../component/information/Information";
import PageContainer from "../../component/pageContainer/PageContainer";
import AutoComplete from "../../component/widgets/AutoComplete";
import Button from "../../component/widgets/button/Button";
import CardContentSkeleton from "../../component/widgets/cardContentSkeleton/CardContentSkeleton";
import EditText from "../../component/widgets/EditText";
import NumberEditText from "../../component/widgets/NumberEditText";
import TableList from "../../component/widgets/tableView/TableList";
import { InfoTooltip } from "../../component/widgets/tooltip/InfoTooltip";
import { OptionType } from "../../component/widgets/widgetsInterfaces";
import { showAlert, showUploadAlert } from "../../redux/actions/AppActions";
import { createRequest, getBpclMobileNumber, getPreviousPaymentsRequest, getVehicleListFromWallet, getVehicleTrips, getCurrentWalletBalance, getIoclMobileNumber, getStateList, getStateFuelRate, getJioBPCardNumber, getJioBPMobileNumber, getVehicleDetails, getPayoutContacts, getPayoutFundAccountsForContactId, createRazorPayRequest  } from "../../serviceActions/RequestServiceAction";
import getPastEntryColumns from "../../templates/RequestAmountTemplate";
import "./CreateRequest.scss";
import UpdateMobileNumberModal from "./requestModals/UpdateMobileNumberModal";
import RequestReducer, { REQUEST_MODULE_STATE } from "./requestModuleRedux/RequestReducer";
import { checkBPCLVariance, createRequestParams, fetchVehicleDetails, getIOCLBalance, showAmountField, showBalanceField, isValidUPIId, extractUPIId, isValidUPILink } from "./requestModuleUtility/RequestUtility";
import DuplicateEntryModal from "../../modals/DuplicateEntryModal/DuplicateEntryModal";
import VehicleDetailsCard from "./components/VehicleDetailsCard";
import { OverflowTip } from "../../component/widgets/tooltip/OverFlowToolTip";
import { useHistory } from "react-router-dom";
import { components } from "react-select";
import { PayoutAddContactModal } from "./PayoutAddContactModal";
import PayoutAddFundAccountModal from "./PayoutAddFundAccountModal";
import QrScanner from 'qr-scanner';

export type PayoutContactType = {
    id: string;
    entity: 'contact';
    name: string;
    type?: 'Vendor' | 'Customer' | 'employees' | 'self';
    contact: string;
    email: string;
    active?: boolean;
    createdAt?: string;
};

export type PayoutFundAccountType = {
  id: string;
  entity: string;
  contactId: string;
  accountType: 'vpa' | 'bank_account';
  vpa?: Vpa;
  active: boolean;
  createdAt: string;
  bankAccount?: BankAccount;
};

export type BankAccount = {
  name: string;
  ifsc: string;
  accountNumber: string;
  bankName: string;
};

export type Vpa = {
  username: string;
  handle: string;
  address: string;
};

export type PayoutContactData = {
  id?: string;
  name: string;
  type?: 'Vendor' | 'Customer' | 'employees' | 'self';
  contact: string;
  email: string;
}

export type PayoutFundAccountData = {
    accountType?: 'vpa' | 'bank_account';
    accountTypeTabIndex: number;
    beneficiaryName: string;
    ifsc: string;
    accountNumber: string;
    confirmAccountNumber: string;
    vpa: string;
} & PayoutContactData;

export type PayoutAddContactFormData = PayoutFundAccountData;

const MenuListComponent = components.MenuList as any;

function CreateRequest() {
  const history = useHistory()
  const appDispatch = useDispatch();
  // eslint-disable-next-line
  const [state = REQUEST_MODULE_STATE, dispatch] = useReducer(RequestReducer, REQUEST_MODULE_STATE);
  const [userParams, setUserParams] = React.useState<any>({});
  const [loading, setLoading] = React.useState<boolean>(false);
  const [createLoading, setCreateLoading] = React.useState<boolean>(false);
  const [vehicleSelected, setVehicleSelected] = React.useState<boolean>(false);
  const [error, setError] = React.useState<any>({});
  const [vehicleList, setVehicleList] = React.useState<any>([]);
  const [vehicleTrips, setVehicleTrips] = React.useState<any>([]);
  const [selectTrip, setSelectTrip] = React.useState<any>({});
  const [walletAmounts, setWalletAmounts] = React.useState<any>({});
  const [totalRequestedAmount, setTotalRequestedAmount] = React.useState<any>(0)
  const [fuelDetails, setFuelDetails] = React.useState<any>({});
  const [adBlueDetails, setAdBlueDetails] = React.useState<any>({});
  const [updateMobileNumber, setUpdateMobileNumber] = React.useState<boolean>(false);
  const [remarks, setRemarks] = React.useState<string>("");
  const [mobileNumberLoader, setMobileNumberLoader] = React.useState<boolean>(false);
  const [mobileNumberDetails, setMobileNumberDetails] = React.useState<any>({});
  const [previousPayments, setPreviousPayments] = React.useState<any>({});
  const [paymentsLoading, setPaymentsLoading] = React.useState<any>([]);
  const [newMobileNumber, setNewMobileNumber] = React.useState<any>();
  const [stateList, setStateList] = React.useState<any>([]);
  const [statesLoading, setStatesLoading] = React.useState<any>([]);
  const [stateFuelRate, setStateFuelRate] = React.useState<any>();
  const [currentWalletBalance, setCurrentWalletBalance] = React.useState<string>("");
  const [walletbalanceLoading, setWalletbalanceLoading] = React.useState<boolean>(false);
  const [selectedState, setSelectedState] = React.useState<any>("");
  const [duplicateEntryModal, setDuplicateEntryModal] = React.useState<any>({open: false, message: ""});
  const [confirmMessage, setConfirmMessage] = React.useState<string>("");
  const [utrNumber, setUtrNumber] = React.useState<string>("");
  const [vehicleDetails, setVehicleDetails] = React.useState<any>({});
  const [payoutContacts, setPayoutContacts] = React.useState<OptionType[]>([]);
  const [isPayoutContactsLoading, setIsPayoutContactsLoading] = React.useState<boolean>(false);
  const [payoutFundAccountsForContact, setPayoutFundAccountsForContact] = React.useState<OptionType[]>([]);
  const [isPayoutFundAccountsForContactLoading, setIsPayoutFundAccountsForContactLoading] = React.useState<boolean>(false);
  const [upiIdFromQrCode, setUpiIdFromQrCode] = React.useState<string | null>(null);
  const [showUPIScannerLoader, setShowUPIScannerLoader] = React.useState<boolean>(false);

  const { id, name, contact, email, type } = userParams?.payoutContact?.data || {};

  const handleUploadQRCode = async (file: File | null) => {
    if (!file) return;

    setShowUPIScannerLoader(true);
    try {
      const scanResult = await QrScanner.scanImage(file, { returnDetailedScanResult: true });
      const upilink = scanResult?.data || '';

      if (isValidUPILink(upilink)) { 
        const upiId = extractUPIId(upilink);
        if (upiId && isValidUPIId(upiId)) {
          setUpiIdFromQrCode(upiId);
        } else {
          appDispatch(showAlert("Invalid UPI ID found in the QR code. Please scan a valid QR code."));
        }
      } else {
        appDispatch(showAlert("Invalid UPI Link. Please scan a valid QR code."));
      }
      setShowUPIScannerLoader(false);
    } catch (error) {
      console.error('Error scanning QR code:', error);
      appDispatch(showAlert("No QR code found"));
      setShowUPIScannerLoader(false);
    }
  }

  const fetchRazorPayContacts = useCallback(async () => {
    setIsPayoutContactsLoading(true);
    appDispatch(getPayoutContacts()).then((response: any) => {
      if (response?.code === 200) {
        setPayoutContacts(setAutoCompleteListWithData(response?.details || [], 'name', 'id'));
      }
      setIsPayoutContactsLoading(false);
    })
  }, [appDispatch])

  const fetchRazorPayFundAccounts = useCallback(async (contactId: string) => {
    if (!isNullValue(contactId)) {
      setIsPayoutFundAccountsForContactLoading(true);
      appDispatch(getPayoutFundAccountsForContactId({ 'contact_id': contactId })).then((response: any) => {
        if (response?.code === 200) {
          const data = response?.details || [];
          const formattedData = data.map((element: any) => ({
            label: element?.accountType === 'vpa'
              ? element?.vpa?.address
              : element?.accountType === 'bank_account'
                ? element?.bankAccount?.accountNumber : undefined,
            value: element?.id,
            data: element,
          }));

          setPayoutFundAccountsForContact(formattedData);
        }
        setIsPayoutFundAccountsForContactLoading(false);
      })
    }
  }, [appDispatch])

  const searchRazorPayContactsByName = useCallback(async (searchTerm: string) => {
    try {
      const response = await appDispatch(getPayoutContacts({ 'name': searchTerm }));
      if (response?.code === 200) {
        return setAutoCompleteListWithData(response?.details || [], 'name', 'id');
      } else {
        throw new Error(`API request failed: ${response?.message}`);
      }
    } catch (error) {
      console.error(error);
      return [];
    }
  }, [appDispatch])

  useEffect(() => {
    setLoading(true);
    const getVehicleList = async () => {
      let queryParams: any = {
        wallet: userParams.wallet.value
      }
      appDispatch(getVehicleListFromWallet(queryParams)).then((response: any) => {
        if (response && response.length > 0) {
          if (response) {
            setVehicleList(setAutoCompleteListWithoutLabelAndValue(response));
            setInitialValues()
          } else {
            setVehicleList([]);
          }
          setLoading(false);
        }
      })
    }
    userParams.wallet && getVehicleList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userParams.wallet])

  useEffect(() => {
    userParams?.wallet?.value === walletListEnum.CAMIONS_RAZOR_PAY && fetchRazorPayContacts();
  }, [userParams?.wallet?.value, fetchRazorPayContacts])

  useEffect(() => {
    fetchRazorPayFundAccounts(userParams?.payoutContact?.value);
  }, [fetchRazorPayFundAccounts, userParams?.payoutContact?.value])

  useEffect(() => {
    setLoading(true);
    const getAllVehicleTrips = async () => {
      let queryParams: any = {
        wallet: userParams.wallet.value,
        vehicleNo: userParams.vehicleNumber.value
      }
      appDispatch(getVehicleTrips(queryParams)).then((response: any) => {
        setVehicleTrips([]);
        if (response) {
          if ((response && response.length > 0)) {
            setVehicleTrips(response);
            if (response.length === 1) {
              setSelectTrip(response[0]?.orderCode);
            }
            setVehicleSelected(true)
          } else {
            setVehicleSelected(false)
            setVehicleList(undefined);
          }
        }
        setLoading(false);
      })
      fetchVehicleDetails(appDispatch, userParams.vehicleNumber.value, setVehicleDetails)
    }
    userParams.vehicleNumber && getAllVehicleTrips()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userParams.vehicleNumber])

  useEffect(() => {
    let totalValue = 0;
    const keysArr = Object.values(walletAmounts);
    // eslint-disable-next-line
    keysArr.map((item: any) => {
      totalValue += Number(item)
    })
    setTotalRequestedAmount(totalValue)
  }, [walletAmounts])

  useEffect(() => {
    if (fuelDetails.fuelQuantity && fuelDetails.fuelRate) {
      let tempAmount = Number(fuelDetails.fuelQuantity) ? Number(fuelDetails.fuelQuantity) * Number(fuelDetails.fuelRate) : 0
      setWalletAmounts({
        ...walletAmounts,
        fuelAmount: Number(tempAmount)
      })
    } else {
      setWalletAmounts({
        ...walletAmounts,
        fuelAmount: 0
      })
    }
    // eslint-disable-next-line
  }, [fuelDetails.fuelQuantity, fuelDetails.fuelRate])

  useEffect(() => {
    if (adBlueDetails.adblueQuantity && adBlueDetails.adblueRate) {
      let tempAmount = Number(adBlueDetails.adblueQuantity) ? Number(adBlueDetails.adblueQuantity) * Number(adBlueDetails.adblueRate) : 0
      setWalletAmounts({
        ...walletAmounts,
        adblueAmount: Number(tempAmount)
      })
    } else {
      setWalletAmounts({
        ...walletAmounts,
        adblueAmount: 0
      })
    }
    // eslint-disable-next-line
  }, [adBlueDetails.adblueQuantity, adBlueDetails.adblueRate])

  useEffect(() => {
    setPaymentsLoading(true);
    const getPreviousPayments = async (selectedTripOrderCode: any) => {
      setPreviousPayments({});
      let selectedTrip: any = {};
      if(!isObjectEmpty(vehicleTrips)){
        selectedTrip = vehicleTrips.find((item: any) => selectedTripOrderCode === item.orderCode);
      }
      let queryParams: any = {
        orderCode: selectedTrip?.orderCode,
        tripCode: selectedTrip?.tripCode,
        vehicleNumber: selectedTrip?.vehicleNo,
      }
      appDispatch(getPreviousPaymentsRequest(queryParams)).then((resp: any) => {
        if (resp) {
          setPreviousPayments(resp);
        }
        setPaymentsLoading(false);
      })
    }
    userParams?.vehicleNumber && selectTrip &&  !isObjectEmpty(vehicleTrips) && getPreviousPayments(selectTrip);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectTrip])

  useEffect(()=>{
    function getNewBPCLMobileNumber(){
      let selectedTripData: any = {};
      selectedTripData = vehicleTrips.find((item: any) => selectTrip === item.orderCode);
      setNewMobileNumber(selectedTripData['driverMobileNumber']);  
    }
    userParams?.vehicleNumber && selectTrip &&  !isObjectEmpty(vehicleTrips) && getNewBPCLMobileNumber();
  },[selectTrip])
  useEffect(() => {
    const getWalletBalance = async () => {
      setWalletbalanceLoading(true);
      let queryParams: any = {
        walletName: userParams.wallet.value,
        vehicleNo: userParams.vehicleNumber.value,
      }
      if(userParams?.wallet?.value===walletListEnum.IOCL || userParams?.wallet?.value===walletListEnum.JIOBP){
        queryParams['cardNumber'] = mobileNumberDetails?.cardPan;
      }
      if(userParams?.wallet?.value===walletListEnum.JIOBP){
        queryParams['mobileNumber'] = mobileNumberDetails?.mobileNumber;
      }
      appDispatch(getCurrentWalletBalance(queryParams)).then((response: any) => {
        if (response) {
          if(response?.walletBalance){
            setCurrentWalletBalance(response?.walletBalance);
          }else{
            setCurrentWalletBalance('NA');
          }
        }else{
          setCurrentWalletBalance('NA');
        }
        setWalletbalanceLoading(false);
      })
    }
    showBalanceField(userParams?.wallet?.value) && userParams.vehicleNumber && getIOCLBalance(userParams?.wallet?.value, mobileNumberDetails?.cardPan, mobileNumberDetails?.mobileNumber) && getWalletBalance()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userParams.vehicleNumber, mobileNumberDetails.cardPan])

  useEffect(() => {
    setStatesLoading(false);
    if (userParams?.wallet?.value === walletListEnum.BPCL || userParams?.wallet?.value === walletListEnum.IOCL || userParams?.wallet?.value === walletListEnum.JIOBP) {
      appDispatch(getStateList()).then((resp: any) => {
        let objectList:any = [];
        if (resp) {
          resp.map((item: any) => {
            objectList.push({
              "label": item.stateName,
              "value": item.stateCode
          });
         })
         setStateList(objectList);
        } else {
          setStateList([]);
        }
      });
    }
    setStatesLoading(true);
  }, [userParams.wallet])

  function setInitialValues(key?: boolean) {
    !key && setUserParams({
      ...userParams,
      vehicleNumber: undefined,
      vehicleNumberLabel: undefined
    })
    setWalletAmounts({})
    setTotalRequestedAmount(0)
    setError({})
    setSelectTrip(undefined)
    setVehicleSelected(false)
    setAdBlueDetails({})
    setFuelDetails({})
    setRemarks("")
    setMobileNumberDetails({
    ...mobileNumberDetails,
    mobileNumber: undefined,
    newMobileNumber: undefined,
    cardPan: undefined
    })
  }

  const handlePayoutAddContactSuccess = useCallback((step: 'CONTACT_ADDED' | 'FUND_ACCOUNT_ADDED') => {
    if (step === 'CONTACT_ADDED') {
      // refetch all Payout contacts
      fetchRazorPayContacts();
    } else if (step === 'FUND_ACCOUNT_ADDED') {
      setUserParams({
        ...userParams,
        openAddContactModal: false
      });
      appDispatch(showAlert("New Contact Added Successfully.", true));
    }
  }, [appDispatch, fetchRazorPayContacts, userParams])

  const handleAddPayoutFundAccountSuccess = useCallback(() => {
    setUserParams({
      ...userParams,
      isOpenAddFundAccountModal: false
    });

    appDispatch(showAlert("Fund Account Added Successfully.", true));
    // refetch all Fund Accounts for selected contact
    fetchRazorPayFundAccounts(userParams?.payoutContact?.value);
  }, [appDispatch,userParams, fetchRazorPayFundAccounts])

  return (
      <div className="wallet-ui">
        <DuplicateEntryModal
          open={duplicateEntryModal.open}
          confirmMessage={confirmMessage}
          setConfirmMessage={setConfirmMessage}
          onClose={() => {
            setDuplicateEntryModal({open: false, message: ""});
          }}
          loading={createLoading}
          onApply={() => {
            setCreateLoading(true);
            const createRequestPromise = userParams?.wallet?.value === walletListEnum.CAMIONS_RAZOR_PAY
              ? createRazorPayRequest
              : createRequest;
            const params: any = createRequestParams(userParams, vehicleTrips, selectTrip, walletAmounts, fuelDetails, adBlueDetails, totalRequestedAmount, mobileNumberDetails, remarks, stateFuelRate, utrNumber, confirmMessage);
            appDispatch(createRequestPromise(params)).then((response: any) => {
              if (response) {     
                if(response.code === 6){
                  setCreateLoading(false);
                  return;
                }      
                response.message && appDispatch(showUploadAlert(response.message, response.details?.walletCode, response.details?.paymentRequestId, response.details?.paymentId, true));
                history.push(requestListingRoute);
              }
              setDuplicateEntryModal({open: false, message: ""});
              setCreateLoading(false);
            })
          }}
          alertMessage={duplicateEntryModal.message}
          buttonTitle={"Create Request"}
        />
        <UpdateMobileNumberModal
          open={updateMobileNumber}
          title={"Mobile Number Update"}
          onClose={() => {
            setUpdateMobileNumber(false);
          }}
          newMobileNumber={newMobileNumber}
          mobileNumberDetails={mobileNumberDetails}
          setMobileNumberDetails={setMobileNumberDetails}
          setUpdateMobileNumber={setUpdateMobileNumber}
        />

        {userParams.openAddContactModal && (
          <PayoutAddContactModal
            isOpen={userParams.openAddContactModal}
            onClose={() => setUserParams({ ...userParams, openAddContactModal: false })}
            onSaveSuccess={handlePayoutAddContactSuccess}
          />
        )}

        {userParams?.isOpenAddFundAccountModal && (
          <PayoutAddFundAccountModal
            isOpen={userParams.isOpenAddFundAccountModal}
            contactData={{ id, name, email, contact, type }}
            onsaveSuccess={handleAddPayoutFundAccountSuccess}
            onClose={() => setUserParams({ ...userParams, isOpenAddFundAccountModal: false })}
          />
        )}

        <Filter
          pageTitle={"Create Request"}
          buttonTitle={isMobile ? " " : "Back"}
          leftIcon={<KeyboardBackspace />}
          buttonStyle={isMobile ? "btn-detail-mob" : "btn-detail btn-rounded"}
          onClick={() => {
            history.goBack()
          }}
        >
        </Filter>

        <PageContainer>
          <Card className="card-wrapper">
            <CardContent>
              <div className="custom-form-row row align-items-end">
                <div className="form-group col-md-6">
                  <AutoComplete
                    label={"Select Wallet"}
                    mandatory
                    placeHolder={"Select Wallet"}
                    value={userParams.wallet}
                    error={error.wallet}
                    options={walletList}
                    onChange={(element: OptionType) => {
                      setUserParams({
                        ...userParams,
                        wallet: element,
                        walletTypeName: element.label,
                        walletTypeValue: element.value,
                        payoutContact: undefined,
                        payoutMethod: undefined
                        
                      })
                      setInitialValues(true)
                      setError({});
                    }}
                  />
                </div>
                <div className="form-group col-md-6">
                  <AutoComplete
                    label={"Vehicle Number"}
                    mandatory
                    placeHolder={"Select Vehicle Number"}
                    value={userParams.vehicleNumber}
                    error={error.vehicleNumber}
                    options={vehicleList}
                    onChange={(element: OptionType) => {
                      setUserParams({
                        ...userParams,
                        vehicleNumber: element,
                        vehicleNumberLabel: element.label
                      })
                      vehicleTrips?.length > 0 && setSelectTrip(undefined);
                      if (userParams.wallet.value === walletListEnum.BPCL) {
                        setMobileNumberLoader(true);
                        appDispatch(getBpclMobileNumber({ vehicleNo: element.value })).then((response: any) => {
                          if (response?.code === "200") {
                            setMobileNumberLoader(true);
                            setMobileNumberDetails({
                              ...mobileNumberDetails,
                              mobileNumber: response.details.phoneNo
                            });
                          } else if (isNullValue(response)) {
                            setInitialValues();
                          }
                          setMobileNumberLoader(false);
                        })
                      }
                      if (userParams.wallet.value === walletListEnum.IOCL) {
                        setMobileNumberLoader(true);
                        appDispatch(getIoclMobileNumber({ vehicleNumber: element.value })).then((response: any) => {
                          if (response?.code === 200) {
                            if (response?.details) {
                              setMobileNumberDetails({
                                ...mobileNumberDetails,
                                mobileNumber: response.details.mobileNumber,
                                cardPan: response.details.cardPan
                              });
                            } else {
                              setMobileNumberDetails({
                                ...mobileNumberDetails,
                                mobileNumber: undefined,
                                cardPan: undefined
                              });
                            }
                          } else if (isNullValue(response)) {
                            setInitialValues();
                          }
                          setMobileNumberLoader(false);
                        })
                      }
                      if (userParams.wallet.value === walletListEnum.JIOBP) {
                        setMobileNumberLoader(true);
                        appDispatch(getJioBPCardNumber({ vehicleNumber: element.value })).then((jioCardNumberResponse: any) => {
                          if (jioCardNumberResponse?.code === 200 && !isNullValue(jioCardNumberResponse?.details?.data)) {
                            appDispatch(getJioBPMobileNumber({ vehicleNumber: element.value, cardNumber: jioCardNumberResponse?.details?.data?.[0]?.cardNo })).then((jioMobileNumberResponse: any) => {
                              if(jioMobileNumberResponse?.code === "200"){
                                if (jioMobileNumberResponse?.details) {
                                  setMobileNumberDetails({
                                    ...mobileNumberDetails,
                                    cardPan: jioCardNumberResponse?.details?.data?.[0]?.cardNo,
                                    mobileNumber: jioMobileNumberResponse?.details?.mobileNumber
                                  });
                                  setMobileNumberLoader(false);
                                } else {
                                  setMobileNumberDetails({
                                    ...mobileNumberDetails,
                                    mobileNumber: undefined,
                                    cardPan: jioCardNumberResponse?.details?.data?.[0]?.cardNo,
                                  });
                                  setMobileNumberLoader(false);
                                }
                              }else{
                                setInitialValues();
                                setMobileNumberLoader(false);
                              }
                            })
                          } else {
                            setMobileNumberLoader(false);
                            setInitialValues();
                            appDispatch(showAlert("Card Not Found"));
                          }
                        })
                      }
                    }}
                  />
                  {vehicleSelected && <div className="tick-icon">
                    <img src="/images/Tick-icon.svg" alt="verified-icon"></img>
                  </div>}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* payout */}
          {(userParams?.wallet?.value === walletListEnum.CAMIONS_RAZOR_PAY && userParams?.vehicleNumber) && (
            <div className="bpcl-wallet bpcl-vehicle">
              <Card className="card-wrapper">
                <div className="bpcl-wallet--heading">
                  <h6 className="bpcl-wallet--heading-left">Payout</h6>
                  <Button
                    title="New Contact"
                    leftIcon={<AddCircle />}
                    buttonStyle="btn-blue btn-square"
                    onClick={() => setUserParams({ ...userParams, openAddContactModal: true })}
                  />
                </div>
                <CardContent>
                  <div className="custom-form-row row align-items-end">
                    <div className="form-group col-md-6">
                      <AutoComplete
                        className="payout-wrap payout-contact-wrap"
                        label="Contact"
                        mandatory
                        placeHolder="Select Contact"
                        value={userParams.payoutContact}
                        options={payoutContacts}
                        isLoading={isPayoutContactsLoading}
                        showCustomView={true}
                        error={error.payoutContact}
                        onAsyncSearch={searchRazorPayContactsByName}
                        renderValueHolder={(data: any) => {
                          return (
                            <>
                              <ul className="align-items-center payout-contact-list">
                                <li className="d-flex align-items-center">
                                  <OverflowTip
                                    text={data.data.data.name}
                                    elementStyle={{
                                      maxWidth: "130px",
                                      minWidth: "130px"
                                    }}
                                  />
                                </li>
                                <li>
                                  {data.data.data.contact && (
                                    <span><LocalPhoneOutlined />{data.data.data.contact}</span>
                                  )}
                                </li>
                                <li className="d-flex align-items-center">
                                  {data.data.data.email && (
                                    <EmailOutlined />
                                  )}
                                  <OverflowTip
                                    text={data.data.data.email}
                                    elementStyle={{ maxWidth: "150px", minWidth: "150px" }}
                                  />
                                </li>
                                {data.data.data.type && (
                                  <li>
                                    <Chip label={convertToTitleCase(data.data.data.type)} />
                                  </li>
                                )}
                              </ul>
                            </>
                          )
                        }}
                        renderOption={(data: any) => {
                          return (
                            <>
                              <ul className="align-items-center gap-0 column-gap-3 payout-contact-list">
                                <li className="d-flex align-items-center">
                                  <OverflowTip
                                    text={data.data.name}
                                    elementStyle={{
                                      maxWidth: "140px",
                                      minWidth: "140px"
                                    }}
                                  />
                                </li>
                                <li>
                                  {data.data.contact && (
                                    <span><LocalPhoneOutlined />{data.data.contact}</span>
                                  )}
                                </li>
                                <li className="d-flex align-items-center">
                                  {data.data.email && (
                                    <EmailOutlined />
                                  )}
                                  <OverflowTip
                                    text={data.data.email}
                                    elementStyle={{
                                      maxWidth: "170px",
                                      minWidth: "170px"
                                    }}
                                  />
                                </li>
                                {data.data.type && (
                                  <li>
                                    <Chip label={convertToTitleCase(data.data.type)} />
                                  </li>
                                )}
                              </ul>
                            </>
                          )
                        }}
                        onChange={(element: OptionType) => {
                          setError({ ...error, payoutContact: undefined });
                          setPayoutFundAccountsForContact([]);
                          setUserParams({
                            ...userParams,
                            payoutContact: element,
                            payoutMethod: undefined,
                          })
                        }}
                      />
                    </div>
                    <div className="form-group col-md-6">
                      <AutoComplete
                        label={"Payment Method"}
                        className="payout-wrap payout-payment-wrap"
                        mandatory
                        placeHolder={"Select Payment Method"}
                        value={userParams.payoutMethod}
                        error={error.payoutMethod}
                        options={payoutFundAccountsForContact}
                        isLoading={isPayoutFundAccountsForContactLoading}
                        isDisabled={userParams?.payoutContact ? false : true}
                        showCustomView={true}
                        inputValue={upiIdFromQrCode || ''}
                        showCustomNotFoundMessage={upiIdFromQrCode ? 'UPI Not Found' : undefined}
                        renderValueHolder={(data: any) => {
                          const accountData = data?.data?.data;
                          if (!accountData) return null;

                          const isUPI = accountData.accountType === 'vpa';

                          return (
                            <List>
                              <ListItem>
                                <ListItemAvatar>
                                  <Avatar>
                                    <AccountBalance />
                                  </Avatar>
                                </ListItemAvatar>
                                <ListItemText primary={
                                  <>
                                    {isUPI ? (
                                      <>
                                        <span>UPI:</span> {accountData?.vpa?.address || 'N/A'}
                                      </>
                                    ) : (
                                        <>
                                          <span>{accountData?.bankAccount?.bankName || 'Bank'}:</span> {accountData?.bankAccount?.accountNumber || 'N/A'}
                                        </>
                                      )}
                                  </>
                                } />
                              </ListItem>
                            </List>
                          );
                        }}
                        renderOption={(data: any) => {
                          const accountData = data?.data;
                          if (!accountData) return null;

                          const isUPI = accountData.accountType === 'vpa';

                          return (
                            <List>
                              <ListItem>
                                <ListItemAvatar>
                                  <Avatar>
                                    <AccountBalance />
                                  </Avatar>
                                </ListItemAvatar>
                                <ListItemText
                                  primary={
                                    <>
                                      {isUPI ? (
                                        <>
                                          <span>UPI:</span> {accountData?.vpa?.address || 'N/A'}
                                        </>
                                      ) : (
                                          <>
                                            <span>{accountData?.bankAccount?.bankName || 'Bank'}:</span> {accountData?.bankAccount?.accountNumber || 'N/A'}
                                          </>
                                        )}
                                    </>
                                  }
                                />
                              </ListItem>
                            </List>
                          )
                        }}
                        customMenuList={(props) => {
                          const { children, resetSelect, ...rest } = props;
                          return (
                            <MenuListComponent {...rest}>
                              <Button
                                title="Fund Account"
                                leftIcon={<AddCircle />}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  resetSelect();
                                  setUserParams({
                                    ...userParams,
                                    isOpenAddFundAccountModal: true
                                  });
                                }}
                              />

                              {/* Original menu options */}
                              {children}
                            </MenuListComponent>
                          );
                        }}
                        onChange={(element: OptionType) => {
                          setError({ ...error, payoutMethod: undefined });
                          setUserParams({
                            ...userParams,
                            payoutMethod: element,
                          })
                        }}
                        onClearInputValue={() => {
                          setUpiIdFromQrCode(null);
                        }}
                      />
                      {userParams?.payoutContact && (
                        <Box
                          className="upload-btn uploadQR"
                          component={"div"}
                        >
                          <Box className="upload-inner" component={"div"}>
                            {showUPIScannerLoader && <CircularProgress size={16} />}
                            <PublishOutlined className="upload-icon" />
                            <span className="title">QR Upload</span>
                            <input
                              type="file"
                              className="upload-input"
                              accept="image/*"
                              disabled={!userParams.payoutContact || loading || isPayoutFundAccountsForContactLoading || createLoading}
                              onChange={(e) => {
                                const file = e.target.files?.[0];
                                if (file) {
                                  handleUploadQRCode(file);
                                  e.target.value = ''; // Clear the input value to allow re-uploading the same file
                                }
                              }}
                            />
                          </Box>
                        </Box>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
          {/* payout end*/}

          {(userParams.wallet && userParams.vehicleNumber) ?
            <>
              {(loading) ? (
                <CardContentSkeleton
                  row={3}
                  column={3}
                />) :
                <>
                  {(vehicleTrips?.length > 0) ?
                    <>
                      {userParams?.vehicleNumberLabel &&
                        <div className="bpcl-wallet bpcl-vehicle">
                          <Card className="card-wrapper">
                            <div className="bpcl-wallet--heading flex-md-nowrap flex-wrap">
                              <h6 className="bpcl-wallet--heading-left">{`Vehicle Number : ${userParams?.vehicleNumberLabel}`}</h6>
                              {(userParams.wallet.value === walletListEnum.BPCL ||
                              userParams.wallet.value === walletListEnum.IOCL || userParams.wallet.value === walletListEnum.JIOBP) &&
                                <div className="bpcl-wallet--heading-right">
                                  <span>{`${userParams.wallet.value} Mobile Number:`}</span>
                                  {mobileNumberLoader ? <CircularProgress size={24}/> :
                                  <p>{`${mobileNumberDetails.mobileNumber}`}</p>}

                                  {!isObjectEmpty(mobileNumberDetails.newMobileNumber) &&
                                    <InfoTooltip
                                      title={"New Mobile Number"}
                                      placement={"top"}
                                      className="bpcl-wallet--tooltip"
                                      children={
                                        <div style={{ display: "flex", flexDirection: "row", marginTop: "4px" }}>
                                          <h6>{mobileNumberDetails.newMobileNumber}</h6>
                                          <Button
                                            buttonStyle="btn-detail"
                                            title={"PENDING"}
                                          />
                                        </div>
                                      }
                                    />
                                  }
                                  <Button
                                    buttonStyle="btn"
                                    title={"Update"}
                                    onClick={() => {
                                      setUpdateMobileNumber(true)
                                    }}
                                  />
                                </div>
                              }</div>
                            <FormControl>
                              <RadioGroup
                                row
                                aria-labelledby="demo-row-radio-buttons-group-label"
                                name="row-radio-buttons-group"
                                defaultValue={selectTrip}
                                value={selectTrip}
                                onChange={(e: any) => {
                                  setSelectTrip(e.target.value)

                                }}
                              >
                                {vehicleTrips.map((element: any, index: any) => {
                                  return (
                                    <CardContent key={index}>
                                      <div className={`custom-form-row row trip-checked ${selectTrip === element.orderCode ? 'trip-checked-active' : ''}`}>
                                        <div className="checked-status">
                                          <div className="row trip-check">
                                            <FormControlLabel
                                              value={element.orderCode}
                                              control={<Radio />}
                                              label=""
                                            />
                                            {isMobile &&
                                              <div>
                                                <Information
                                                  title={tripCodeLabel}
                                                  text={element.tripCode}
                                                />
                                              </div>
                                            }
                                          </div>
                                        </div>
                                        <div className="vehicle-trip">
                                          <div className="row trip-text">
                                            {!isMobile &&
                                              <div className="col-md-3 card-group col-6">
                                                <Information
                                                  title={tripCodeLabel}
                                                  text={element.tripCode}
                                                />
                                              </div>
                                            }
                                            <div className="col-md-3 card-group col-6">
                                              <Information
                                                title={orderCodeLabel}
                                                text={element.orderCode}
                                              />
                                            </div>
                                            <div className="col-md-3 card-group col-6">
                                              <Information
                                                title={originLabel}
                                                text={element.originName}
                                              />
                                            </div>
                                            <div className="col-md-3 card-group col-6">
                                              <Information
                                                title={destinationLabel}
                                                text={element.destinationName}
                                              />
                                            </div>
                                            <div className="col-md-3 card-group col-6">
                                              <Information
                                                title={driverNameLabel}
                                                text={element.driverName}
                                              />
                                            </div>
                                            <div className="col-md-3 card-group col-6">
                                              <Information
                                                title={gbCodeLabel}
                                                text={element.driverCode}
                                              />
                                            </div>
                                            <div className="col-md-3 card-group col-6">
                                              <Information
                                                title={driverMobileNumberLabel}
                                                text={element.driverMobileNumber}
                                              // customLabel={
                                              //   userParams?.wallet?.value === walletListEnum.BPCL && <div>
                                              //     <Button
                                              //       buttonStyle={"btn-outline"}
                                              //       title={"Update"}
                                              //       onClick={() => {
                                              //         // setUpdateMobileNumber(true)
                                              //         dispatch(setSelectedElement(element))
                                              //       }}
                                              //     />
                                              //   </div>
                                              // }
                                              />
                                            </div>
                                            <div className="col-md-3 card-group col-6">
                                              <Information
                                                title={customerNameLabel}
                                                text={element.customerCode}
                                              />
                                            </div>
                                            <div className="col-md-3 card-group col-6">
                                              <Information
                                                title={tripStatusLabel}
                                                text={element.tripStatus}
                                                valueClassName={"orange-text"}
                                              />
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </CardContent>
                                  )
                                })
                                }
                              </RadioGroup>
                            </FormControl>
                          </Card>
                        </div>
                      }
                    </>
                    :
                    <Card className="card-wrapper request-not-found">
                      <div className="bpcl-wallet--heading">
                        <h6>{`Vehicle Number : ${userParams?.vehicleNumberLabel}`}</h6>
                      </div>
                      <CardContent>
                        <DataNotFound
                          image={"/images/info_icon.svg"}
                          customWarning={"Trip Not Available"}
                          message={"There is no running trip associated with the vehicle"}
                        />
                      </CardContent>
                    </Card>
                  }
                  {loading ? (
                    <CardContentSkeleton
                      row={3}
                      column={3}
                    />
                  ) : <VehicleDetailsCard vehicleDetails={vehicleDetails} />}
                  {
                    (vehicleTrips?.length > 0)  && userParams?.vehicleNumber && !isObjectEmpty(selectTrip) ? (
                      <>
                        {
                          <>
                            {
                              (paymentsLoading) ? (
                                <CardContentSkeleton
                                  row={3}
                                  column={3}
                                />
                              ) : (
                                <>
                                  {
                                    !isObjectEmpty(previousPayments?.paidReconciledDetails) ? (
                                      <div className="bpcl-wallet bpcl-vehicle">
                                        <Card className="card-wrapper">
                                          <div className="bpcl-wallet--heading">
                                            <h6 className="bpcl-wallet--heading-left">{`Previous Payment Requests`}</h6>
                                          </div>
                                          <CardContent>
                                            <div className="table-detail-listing inp-tableList scroll-table">
                                              <TableList
                                                tableColumns={getPastEntryColumns()}
                                                currentPage={0}
                                                rowsPerPage={25}
                                                rowsPerPageOptions={[]}
                                                listData={previousPayments?.paidReconciledDetails}
                                                onChangePage={() => { }}
                                                onRowsPerPageChange={() => { }}
                                              />
                                            </div>
                                          </CardContent>
                                        </Card>
                                      </div>
                                    ):(
                                      <Card className="card-wrapper">
                                        <div className="bpcl-wallet--heading">
                                          <h6 className="bpcl-wallet--heading-left">{`Previous Payment Requests`}</h6>
                                        </div>
                                        <CardContent>
                                          <DataNotFound
                                            imageId={"payment-not-found"}
                                            image={"/images/info_icon.svg"}
                                            customWarning={"No Data Available"}
                                            message={"No previous payment records corresponding to the vehicle"}
                                          />
                                        </CardContent>
                                      </Card>
                                    )
                                  }
                                </>
                              )
                            }
                          </>
                        }
                        {
                          <>
                            {
                              (paymentsLoading) ? (
                                <CardContentSkeleton
                                  row={3}
                                  column={3}
                                />
                              ) : (
                                <>
                                  {
                                    !isObjectEmpty(previousPayments?.invalidPendingDetails) && (
                                      <div className="bpcl-wallet bpcl-vehicle">
                                        <Card className="card-wrapper">
                                          <div className="bpcl-wallet--heading">
                                            <h6 className="bpcl-wallet--heading-left">{`Invalid Pending Payment Requests`}</h6>                                    
                                              <div className="d-flex align-item-center legacy-heading upload-payment">
                                                <div>
                                                  <span className="legacy-name">Total Amount</span>
                                                  <span className="legacy-price">
                                                    ₹ {!isNullValue(previousPayments?.totalInvalidPendingAmount) ? Numeral(previousPayments.totalInvalidPendingAmount).format("0,0.00") : "N/A"}
                                                  </span>
                                                </div>
                                              </div>
                                          </div>
                                          <CardContent>
                                            <div className="table-detail-listing inp-tableList scroll-table">
                                              <TableList
                                                tableColumns={getPastEntryColumns()}
                                                currentPage={0}
                                                rowsPerPage={25}
                                                rowsPerPageOptions={[]}
                                                listData={previousPayments?.invalidPendingDetails}
                                                onChangePage={() => { }}
                                                onRowsPerPageChange={() => { }}
                                              />
                                            </div>
                                          </CardContent>
                                        </Card>
                                      </div>
                                    )
                                  }
                                </>
                              )
                            }
                          </>
                        }
                      </>
                    ) : (
                      <></>
                    )
                  }

                  {(!isObjectEmpty(walletAmounts) || userParams.vehicleNumber) &&
                    <>
                      <div className="bpcl-wallet">
                        <Card className="card-wrapper">
                          <div className="bpcl-wallet--heading">
                            <h6>{userParams?.walletTypeName}</h6>
                            <div className="d-flex align-item-center">
                              {
                                showBalanceField(userParams?.walletTypeValue) && (
                                  <div className="d-flex align-item-center legacy-heading upload-payment">
                                    <div className="legacy-currency">
                                      <span><AccountBalanceWalletRounded /></span>
                                    </div>
                                    <div>
                                      <span className="legacy-name">Current Wallet Balance </span>
                                        {walletbalanceLoading ? <span className="legacy-price">₹ <img src={'/images/Loading.svg'} /> </span> : <span className="legacy-price">₹ {currentWalletBalance !== "" ? currentWalletBalance : "N/A" }</span>}
                                    </div>
                                  </div>
                                )  
                              }
                            </div>
                          </div>
                          <CardContent>
                            <div className="custom-form-row row align-items-end">
                              {showAmountField(userParams?.walletTypeValue, "tollAmount") && <div className="form-group col-md-3">
                                <NumberEditText
                                  label={tollAmountLabel}
                                  placeholder={tollAmountLabel}
                                  value={walletAmounts.tollAmount}
                                  decimalScale={2}
                                  maxLength={10}
                                  onChange={(text: any) => {
                                    setWalletAmounts({
                                      ...walletAmounts,
                                      tollAmount: text,
                                    })
                                  }}
                                />
                              </div>}
                              {showAmountField(userParams?.walletTypeValue, "challanAmount") && <div className="form-group col-md-3">
                                <NumberEditText
                                  label={challanAmountLabel}
                                  placeholder={challanAmountLabel}
                                  value={walletAmounts.challanAmount}
                                  decimalScale={2}
                                  maxLength={10}
                                  onChange={(text: any) => {
                                    setWalletAmounts({
                                      ...walletAmounts,
                                      challanAmount: text
                                    })
                                  }}
                                />
                              </div>}
                              {showAmountField(userParams?.walletTypeValue, "loadingAmount") && <div className="form-group col-md-3">
                                <NumberEditText
                                  label={loadingAmountLabel}
                                  placeholder={loadingAmountLabel}
                                  value={walletAmounts.loadingAmount}
                                  decimalScale={2}
                                  maxLength={10}
                                  onChange={(text: any) => {
                                    setWalletAmounts({
                                      ...walletAmounts,
                                      loadingAmount: text
                                    })
                                  }}
                                />
                              </div>}
                              {showAmountField(userParams?.walletTypeValue, "unloadingAmount") && <div className="form-group col-md-3">
                                <NumberEditText
                                  label={unloadingAmountLabel}
                                  placeholder={unloadingAmountLabel}
                                  value={walletAmounts.unloadingAmount}
                                  decimalScale={2}
                                  maxLength={10}
                                  onChange={(text: any) => {
                                    setWalletAmounts({
                                      ...walletAmounts,
                                      unloadingAmount: text
                                    })
                                  }}
                                />
                              </div>}
                              {showAmountField(userParams?.walletTypeValue, "foodingAmount") && <div className="form-group col-md-3">
                                <NumberEditText
                                  label={foodingAmountLabel}
                                  placeholder={foodingAmountLabel}
                                  value={walletAmounts.foodingAmount}
                                  decimalScale={2}
                                  maxLength={10}
                                  onChange={(text: any) => {
                                    setWalletAmounts({
                                      ...walletAmounts,
                                      foodingAmount: text
                                    })
                                  }}
                                />
                              </div>}
                              {showAmountField(userParams?.walletTypeValue, "dallaAmount") && <div className="form-group col-md-3">
                                <NumberEditText
                                  label={advanceAmountLabel}
                                  placeholder={advanceAmountLabel}
                                  value={walletAmounts.dallaAmount}
                                  decimalScale={2}
                                  maxLength={10}
                                  onChange={(text: any) => {
                                    setWalletAmounts({
                                      ...walletAmounts,
                                      dallaAmount: text
                                    })
                                  }}
                                />
                              </div>}
                              {showAmountField(userParams?.walletTypeValue, "maintainceAmount") && <div className="form-group col-md-3">
                                <NumberEditText
                                  disabled={true}
                                  label={maintainceAmountLabel}
                                  placeholder={maintainceAmountLabel}
                                  value={walletAmounts.maintainceAmount}
                                  decimalScale={2}
                                  maxLength={10}
                                  onChange={(text: any) => {
                                    setWalletAmounts({
                                      ...walletAmounts,
                                      maintainceAmount: text
                                    })
                                  }}
                                />
                              </div>}
                              {showAmountField(userParams?.walletTypeValue, "accidentSettlemantAmount") && <div className="form-group col-md-3">
                                <NumberEditText
                                  label={accidentSettlemantAmountLabel}
                                  placeholder={accidentSettlemantAmountLabel}
                                  value={walletAmounts.accidentSettlemantAmount}
                                  decimalScale={2}
                                  maxLength={10}
                                  onChange={(text: any) => {
                                    setWalletAmounts({
                                      ...walletAmounts,
                                      accidentSettlemantAmount: text
                                    })
                                  }}
                                />
                              </div>}
                              {showAmountField(userParams?.walletTypeValue, "fuelAmount") && <div className="form-group col-md-3">
                                <NumberEditText
                                  label={fuelAmountLabel}
                                  placeholder={fuelAmountLabel}
                                  value={walletAmounts.fuelAmount}
                                  decimalScale={2}
                                  maxLength={7}
                                  disabled
                                  onChange={() => {
                                  }}
                                />
                              </div>}
                              {showAmountField(userParams?.walletTypeValue, "fuelQuantity") && <div className="form-group col-md-3">
                                <NumberEditText
                                  label={fuelQuantityLabel}
                                  placeholder={fuelQuantityLabel}
                                  value={fuelDetails.fuelQuantity}
                                  mandatory={((fuelDetails.fuelRate || (userParams?.walletTypeValue === walletListEnum.BPCL && selectedState)) ? true : false)}
                                  error={error.fuelQuantity}
                                  decimalScale={2}
                                  maxLength={6}
                                  onChange={(text: any) => {
                                    setFuelDetails({
                                      ...fuelDetails,
                                      fuelQuantity: text
                                    })
                                    setError({})
                                  }}
                                />
                              </div>}
                              {( userParams?.walletTypeValue !== walletListEnum.BPCL && userParams?.walletTypeValue !== walletListEnum.JIOBP) && showAmountField(userParams?.walletTypeValue, "fuelRate") && <div className="form-group col-md-3">
                                <NumberEditText
                                  label={fuelRatePerLitreLabel}
                                  placeholder={fuelRatePerLitreLabel}
                                  value={fuelDetails.fuelRate && Numeral(fuelDetails.fuelRate).format("0,0.00")}
                                  error={error.fuelRate}
                                  mandatory={fuelDetails.fuelQuantity ? true : false}
                                  decimalScale={2}
                                  maxLength={6}
                                  onChange={(text: any) => {
                                    if (text>=1000) {
                                      setError({
                                        fuelRate: "Value cannot exceed 999.99"
                                      })
                                    }
                                    else {
                                      setFuelDetails({
                                        ...fuelDetails,
                                        fuelRate: text
                                      })
                                      setError({})
                                    }
                                  }}
                                />
                              </div>}
                              {showAmountField(userParams?.walletTypeValue, "stateList") && <div className="form-group col-md-3">
                                <div className="input-wrap">
                                  <AutoComplete
                                    label={"State for Fuel"}
                                    isClearable={true}
                                    value={selectedState}
                                    placeHolder={"Select State"}
                                    error={error.stateList}
                                    options={stateList}
                                    mandatory={(fuelDetails.fuelQuantity || fuelDetails.fuelRate) ? true : false}
                                    onChange={(element: OptionType) => {
                                      setSelectedState(element);
                                      if(element!=null){
                                        setUserParams({
                                          ...userParams,
                                          stateCode: element.label
                                        })
                                        let queryParams: any = {
                                            stateCode: element.value,
                                            walletCode: userParams?.wallet?.value
                                        }
                                        appDispatch(getStateFuelRate(queryParams)).then((response: any) => {
                                            if (response && response.suggestiveFuelRate) {
                                              setStateFuelRate(response.suggestiveFuelRate);
                                              setStateFuelRate({
                                                ...stateFuelRate,
                                                stateCode: element.label,
                                                stateRate: response.suggestiveFuelRate
                                              })
                                            } else {
                                              setStateFuelRate(null);
                                            }
                                        })
                                        setError({});
                                      }else{
                                        setStateFuelRate({});
                                        setError({});
                                      }
                                    }}
                                  />
                                </div>
                              </div>}
                              {showAmountField(userParams?.walletTypeValue, "stateFuelRate") && <div className="form-group col-md-3">
                                <div className="row">
                                  <div className="col">
                                    <NumberEditText
                                      label={fuelRatePerLitreLabel}
                                      placeholder={"Suggestive Rates"}
                                      mandatory={(fuelDetails.fuelQuantity || stateFuelRate?.stateRate || selectedState) ? true : false}
                                      onChange={()=>{}}
                                      value={stateFuelRate?.stateRate && Numeral(stateFuelRate?.stateRate).format("0,0.00")}
                                      decimalScale={2}
                                      maxLength={7}
                                      disabled
                                  />
                                 </div>
                                 {showAmountField(userParams?.walletTypeValue, "fuelRate") && <div className="col hide-label">
                                  <NumberEditText
                                    label={fuelRatePerLitreLabel}
                                    placeholder={( userParams?.walletTypeValue === walletListEnum.BPCL || userParams?.walletTypeValue === walletListEnum.JIOBP ) ? "Actual Rate" : fuelRatePerLitreLabel}
                                    value={fuelDetails.fuelRate && Numeral(fuelDetails.fuelRate).format("0,0.00")}
                                    error={error.fuelRate}
                                    decimalScale={2}
                                    maxLength={6}
                                    onChange={(text: any) => {
                                      if (text>=1000) {
                                        setError({
                                          fuelRate : "Value cannot exceed 999.99"
                                        })
                                      }
                                      else {
                                        setFuelDetails({
                                          ...fuelDetails,
                                          fuelRate: text
                                        })
                                        setError({})
                                      }
                                    }}
                                  />
                                  </div>}
                                </div>
                              </div>}
                              {showAmountField(userParams?.walletTypeValue, "adblueAmount") && <div className="form-group col-md-3">
                                <NumberEditText
                                  label={adblueAmountLabel}
                                  placeholder={adblueAmountLabel}
                                  value={walletAmounts.adblueAmount}
                                  decimalScale={2}
                                  maxLength={7}
                                  disabled
                                  onChange={() => {
                                  }}
                                />
                              </div>}
                              {showAmountField(userParams?.walletTypeValue, "adblueQuantity") && <div className="form-group col-md-3">
                                <NumberEditText
                                  label={adblueQuantityLabel}
                                  placeholder={adblueQuantityLabel}
                                  value={adBlueDetails.adblueQuantity}
                                  mandatory={adBlueDetails.adblueRate ? true : false}
                                  error={error.adblueQuantity}
                                  decimalScale={2}
                                  maxLength={6}
                                  onChange={(text: any) => {
                                    setAdBlueDetails({
                                      ...adBlueDetails,
                                      adblueQuantity: text
                                    })
                                    setError({})
                                  }}
                                />
                              </div>}
                              {showAmountField(userParams?.walletTypeValue, "adblueRate") && <div className="form-group col-md-3">
                                <NumberEditText
                                  label={adblueRatePerLitreLabel}
                                  placeholder={adblueRatePerLitreLabel}
                                  value={adBlueDetails.adblueRate && Numeral(adBlueDetails.adblueRate).format("0,0.00")}
                                  error={error.adblueRate}
                                  mandatory={adBlueDetails.adblueQuantity ? true : false}
                                  decimalScale={2}
                                  maxLength={10}
                                  onChange={(text: any) => {
                                    if (text>=1000) {
                                      setError({
                                        adblueRate : "Value cannot exceed 999.99"
                                      })
                                    }
                                    else {
                                      setAdBlueDetails({
                                        ...adBlueDetails,
                                        adblueRate: text
                                      })
                                      setError({})
                                    }
                                  }}
                                />
                              </div>}

                              <div className="form-group col-md-3">
                                <EditText
                                  label={remarksLabel}
                                  placeholder={remarksLabel}
                                  value={remarks}
                                  onChange={(text: string) => {
                                    setRemarks(text);
                                  }}
                                  maxLength={250}
                                />
                              </div>
                              {userParams?.walletTypeValue === walletListEnum.CAMIONS_RAZOR_PAY &&
                                <div className="form-group col-md-3">
                                  <EditText
                                    label={utrNoLabel}
                                    placeholder={utrNoLabel}
                                    value={utrNumber}
                                    onChange={(text: string) => {
                                      setUtrNumber(text);
                                    }}
                                    maxLength={500}
                                  />
                                </div> 
                              }
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </>
                  }

                  {(!isObjectEmpty(walletAmounts) || userParams.vehicleNumber) &&
                    <>
                      <div className="bpcl-amount mb-5 pb-5 mb-md-0 pb-md-0">
                        <ul>
                          <li>
                            <p>{`Request Amount: `}<span><strong>₹ {totalRequestedAmount.toFixed(2)}</strong></span></p>
                          </li>
                          <li>
                            <Button
                              buttonStyle={"btn-blue"}
                              title={"Create Request"}
                              leftIcon={<CheckCircle />}
                              disable={(totalRequestedAmount === 0 || isObjectEmpty(selectTrip))}
                              loading={createLoading}
                              onClick={() => {
                                const validate = validateData(walletAmounts, fuelDetails, adBlueDetails, totalRequestedAmount, userParams?.walletTypeValue)
                                if (validate === true) {
                                  setCreateLoading(true);
                                  const createRequestPromise = userParams?.wallet?.value === walletListEnum.CAMIONS_RAZOR_PAY
                                    ? createRazorPayRequest
                                    : createRequest;
                                  const params: any = createRequestParams(userParams, vehicleTrips, selectTrip, walletAmounts, fuelDetails, adBlueDetails, totalRequestedAmount, mobileNumberDetails, remarks, stateFuelRate, utrNumber);
                                  appDispatch(createRequestPromise(params)).then((response: any) => {
                                    if (response) {
                                      if(response.code === 6){
                                        setDuplicateEntryModal({open: true, message: response.message});
                                      }else{
                                        response.message && appDispatch(showUploadAlert(response.message, response.details?.walletCode, response.details?.paymentRequestId, response.details?.paymentId, true));
                                        history.push(requestListingRoute);
                                      }
                                    }
                                    setCreateLoading(false);
                                  })
                                }
                              }}
                            />
                          </li>
                        </ul>
                      </div>
                    </>}
                </>}
            </>
            : <div className="shipment-img text-center">
              <img src="/images/shipment-img.png" alt="shipment" />
            </div>
          }
        </PageContainer>
      </div>
  );

  function validateData(walletAmounts: any, fuelDetails: any, adBlueDetails: any, totalRequestAmount: any, walletType: any) {
    if (fuelDetails) {
      if (isNullValue(fuelDetails.fuelRate) && !isNullValue(fuelDetails.fuelQuantity)) {
        setError({
          fuelRate: "Enter fuel rate"
        });
        return false;
      } else if (!isNullValue(fuelDetails.fuelRate) && isNullValue(fuelDetails.fuelQuantity)) {
        setError({
          fuelQuantity: "Enter fuel quantity"
        });
        return false;
      } else if (!isNullValueOrZero(fuelDetails.fuelRate) && fuelDetails.fuelRate < 1) {
        setError({
          fuelRate: "Cannot be less than 1"
        });
        return false;
      }else if(fuelDetails.fuelQuantity>FUEL_QUANTITY_LIMIT){
        setError({
          fuelQuantity: "Cannot be more than 999.99"
        });
        return false;
      }else if(walletType==walletListEnum.BPCL || walletType==walletListEnum.JIOBP){
        if(fuelDetails?.fuelRate && !stateFuelRate?.stateRate){
          setError({
            stateList: "Please select the state."
          });
          return false;
        }else if(stateFuelRate?.stateRate && !fuelDetails?.fuelRate){
          setError({
            fuelRate: "Enter fuel rate."
          });
          return false;
        }
        if (checkBPCLVariance(stateFuelRate?.stateRate, fuelDetails?.fuelRate, walletType)) {
          if(walletType==="JioBP Wallet" || walletType==walletListEnum.JIOBP){
            setError({
              fuelRate: "Variance allowed is 0.55%."
            });
          }else{
            setError({
              fuelRate: "Variance allowed is 0.55%."
            });
          }
          return false;
        }
      }else if(fuelDetails.fuelRate>FUEL_RATE_LIMIT){
        setError({
          fuelRate: "Cannot be more than 150 ₹/Ltr."
        });
        return false;
      }
    }
    if (adBlueDetails) {
      if (isNullValue(adBlueDetails.adblueRate) && !isNullValue(adBlueDetails.adblueQuantity)) {
        setError({
          adblueRate: "Enter adblue rate"
        });
        return false;
      } else if (!isNullValue(adBlueDetails.adblueRate) && isNullValue(adBlueDetails.adblueQuantity)) {
        setError({
          adblueQuantity: "Enter adblue quantity"
        });
        return false;
      } else if (!isNullValueOrZero(adBlueDetails.adblueRate) && adBlueDetails.adblueRate < 1) {
        setError({
          adblueRate: "Cannot be less than 1"
        });
        return false;
      }else if(adBlueDetails.adblueQuantity>ADBLUE_QUANTITY_LIMIT){
        setError({
          adblueQuantity: "Cannot be more than 101.00"
        });
        return false;
      }else if(adBlueDetails.adblueRate>ADBLUE_RATE_LIMIT){
        setError({
          adblueRate: "Cannot be more than 100 ₹/Ltr."
        });
        return false;
      }
    }
    if(walletType && totalRequestAmount!==0){
      if(walletType===walletListEnum.MCPL){
        if(totalRequestAmount>MCPL_TOTAL_REQUEST_LIMIT){
          appDispatch(showAlert(mcplLimitMessage));
          return false;
        }
      }else if(walletType===walletListEnum.HUB_ADBLUE){
        if(totalRequestAmount>ADBLUE_TOTAL_REQUEST_LIMIT){
          appDispatch(showAlert(adBlueLimitMessage));
          return false;
        }
      }else if(walletType===walletListEnum.OM_PETRO_MART_CASH){
        if(totalRequestAmount>OM_PETRO_MART_CASH_LIMIT){
          appDispatch(showAlert(omPetroMartCashLimitMessage));
          return false;
        }
      }else if(walletType===walletListEnum.OM_PETRO_MART_DIESEL){
        if(totalRequestAmount>OM_PETRO_MART_DIESEL_LIMIT){
          appDispatch(showAlert(omPetroMartDieselLimitMessage));
          return false;
        }
      }else if(walletType===walletListEnum.CONTINENTAL_PETROLEUMS){
        if(totalRequestAmount>CONTINENTAL_PETROLEUMS_TOTAL_REQUEST_LIMIT){
          appDispatch(showAlert(continentalPetroleumsMessage));
          return false;
        }
      } else if (walletType === walletListEnum.GOBOLT_CASH) {
        if (totalRequestAmount > GOBOLT_CASH_LIMIT) {
          appDispatch(showAlert(goboltCashLimitMessage));
          return false;
        }
      } else if (walletType === walletListEnum.CAMIONS_RAZOR_PAY) {
        if (isNullValue(userParams?.payoutContact?.value)) {
          setError({
            payoutContact: "Select payout contact"
          });
          return false;
        }
        if (isNullValue(userParams?.payoutMethod?.value)) {
          setError({
            payoutMethod: "Select payout payment method"
          });
          return false;
        }
      }
    }
    return true;
  }
}

export default CreateRequest;
