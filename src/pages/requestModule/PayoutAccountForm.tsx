import React, { useCallback } from 'react'
import { Box, Typography, Tabs, Paper, Tab, CircularProgress } from '@material-ui/core'
import { PayoutAddContactFormData, PayoutFundAccountData } from './CreateRequest'
import Styles from './PayoutAddContactModal.module.scss'
import Button from '../../component/widgets/button/Button'
import { EditOutlined, MailOutline, CallOutlined, AccountBalanceOutlined, PublishOutlined } from '@material-ui/icons'
import EditText from '../../component/widgets/EditText'
import { OverflowTip } from '../../component/widgets/tooltip/OverFlowToolTip'
import { isMobile } from '../../base/utility/ViewUtils'
import NumberEditText from '../../component/widgets/NumberEditText'
import { isNullValue } from '../../base/utility/StringUtils'
import QrScanner from 'qr-scanner'
import { isValidUPILink, extractUPIId, isValidUPIId } from './requestModuleUtility/RequestUtility'
import { useDispatch } from 'react-redux'
import { showAlert } from '../../redux/actions/AppActions'

type PayoutAccountFormProps = {
    formData: PayoutFundAccountData;
    isUpiAccount: boolean;
    isBankAccount: boolean;
    showContactEditBtn?: boolean;
    isDisabled: boolean;
    errors: Partial<Record<keyof PayoutAddContactFormData, string>>;
    onInputChange: (field: keyof PayoutAddContactFormData, value: string) => void;
    onAccountTypeChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
    onEditContact?: () => void;
}

const PayoutAccountForm = (props: PayoutAccountFormProps) => {
    const { 
        formData, isUpiAccount, isBankAccount, showContactEditBtn = false, 
        errors, onInputChange, onAccountTypeChange, onEditContact, isDisabled
    } = props;

    const appDispatch = useDispatch();
    const [showUPIScannerLoader, setShowUPIScannerLoader] = React.useState<boolean>(false);

    const handleUploadQRCode = useCallback(async (file: File | null) => {
        if (!file) return;

        setShowUPIScannerLoader(true);

        try {
            const scanResult = await QrScanner.scanImage(file, { returnDetailedScanResult: true });
            const upilink = scanResult?.data || '';

            if (isValidUPILink(upilink)) {
                const upiId = extractUPIId(upilink);
                if (upiId && isValidUPIId(upiId)) {
                    onInputChange("vpa", upiId)
                } else {
                    appDispatch(showAlert("Invalid UPI ID found in the QR code. Please scan a valid QR code."));
                }
            } else {
                appDispatch(showAlert("Invalid UPI Link. Please scan a valid QR code."));
            }
            setShowUPIScannerLoader(false);
        } catch (error) {
            console.error('Error scanning QR code:', error);
            appDispatch(showAlert("No QR code found"));
            setShowUPIScannerLoader(false);
        }
    }, [appDispatch, onInputChange]);

    return <>
        {/*  Account Info  */}
        <div className={`${Styles.info_container}`}>
            <Box className={Styles.info_wrap}>
                <div className="col d-flex align-items-center pl-0">
                    <Box component="h2" className={Styles.name}>
                        <OverflowTip
                            text={formData.name}
                            elementStyle={isMobile ? { maxWidth: "210px", minWidth: "auto" } : {}}
                        />
                    </Box>
                    {!isNullValue(formData.type) && (
                        <span className={Styles.type}>{formData.type || ''}</span>
                    )}
                </div>
                {showContactEditBtn && (
                    <div className="col-auto pr-0">
                        <Button
                            buttonStyle={Styles.editIcon}
                            leftIcon={<EditOutlined />}
                            onClick={onEditContact}
                            disable={isDisabled}
                        />
                    </div>
                )}
            </Box>
            <Box className={Styles.info_wrap}>
                <Typography component={"p"} className={Styles.text_info}>
                    {!isNullValue(formData.contact) && (
                        <>
                            <CallOutlined className={Styles.icon} /> {formData.contact}
                        </>
                    )}
                </Typography>
                {!isNullValue(formData.email) && (
                    <div className={Styles.text_info}>
                        <OverflowTip
                            cellIcon={<MailOutline className={`${Styles.icon}`} />}
                            elementStyle={isMobile ? { maxWidth: "190px", minWidth: "190px" } : {}}
                            text={formData.email}
                        />
                    </div>
                )}
            </Box>
        </div>
        <Typography component={"h3"} className={Styles.heading}>
            Add Fund Account
        </Typography>
        <div className={`${Styles.fund_account}`}>
            <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                <h3 className={Styles.heading} style={{ fontWeight: "normal" }}>
                    Select Account Type
                            </h3>
                <Tabs
                    className={Styles.tabs}
                    value={formData.accountTypeTabIndex}
                    onChange={onAccountTypeChange}
                    aria-label="tabs"
                >
                    <Tab label="UPI" icon={<img
                        src={formData.accountTypeTabIndex === 0 ? "/images/upi-active.svg" : "/images/upi.svg"}
                        alt="upi"
                    />} />
                    <Tab label="Bank Account" icon={<AccountBalanceOutlined />} />
                </Tabs>
            </Box>
            <Box>
                {isUpiAccount && (
                    <Box className={`${Styles.upiForm} ${Styles.accountForm}`} component={'div'}>
                        <Paper className={Styles.form_Wrap} style={{
                            gridTemplateColumns: "1fr auto"
                        }}>
                            <EditText
                                label={"VPA (UPI ID)"}
                                mandatory={true}
                                value={formData.vpa}
                                placeholder={"UPI ID"}
                                maxLength={60}
                                error={errors.vpa}
                                disabled={isDisabled}
                                onChange={(accountType: string) => onInputChange("vpa", accountType)}
                            />
                            <Box
                                className="upload-btn"
                                component={"div"}
                            >
                                <div className="upload-inner"
                                    style={{
                                        background: "#fff",
                                        border: "solid 2px #ebeff3",
                                        height: "47px"
                                    }}
                                >
                                    {showUPIScannerLoader && <CircularProgress size={16} />}
                                    <PublishOutlined className="upload-icon" />
                                    <span className="title">QR Upload</span>
                                    <input
                                        type="file"
                                        className="upload-input"
                                        accept="image/*"
                                        disabled={isDisabled}
                                        onChange={(e) => {
                                            const file = e.target.files?.[0];
                                            if (file) {
                                                handleUploadQRCode(file);
                                                e.target.value = ''; // Clear the input value to allow re-uploading the same file
                                            }
                                        }}
                                    />
                                </div>
                            </Box>
                        </Paper>
                    </Box>
                )}

                {isBankAccount && (
                    <Box className={Styles.accountForm} component={'div'}>
                        <Paper className={Styles.form_Wrap}>
                            <NumberEditText
                                label={"Account No."}
                                mandatory={true}
                                value={formData.accountNumber}
                                placeholder={"Account No."}
                                maxLength={18}
                                error={errors.accountNumber}
                                disabled={isDisabled}
                                decimalSeparator={false}
                                decimalScale={0}
                                showFormattedValue={true}
                                onChange={(accountNumber: string) => {
                                    onInputChange("accountNumber", accountNumber.trim());
                                }}
                            />
                            <NumberEditText
                                label={"Confirm Account No."}
                                mandatory={true}
                                value={formData.confirmAccountNumber}
                                placeholder={"Confirm Account No."}
                                maxLength={18}
                                error={errors.confirmAccountNumber}
                                disabled={isDisabled}
                                decimalSeparator={false}
                                decimalScale={0}
                                showFormattedValue={true}
                                onChange={(accountNumber: string) => {
                                    onInputChange("confirmAccountNumber", accountNumber.trim());
                                }}
                            />
                            <EditText
                                label={"IFSC"}
                                mandatory={true}
                                value={formData.ifsc}
                                placeholder={"IFSC"}
                                maxLength={11}
                                error={errors.ifsc}
                                disabled={isDisabled}
                                onChange={(ifscCode: string) => onInputChange("ifsc", ifscCode)}
                            />
                            <EditText
                                label={"Beneficiary Name"}
                                value={formData.beneficiaryName}
                                placeholder={"Beneficiary Name"}
                                mandatory={true}
                                maxLength={250}
                                error={errors.beneficiaryName}
                                disabled={isDisabled}
                                onChange={(beneficiaryName: string) => onInputChange("beneficiaryName", beneficiaryName)}
                            />
                        </Paper>
                    </Box>
                )}
            </Box>
        </div>
    </>;
}

export default PayoutAccountForm