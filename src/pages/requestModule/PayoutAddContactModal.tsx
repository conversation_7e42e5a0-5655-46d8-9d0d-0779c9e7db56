import React, { useCallback } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>B<PERSON> } from '@material-ui/icons'
import Styles from './PayoutAddContactModal.module.scss'
import Box from '@material-ui/core/Box/Box'
import ModalContainer from '../../modals/ModalContainer'
import { isValidEmailId, isNullValue } from '../../base/utility/StringUtils'
import { PayoutAddContactFormData } from './CreateRequest'
import PayoutContactForm from './PayoutContactForm'
import PayoutAccountForm from './PayoutAccountForm'
import { useDispatch } from 'react-redux'
import { createPayoutContact, addPayoutFundAccountBankForContactId, addPayoutFundAccountVpaForContactId, updatePayoutContact } from '../../serviceActions/RequestServiceAction'
import { showAlert } from '../../redux/actions/AppActions'
import { IFSC_CODE_REGEX, BANK_ACCOUNT_NO_REGEX } from '../../base/moduleUtility/ConstantValues'

const CONTACT_FORM_PAGE = 0;
const ACCOUNT_FORM_PAGE = 1;

type FormErrors = Partial<Record<keyof PayoutAddContactFormData, string>>;

type ContactModalProps = {
    isOpen: boolean;
    onClose: () => void;
    onSaveSuccess: (step: 'CONTACT_ADDED' | 'FUND_ACCOUNT_ADDED') => void;
};

type AddEditContactPayload = {
    name: string;
    email: string;
    type: string | undefined;
    contact: string;
    contactId?: string;
}

const INITIAL_FORM_DATA: PayoutAddContactFormData = {
    name: '',
    type: undefined,
    contact: '',
    email: '',
    accountType: 'vpa',
    accountTypeTabIndex: 0,
    beneficiaryName: '',
    ifsc: '',
    accountNumber: '',
    confirmAccountNumber: '',
    vpa: '',
};

const validateContactFields = (data: PayoutAddContactFormData): FormErrors => {
    const errors: FormErrors = {};
    if (isNullValue(data.name)) {
        errors.name = "Name is required";
    }
    if (isNullValue(data.type)) {
        errors.type = "Contact Type is required";
    }

    if (!isNullValue(data.contact) && data.contact.length !== 10) {
        errors.contact = "Contact Phone must be 10 digits";
    }

    if (!isNullValue(data.email) && !isValidEmailId(data.email)) {
        errors.email = "Email is not valid";
    }

    return errors;
}

const validateAccountFields = (data: PayoutAddContactFormData): FormErrors => {
    const errors: FormErrors = {};
    if (data.accountType === 'bank_account') {
        if (isNullValue(data.beneficiaryName)) {
            errors.beneficiaryName = "Beneficiary Name is required";
        }
        if (isNullValue(data.ifsc)) {
            errors.ifsc = "IFSC is required";
        }
        if (!IFSC_CODE_REGEX.test(data.ifsc)) {
            errors.ifsc = 'IFSC code is not valid';
        }
        if (isNullValue(data.accountNumber)) {
            errors.accountNumber = "Account Number is required";
        }
        if (!BANK_ACCOUNT_NO_REGEX.test(data.accountNumber)) {
            errors.accountNumber = 'Account number is not valid';
        }
        if (isNullValue(data.confirmAccountNumber)) {
            errors.confirmAccountNumber = "Confirm Account Number is required";
        }
        if (!BANK_ACCOUNT_NO_REGEX.test(data.confirmAccountNumber)) {
            errors.confirmAccountNumber = 'Account number is not valid';
        }
        if (data.accountNumber !== data.confirmAccountNumber) {
            errors.confirmAccountNumber = "Account Number does not match";
        }
    } else {
        if (isNullValue(data.vpa)) {
            errors.vpa = "VPA is required";
        }
    }
    return errors;
}

export const PayoutAddContactModal = (props: ContactModalProps) => {
    const { isOpen, onClose, onSaveSuccess } = props;
    const appDispatch = useDispatch();
    const [currentPage, setCurrentPage] = React.useState(CONTACT_FORM_PAGE);
    const [payoutAddContactFormData, setPayoutAddContactFormData] = React.useState<PayoutAddContactFormData>(INITIAL_FORM_DATA);
    const [errors, setErrors] = React.useState<FormErrors>({});
    const [isSubmitting, setIsSubmitting] = React.useState(false);
    const [newlyCreatedOrUpdatedContactId, setNewlyCreatedOrUpdatedContactId] = React.useState<string | undefined>(undefined);

    const isContactPage = currentPage === CONTACT_FORM_PAGE;
    const isAccountPage = currentPage === ACCOUNT_FORM_PAGE;
    const isUPiAccountType = payoutAddContactFormData.accountTypeTabIndex === 0;
    const isBankAccountType = payoutAddContactFormData.accountTypeTabIndex === 1;
    const { name, email, type, contact } = payoutAddContactFormData;

    const handleInputDataChange = useCallback((field: keyof PayoutAddContactFormData, value: string) => {
        setPayoutAddContactFormData(prevState => {
            if (field === 'name') {
                return {
                    ...prevState,
                    name: value,
                    beneficiaryName: value
                };
            }
            return {
                ...prevState,
                [field]: value
            };
        });

        // clear error for the field
        if (errors[field]) {
            setErrors(prevState => ({
                ...prevState,
                [field]: undefined
            }));
        }
    }, [errors])

    const handleAccountTypeTabChange = useCallback((_event: React.ChangeEvent<{}>, newValue: number) => {
        setPayoutAddContactFormData(prevState => {
            const isVpa = newValue === 0;

            return {
                ...prevState,
                accountType: newValue === 0 ? 'vpa' : 'bank_account',
                accountTypeTabIndex: newValue,
                ...(isVpa
                    ? {
                        accountNumber: '',
                        confirmAccountNumber: '',
                        ifsc: '',
                    }
                    : {
                        vpa: '',
                    }),
            };
        });
    }, [])

    const handleCreateContact = useCallback(async () => {
        const contactErrors = validateContactFields(payoutAddContactFormData);
        setErrors(contactErrors);

        if (Object.keys(contactErrors).length === 0) {
            let createContactPayload: AddEditContactPayload = {
                name: name.trim(),
                email,
                type: type?.toLowerCase(),
                contact,
            };

            createContactPayload = isNullValue(newlyCreatedOrUpdatedContactId)
                ? createContactPayload
                : {
                    ...createContactPayload,
                    contactId: newlyCreatedOrUpdatedContactId,
                };

            const addEditContactPromise = isNullValue(newlyCreatedOrUpdatedContactId)
                ? createPayoutContact
                : updatePayoutContact;

            setIsSubmitting(true);
            try {
                const addEditContactResponse = await appDispatch(addEditContactPromise(createContactPayload));
                if (addEditContactResponse?.code === 200) {
                    setNewlyCreatedOrUpdatedContactId(addEditContactResponse?.details?.id);
                    setCurrentPage(ACCOUNT_FORM_PAGE);
                    onSaveSuccess('CONTACT_ADDED');
                }
                setIsSubmitting(false);
            } catch (error) {
                setIsSubmitting(false);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [appDispatch, contact, email, name, type])

    const handlePreviousPage = useCallback(() => {
        setCurrentPage(CONTACT_FORM_PAGE);
    }, [])

    const handleSubmit = useCallback(async () => {
        if (newlyCreatedOrUpdatedContactId === undefined) {
            appDispatch(showAlert("Please create contact first"));
            return;
        }

        const errors = validateAccountFields(payoutAddContactFormData);
        setErrors(errors);

        if (Object.keys(errors).length !== 0) {
            return;
        }

        const accountType = payoutAddContactFormData?.accountType;

        // save the data
        const basePayload = {
            account_type: accountType,
            contact_id: newlyCreatedOrUpdatedContactId,
        };

        const payload = accountType === 'bank_account'
            ? {
                ...basePayload,
                bank_account: {
                    account_number: payoutAddContactFormData?.accountNumber,
                    ifsc: payoutAddContactFormData?.ifsc,
                    name: payoutAddContactFormData?.beneficiaryName,
                }
            }
            : {
                ...basePayload,
                vpa: {
                    address: payoutAddContactFormData?.vpa,
                }
            };

        const addFundAccountPromise = accountType === 'bank_account'
            ? addPayoutFundAccountBankForContactId
            : addPayoutFundAccountVpaForContactId;

        setIsSubmitting(true);
        try {
            const response = await appDispatch(addFundAccountPromise(payload));
            if (response?.code === 200) {
                onSaveSuccess('FUND_ACCOUNT_ADDED');
            }
            setIsSubmitting(false);
        } catch (error) {
            console.log(error);
            setIsSubmitting(false);
        }

    }, [newlyCreatedOrUpdatedContactId, payoutAddContactFormData, appDispatch, onSaveSuccess])

    const handleModalAction = useCallback(() => {
        if (currentPage === CONTACT_FORM_PAGE) {
            handleCreateContact();
        } else {
            // submit form
            handleSubmit();
        }
    }, [currentPage, handleCreateContact, handleSubmit])

    return (
        <ModalContainer
            title={"Add New Contact"}
            open={isOpen}
            styleName={"modal-contact"}
            onApply={handleModalAction}
            onClear={handlePreviousPage}
            onClose={onClose}
            actionButtonStyle={'justify-content-between'}
            primaryButtonTitle={currentPage === CONTACT_FORM_PAGE ? "Next" : "Save"}
            primaryButtonLeftIcon={<ArrowForward />}
            secondaryButtonTitle={"Back"}
            secondaryButtonStyle={currentPage === CONTACT_FORM_PAGE ? 'd-none' : 'btn-grey--cancel'}
            secondaryButtonLeftIcon={<ArrowBack />}
            loading={isSubmitting}
        >
            <Box className={Styles.Contact_container}>

                {isContactPage && (
                    /* Contact Info */
                    <PayoutContactForm
                        formData={{ name, email, type, contact }}
                        errors={errors}
                        isDisabled={isSubmitting}
                        onInputChange={handleInputDataChange}
                    />
                )}

                {isAccountPage && (
                    /* Account Info */
                    <PayoutAccountForm
                        formData={payoutAddContactFormData}
                        errors={errors}
                        isDisabled={isSubmitting}
                        isUpiAccount={isUPiAccountType}
                        isBankAccount={isBankAccountType}
                        showContactEditBtn={true}
                        onInputChange={handleInputDataChange}
                        onAccountTypeChange={handleAccountTypeTabChange}
                        onEditContact={handlePreviousPage}
                    />
                )}
            </Box>
        </ModalContainer>
    );
}
