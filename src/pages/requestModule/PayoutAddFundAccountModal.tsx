import React, { useCallback, useState } from 'react'
import ModalContainer from '../../modals/ModalContainer'
import { saveTitle } from '../../base/constant/MessageUtils'
import { ArrowForward } from '@material-ui/icons'
import Styles from './PayoutAddContactModal.module.scss'
import { Box } from '@material-ui/core'
import PayoutAccountForm from './PayoutAccountForm'
import { PayoutFundAccountData, PayoutContactData } from './CreateRequest'
import { isNullValue } from '../../base/utility/StringUtils'
import { useDispatch } from 'react-redux'
import { addPayoutFundAccountBankForContactId, addPayoutFundAccountVpaForContactId } from '../../serviceActions/RequestServiceAction'
import { IFSC_CODE_REGEX, BANK_ACCOUNT_NO_REGEX } from '../../base/moduleUtility/ConstantValues'

type PayoutAddFundAccountModalProps = {
  isOpen: boolean;
  contactData: PayoutContactData;
  onClose: () => void;
  onsaveSuccess: () => void;
}

const setInitialFormData = (contactData: PayoutContactData): PayoutFundAccountData => {
  return {
    id: contactData?.id,
    name: contactData.name,
    email: contactData.email,
    contact: contactData.contact,
    type: contactData.type,
    accountType: 'vpa',
    beneficiaryName: contactData.name,
    ifsc: '',
    accountNumber: '',
    confirmAccountNumber: '',
    vpa: '',
    accountTypeTabIndex: 0
  }
}

type FormErrors = Partial<Record<keyof PayoutFundAccountData, string>>;

const validateAccountFields = (data: PayoutFundAccountData): FormErrors => {
  const errors: FormErrors = {};
  if (data.accountType === 'bank_account') {
    if (isNullValue(data.beneficiaryName)) {
      errors.beneficiaryName = "Beneficiary Name is required";
    }
    if (isNullValue(data.ifsc)) {
      errors.ifsc = "IFSC is required";
    }
    if (!IFSC_CODE_REGEX.test(data.ifsc)) {
      errors.ifsc = 'IFSC code is not valid';
    }
    if (isNullValue(data.accountNumber)) {
      errors.accountNumber = "Account Number is required";
    }
    if (!BANK_ACCOUNT_NO_REGEX.test(data.accountNumber)) {
      errors.accountNumber = 'Account number is not valid';
    }
    if (isNullValue(data.confirmAccountNumber)) {
      errors.confirmAccountNumber = "Confirm Account Number is required";
    }
    if (!BANK_ACCOUNT_NO_REGEX.test(data.confirmAccountNumber)) {
      errors.confirmAccountNumber = 'Account number is not valid';
    }
    if (data.accountNumber !== data.confirmAccountNumber) {
      errors.confirmAccountNumber = "Account Number does not match";
    }
  } else {
    if (isNullValue(data.vpa)) {
      errors.vpa = "VPA is required";
    }
  }
  return errors;
}

const PayoutAddFundAccountModal = (props: PayoutAddFundAccountModalProps) => {
  const { isOpen, onClose, contactData, onsaveSuccess } = props;
  const appDispatch = useDispatch();
  const [payoutFundAccountFormData, setPayoutFundAccountFormData] = useState<PayoutFundAccountData>(() => setInitialFormData(contactData));
  const [errors, setErrors] = React.useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isUPiAccountType = payoutFundAccountFormData.accountTypeTabIndex === 0;
  const isBankAccountType = payoutFundAccountFormData.accountTypeTabIndex === 1;

  const handleInputDataChange = useCallback((field: keyof PayoutFundAccountData, value: string) => {
    setPayoutFundAccountFormData(prevState => ({
      ...prevState,
      [field]: value
    }));

    // clear error for the field
    if (errors[field]) {
      setErrors(prevState => ({
        ...prevState,
        [field]: undefined
      }));
    }
  }, [errors])

  const handleAccountTypeTabChange = useCallback((_event: React.ChangeEvent<{}>, newValue: number) => {
    setPayoutFundAccountFormData(prevState => {
      const isVpa = newValue === 0;

      return {
        ...prevState,
        accountType: isVpa ? 'vpa' : 'bank_account',
        accountTypeTabIndex: newValue,
        ...(isVpa
          ? {
            accountNumber: '',
            confirmAccountNumber: '',
            ifsc: '',
          }
          : {
            vpa: '',
          }),
      };
    });

  }, [])

  const handleSave = useCallback(async () => {
    const errors = validateAccountFields(payoutFundAccountFormData);
    if (Object.keys(errors).length > 0) {
      setErrors(errors);
      return;
    }

    const accountType = payoutFundAccountFormData?.accountType;

    // save the data
    const basePayload = {
      account_type: accountType,
      contact_id: payoutFundAccountFormData?.id,
    };

    const payload = accountType === 'bank_account'
      ? {
        ...basePayload,
        bank_account: {
          account_number: payoutFundAccountFormData?.accountNumber,
          ifsc: payoutFundAccountFormData?.ifsc,
          name: payoutFundAccountFormData?.beneficiaryName,
        }
      }
      : {
        ...basePayload,
        vpa: {
          address: payoutFundAccountFormData?.vpa,
        }
      };

    const addFundAccountPromise = accountType === 'bank_account'
      ? addPayoutFundAccountBankForContactId
      : addPayoutFundAccountVpaForContactId;

    setIsSubmitting(true);

    try {
      const response = await appDispatch(addFundAccountPromise(payload));
      if (response?.code === 200) {
        onsaveSuccess();
      }
      setIsSubmitting(false);
    } catch (error) {
      console.log(error);
      setIsSubmitting(false);
    }
  }, [appDispatch, onsaveSuccess, payoutFundAccountFormData])

  return (
    <ModalContainer
      open={isOpen}
      title="Add Fund Account"
      onClose={onClose}
      onApply={handleSave}
      loading={isSubmitting}
      styleName={"modal-contact"}
      primaryButtonTitle={saveTitle}
      primaryButtonLeftIcon={<ArrowForward />}
    >
      <Box className={Styles.Contact_container}>
        <PayoutAccountForm
          formData={payoutFundAccountFormData}
          errors={errors}
          isDisabled={isSubmitting}
          isUpiAccount={isUPiAccountType}
          isBankAccount={isBankAccountType}
          onInputChange={handleInputDataChange}
          onAccountTypeChange={handleAccountTypeTabChange}
        />
      </Box>
    </ModalContainer>
  )
}

export default PayoutAddFundAccountModal