import React from 'react'
import { PayoutContactData } from './CreateRequest'
import { Box } from '@material-ui/core';
import EditText from '../../component/widgets/EditText';
import { OptionType } from '../../component/widgets/widgetsInterfaces';
import { payoutContactTypeOptions } from '../../base/constant/ArrayList';
import AutoComplete from '../../component/widgets/AutoComplete';
import NumberEditText from '../../component/widgets/NumberEditText';
import Styles from './PayoutAddContactModal.module.scss'
import { isNullValue } from '../../base/utility/StringUtils';
import { ALPHABETIC_WITH_SPACE_REGEX } from '../../base/moduleUtility/ConstantValues';

type PayoutContactFormProps = {
    formData: PayoutContactData;
    isDisabled: boolean;
    errors: Partial<Record<keyof PayoutContactData, string>>;
    onInputChange: (field: keyof PayoutContactData, value: string) => void;
}

const PayoutContactForm = React.memo((props: PayoutContactFormProps) => {
    const { formData, errors, onInputChange, isDisabled } = props;
    return (
        <Box className={Styles.Contact_info}>
            <EditText
                label={"Contact Name"}
                mandatory={true}
                value={formData.name}
                placeholder={"Contact Name"}
                maxLength={100}
                error={errors.name}
                disabled={isDisabled}
                onChange={(name: string) => {
                    onInputChange("name", name);
                }}
            />
            <Box sx={{ width: "100%" }}>
                <AutoComplete
                    label={"Contact Type"}
                    mandatory={true}
                    value={formData.type
                        ? { label: formData.type, value: formData.type }
                        : undefined
                    }
                    placeHolder={"Contact Type"}
                    error={errors.type}
                    isDisabled={isDisabled}
                    options={payoutContactTypeOptions}
                    onChange={(contactType: OptionType) => onInputChange('type', contactType.value)}
                />
            </Box>
            <NumberEditText
                label={"Contact Number"}
                value={formData.contact}
                placeholder={"Contact Number"}
                maxLength={10}
                decimalSeparator={false}
                error={errors.contact}
                disabled={isDisabled}
                onChange={(contact: string) => onInputChange("contact", contact)}
            />
            <Box>
                <EditText
                    label={"Contact Email"}
                    value={formData.email}
                    placeholder={"Contact Email"}
                    maxLength={60}
                    error={errors.email}
                    disabled={isDisabled}
                    onChange={(email: string) => onInputChange("email", email)}
                />
            </Box>
        </Box>
    )
})

export default PayoutContactForm