.button-list {
  display: flex;
  justify-content: flex-end;
  @media screen and (max-width: 767px) {
    flex-wrap: wrap;
  }
  button {
    margin-left: 16px;
    @media screen and (max-width: 767px) {
      margin: 0 13px 15px 0;
    }
  }
}
.btn--approve {
  height: 25px;
  border: 1px solid #12548d;
  border-radius: 3px;
  margin-left: 5px;
  span {
    color: #12548d;
  }
  @media screen and (max-width: 767px) {
    font-size: 7px;
    padding: 0;
    height: 12px;
    margin-left: 2px;
  }
}

#payment-not-found{
  height: 100px;
  margin-bottom: 5px;
}
.upload-payment{
  .legacy-currency {
    margin-right: 10px;
    background-color: #fee9d2;
    svg {
      color: #f7931e;
  }
  }
}
.upload-request {
    border-left: 1px solid #ccc;
    padding-left: 10px;
    margin-left: 10px;
}
.upload-radius-btn {
  border-radius: 5px;
  padding-right: 10px;
}
.id-wrap{
  padding: 10px 0;
}
.id-wrap .title-label{
  margin-right: 8px;
}
.id-wrap .title-label,
.id-wrap .title-value{
  font-size: 16px;
  color: #083654;
  font-weight: 500;
}
.uploadedDocIcon {
  border: none;
  position: relative;
  margin-right: 8px;;
  p{
    position: absolute;
    top: -5px;
    right: -3px;
    background: #fff;
    border-radius: 22px;
    width: 17px;
    line-height: 17px;
    font-size: 11px;
    box-shadow: 0px 3px 6px #00000029;
  }
}
@media screen and (max-width: 767px) {
  .id-wrap{
    padding: 4px 0;
  }
  .id-wrap .title-label,
  .id-wrap .title-value{
    font-size: 13px;
  }
  .upload-request{flex: 1;}
}