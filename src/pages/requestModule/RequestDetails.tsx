import { Card, CardContent } from "@material-ui/core";
import { AccountBalanceWalletRounded, Edit, KeyboardBackspace, Publish, Visibility } from "@material-ui/icons";
import CheckCircleIcon from '@material-ui/icons/CheckCircle';
import ClearIcon from "@material-ui/icons/Clear";
import Numeral from 'numeral';
import React, { useEffect, useReducer } from "react";
import { shallowEqual, useDispatch, useSelector } from "react-redux";
import { useHistory, useParams } from "react-router-dom";
import { fuelAndAdBlueWalletModify, fuelWalletModify, paymentStatusEnum, requestListingStatusEnum, balanceWalletListEnum, walletAmountsArray, walletListEnum } from "../../base/constant/ArrayList";
import { ADBLUE_QUANTITY_LIMIT, ADBLUE_RATE_LIMIT, ADBLUE_TOTAL_REQUEST_LIMIT, CONTINENTAL_PETROLEUMS_TOTAL_REQUEST_LIMIT, FUEL_QUANTITY_LIMIT, FUEL_RATE_LIMIT, MCPL_TOTAL_REQUEST_LIMIT, OM_PETRO_MART_CASH_LIMIT, OM_PETRO_MART_DIESEL_LIMIT } from "../../base/constant/limits";
import { accidentSettlemantAmountLabel, adblueAmountLabel, adBlueLimitMessage, adblueQuantityLabel, adblueRatePerLitreLabel, advanceAmountLabel, challanAmountLabel, continentalPetroleumsMessage, foodingAmountLabel, fuelAmountLabel, fuelQuantityLabel, fuelRatePerLitreLabel, loadingAmountLabel, maintainceAmountLabel, mcplLimitMessage, omPetroMartCashLimitMessage, omPetroMartDieselLimitMessage, remarksLabel, tollAmountLabel, unloadingAmountLabel, utrNoLabel } from "../../base/constant/MessageUtils";
import { requestListingRoute } from "../../base/constant/RoutePath";
import { isNullValue, isNullValueOrZero, isObjectEmpty } from "../../base/utility/StringUtils";
import { isMobile } from "../../base/utility/ViewUtils";
import Filter from "../../component/filter/Filter";
import PageContainer from "../../component/pageContainer/PageContainer";
import Button from "../../component/widgets/button/Button";
import CardContentSkeleton from "../../component/widgets/cardContentSkeleton/CardContentSkeleton";
import EditText from "../../component/widgets/EditText";
import NumberEditText from "../../component/widgets/NumberEditText";
import { InfoTooltip } from "../../component/widgets/tooltip/InfoTooltip";
import UploadDocumentModal from "../../modals/UploadDocumentModal/UploadDocumentModal";
import ViewDocumentModal from "../../modals/ViewDocumentModal/ViewDocumentModal";
import { hideUploadDocumentsModal, pollStart, showAlert, hideDuplicateEntryModal } from "../../redux/actions/AppActions";
import { createApproveRequest, orchestrationToken, reconcileCamionsWalletRequest } from "../../serviceActions/OpsWorkflowServiceAction";
import { getCurrentWalletBalance, getIoclMobileNumber, getPreviousPaymentsRequest, getRequestDetails, getTripStatus, modifyRequest, getStateList, getStateFuelRate, getJioBPCardNumber, getJioBPMobileNumber, getDriverLicense, getVehicleDetails  } from "../../serviceActions/RequestServiceAction";
import "./RequestDetails.scss";
import "../../component/filter/Filter.scss";
import ApproveMobileNumberModal from "./requestModals/approveMobileNumberModal/ApproveMobileNumberModal";
import RequestActionModal from "./requestModals/requestActionModal/RequestActionModal";
import ResolveActionModal from "./requestModals/resolveActionModal/ResolveActionModal";
import { refreshList } from "./requestModuleRedux/RequestActions";
import RequestReducer, { REQUEST_MODULE_STATE } from "./requestModuleRedux/RequestReducer";
import RequestAmounts from "./requestModuleUtility/RequestAmounts";
import { checkBPCLVariance, fetchVehicleDetails, getPageLabelName, getWalletNameInDetails, isPaidGobolCashWalletReconcileRequest, isPaidMaintenanceRequestForHappayOrCamionsRazorPay, isPaidReactiveMaintenanceRequest, isReactiveRepairMaintenenceRequest, isRequestFromMaintenanceModule, modifyRequestParams, showAmountField, showBalanceField, showGoboltCashReconcileDocuments } from "./requestModuleUtility/RequestUtility"
import AutoComplete from "../../component/widgets/AutoComplete";
import { OptionType } from "../../component/widgets/widgetsInterfaces";
import DuplicateEntryModal from "../../modals/DuplicateEntryModal/DuplicateEntryModal";
import { CustomTooltipTable } from "../../component/widgets/tooltip/CustomTooltipTable/CustomToolTipTable";
import { getGoboltTooltipData, getGoboltTooltipTableColumns } from "../../templates/RequestListingTemplate";
import ReactiveRepairMaintenanceRequestInfo from "./requestModuleUtility/ReactiveRepairMaintenanceRequestInfo";
import ReactiveRepairData from "./components/ReactiveRepairData";
import TripData from "./components/TripData";
import PreviousPaymentsRequests from "./components/PreviousPaymentsRequests";
import VehicleDetailsCard from "./components/VehicleDetailsCard";
import PayoutDetailsCard from "./components/PayoutDetailsCard";


function RequestDetails() {
  const appDispatch = useDispatch();
  const history = useHistory();
  // const localState = useLocation<any>()
  const { id } = useParams<any>();
  const [loading, setLoading] = React.useState<boolean>(false);
  const [response, setResponse] = React.useState<any>({});
  const [editable, setEditable] = React.useState<boolean>(false);
  const [error, setError] = React.useState<any>({});
  const [pollingLoader, setPollingLoader] = React.useState<boolean>(false);
  const [cancelPollingLoader, setCancelPollingLoader] = React.useState<boolean>(false);
  const [totalRequestedAmount, setTotalRequestedAmount] = React.useState<any>(response?.totalRequestAmount)
  const [walletAmounts, setWalletAmounts] = React.useState<any>({});
  const [fuelDetails, setFuelDetails] = React.useState<any>({});
  const [adBlueDetails, setAdBlueDetails] = React.useState<any>({});
  const [selectedItem, setSelectedItem] = React.useState<any>({});
  const [actionButton, setActionButtons] = React.useState<any>({});
  const [tripStatus, setTripStatus] = React.useState<any>("");
  const rolesList = useSelector((state: any) => state.appReducer.rolesList, shallowEqual);
  const appReducer = useSelector((state: any) => state.appReducer, shallowEqual);
  const [state = REQUEST_MODULE_STATE, dispatch] = useReducer(RequestReducer, REQUEST_MODULE_STATE);
  const [requestAction, setRequestAction] = React.useState<any>({
    onClickedReject: false,
    onClickedCancel: false,
    onClickedApprove: false,
    onClickedCancelPaid: false,
  })
  const [approveMobileNumber, setApproveMobileNumber] = React.useState<boolean>(false);
  const [remarks, setRemarks] = React.useState<string>("");
  const [previousPayments, setPreviousPayments] = React.useState<any>({});
  const [paymentsLoading, setPaymentsLoading] = React.useState<any>([]);
  const [stateList, setStateList] = React.useState<any>([]);
  const [statesLoading, setStatesLoading] = React.useState<any>([]);
  const [stateFuelRate, setStateFuelRate] = React.useState<any>();
  const [resolveAction, setResolveAction] = React.useState<boolean>(false);
  const [openUploadDocumentsModal, setOpenUploadDocumentsModal] = React.useState<boolean>(false);
  const [uploadDocuments, setUploadDocuments] = React.useState<any>([]);
  const [onCancel, setOnCancel] = React.useState<boolean>(false);
  const [currentWalletBalance, setCurrentWalletBalance] = React.useState<string>("");
  const [walletbalanceLoading, setWalletbalanceLoading] = React.useState<boolean>(false);
  const [openViewDocumentsModal, setOpenViewDocumentsModal] = React.useState<boolean>(false);
  const [documentLinks, setDocumentLinks] = React.useState<Array<any>>([]);
  const [selectedState, setSelectedState] = React.useState<any>("");
  const [duplicateEntryModal, setDuplicateEntryModal] = React.useState<any>({open: false, message: ""});
  const [confirmMessage, setConfirmMessage] = React.useState<string>("");
  const [utrNumber, setUtrNumber] = React.useState<string>("");
  const [isGoboltCashReconcileClicked, setIsGoboltCashReconcileClicked] = React.useState<boolean>(false);
  const [vehicleDetails, setVehicleDetails] = React.useState<any>({});
  const [payoutDetails, setPayoutDetails] = React.useState<any>({});

  useEffect(() => {
    const getDetails = async () => {
      setLoading(true);
      if (rolesList) {
        const isEditor = rolesList.indexOf("payments-editor")
        const isViewer = rolesList.indexOf("payments-viewer")
        const isApprover = rolesList.indexOf("payments-approver")
        const isUnknownResolver = rolesList.indexOf("unknown-resolver")
        const isGoboltCashReconciler = rolesList.indexOf("gobolt_cash_reconciler")
        const isGoboltCashCanceller = rolesList.indexOf("gobolt-cash-canceller")
        if (isGoboltCashReconciler >= 0) {
          setActionButtons((prevState: any) => (
            {
              ...prevState,
              modify: false,
              cancel: false,
              reject: false,
              approve: false,
              cancelPaid: false,
              goboltCashReconcile: true,
            }
          ))
        }
        if (isGoboltCashCanceller >= 0) {
          setActionButtons((prevState: any) => (
            {
              ...prevState,
              modify: false,
              cancel: false,
              reject: false,
              approve: false,
              cancelPaid: false,
              goboltCashCanceller: true
            }
          ))
        }
        if(isUnknownResolver >= 0 && isEditor >= 0 && isApprover >= 0){
          setActionButtons((prevState: any) => (
            {
              ...prevState,
              modify: true,
              reject: true,
              cancel: true,
              approve: true,
              cancelPaid: true,
              unknownResolve: true,
            }
         ))
        }else if(isUnknownResolver >= 0 && isEditor >= 0){
          setActionButtons((prevState: any) => (
            {
              ...prevState,
              modify: true,
              cancel: true,
              reject: false,
              approve: false,
              cancelPaid: false,
              unknownResolve: false,
            }
          ))
        }else if(isUnknownResolver >= 0 && isApprover >= 0){
          setActionButtons((prevState: any) => (
            {
              ...prevState,
              modify: false,
              cancel: false,
              reject: true,
              approve: true,
              cancelPaid: true,
              unknownResolve: true,
            }
          ))
        }else if(isUnknownResolver >= 0 && isViewer >= 0){
          setActionButtons((prevState: any) => (
            {
              ...prevState,
              modify: false,
              cancel: false,
              reject: false,
              approve: false,
              cancelPaid: false,
              unknownResolve: false,
            }
          ))
        }else if (isEditor >= 0 && isApprover >= 0) {
          setActionButtons((prevState: any) => (
            {
              ...prevState,
              modify: true,
              reject: true,
              cancel: true,
              approve: true,
              cancelPaid: true,
            }
          ))
        } else if (isEditor >= 0) {
          setActionButtons((prevState: any) => (
            {
              ...prevState,
              modify: true,
              cancel: true,
              reject: false,
              approve: false,
              cancelPaid: false,
            }
          ))
        } else if (isApprover >= 0) {
          setActionButtons((prevState: any) => (
            {
              ...prevState,
              modify: false,
              cancel: false,
              reject: true,
              approve: true,
              cancelPaid: true,
            }
          ))
        } else if (isViewer >= 0) {
          setActionButtons((prevState: any) => (
            {
              ...prevState,
              modify: false,
              cancel: false,
              reject: false,
              approve: false,
              cancelPaid: false,
            }
          ))
        }
        let queryParams: any = {
          paymentId: id,
        };
        let promiseArray = [appDispatch(getRequestDetails(queryParams))];
        Promise.all(promiseArray).then((response: any) => {
          if (response[0]) {
            setResponse(response[0]);
            if (response[0]?.walletDetails) {
              setWalletAmounts({
                ...walletAmounts,
                ...response[0]?.walletDetails,
              })
              setFuelDetails({
                ...fuelDetails,
                fuelRate: response[0]?.walletDetails?.fuelRate,
                fuelQuantity: Number(response[0]?.walletDetails?.fuelQuantity || 0),
              })
              setAdBlueDetails({
                ...adBlueDetails,
                adblueRate: response[0]?.walletDetails?.adblueRate,
                adblueQuantity: Number(response[0]?.walletDetails?.adblueQuantity || 0),
              })
              setRemarks(response[0]?.remarks)
              setUtrNumber(response[0]?.razorPayUtrNo)
            } else {
              setResponse({});
              setLoading(false)
            }
            const queryParams: any = {
              order_code: response[0]?.orderCode,
              vehicle_no: response[0]?.vehicleNumber
            }
            let innerPromiseArray = [appDispatch(getTripStatus(queryParams))]
            !isReactiveRepairMaintenenceRequest(response[0]?.maintenanceType) &&
             fetchVehicleDetails(appDispatch, response[0]?.vehicleNumber, setVehicleDetails);
            return Promise.all(innerPromiseArray)
          }
        }).then((innerResponse: any) => {
          if (innerResponse && innerResponse[0] && innerResponse[0].trip_status) {
            setTripStatus(innerResponse[0].trip_status)
          } else {
            setTripStatus(undefined)
          }
          setLoading(false);
        })
      }
    };
    getDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rolesList, state.refreshList]);

  useEffect(() => {
    if (fuelDetails.fuelQuantity && fuelDetails.fuelRate) {
      let tempAmount = Number(fuelDetails.fuelQuantity) ? Number(fuelDetails.fuelRate) * Number(fuelDetails.fuelQuantity) : undefined
      setWalletAmounts({
        ...walletAmounts,
        fuelAmount: Number(tempAmount)
      })
    } else {
      setWalletAmounts({
        ...walletAmounts,
        fuelAmount: 0
      })
    }
    // eslint-disable-next-line
  }, [fuelDetails.fuelQuantity, fuelDetails.fuelRate])

  useEffect(() => {
    if (adBlueDetails.adblueQuantity && adBlueDetails.adblueRate) {
      let tempAmount = Number(adBlueDetails.adblueQuantity) ? Number(adBlueDetails.adblueRate) * Number(adBlueDetails.adblueQuantity) : undefined
      setWalletAmounts({
        ...walletAmounts,
        adblueAmount: Number(tempAmount)
      })
    } else {
      setWalletAmounts({
        ...walletAmounts,
        adblueAmount: 0
      })
    }
    // eslint-disable-next-line
  }, [adBlueDetails.adblueQuantity, adBlueDetails.adblueRate])

  useEffect(() => {
    if (editable) {
      setWalletAmounts({
        ...response?.walletDetails
      })
      setAdBlueDetails({
        ...adBlueDetails,
        adblueAmount: response.walletDetails?.adblueAmount ? response.walletDetails?.adblueAmount : 0,
        adblueQuantity: response.walletDetails?.adblueQuantity ? response.walletDetails?.adblueQuantity : 0,
        adblueRate: response.walletDetails?.adblueRate ? response.walletDetails?.adblueRate : 0
      })
      setFuelDetails({
        ...fuelDetails,
        fuelAmount: response.walletDetails?.fuelAmount ? response.walletDetails?.fuelAmount : 0,
        fuelQuantity: response.walletDetails?.fuelQuantity ? response.walletDetails?.fuelQuantity : 0,
        fuelRate: response.walletDetails?.fuelRate ? response.walletDetails?.fuelRate : 0
      })
      if(response?.walletDetails?.stateForFuel){
        setSelectedState({'label': response?.walletDetails?.stateForFuel, 'value': response?.walletDetails?.stateForFuel})
      }else{
        setSelectedState("");
      }
      setStateFuelRate({
        ...stateFuelRate,
        stateRate: response.walletDetails?.suggestiveFuelRate,
        stateCode: response.walletDetails?.stateForFuel
      })
    }
    // eslint-disable-next-line
  }, [editable])

  useEffect(() => {
    let totalValue = 0;
    const entriesArr = Object.entries(walletAmounts);
    // eslint-disable-next-line
    entriesArr.forEach((item: any) => {
      let result: any = walletAmountsArray.includes(item[0])
      if (result) {
        totalValue += Number(item[1])
      }
    })
    totalValue >= 0 && setTotalRequestedAmount(totalValue.toFixed(2))
  }, [walletAmounts])

  useEffect(() => {
    setPaymentsLoading(true);
    const getPreviousPayments = async () => {
      setPreviousPayments({});
      let queryParams: any = {
        orderCode: response?.orderCode,
        tripCode: response?.tripCode,
        vehicleNumber: response?.vehicleNumber,
      }
      appDispatch(getPreviousPaymentsRequest(queryParams)).then((resp: any) => {
        if (resp) {
          setPreviousPayments(resp);
        }
        setPaymentsLoading(false);
      })
    }
    !isObjectEmpty(response) 
    && (response?.paymentStatus === paymentStatusEnum.PENDING || response?.paymentStatus === paymentStatusEnum.APPROVED)
    && response?.vehicleNumber 
    && !(isNullValue(response?.orderCode) || response?.orderCode === 'NA') && getPreviousPayments();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [response])

  useEffect(()=>{
    if(onCancel){
      setLoading(true);
      let queryParams: any = {
        paymentId: id,
      };
      let promiseArray = [appDispatch(getRequestDetails(queryParams))];
      Promise.all(promiseArray).then((resp: any) => {
        if (resp[0]) {
          if(resp?.[0]?.documentLinks){
            setResponse({...response, documentLinks: resp?.[0]?.documentLinks})
            setLoading(false);
            setOnCancel(false);
          }else{
            setResponse({...response, documentLinks: {}})
            setLoading(false);
            setOnCancel(false);
          }
        }
      })
    }
  },[onCancel])

  useEffect(() => {
    const getWalletBalance = async () => {
      setWalletbalanceLoading(true);
      let queryParams: any = {
        walletName: response?.walletCode,
        vehicleNo: response?.vehicleNumber,
      }
      appDispatch(getCurrentWalletBalance(queryParams)).then((response: any) => {
        if (response) {
          if(response?.walletBalance){
            setCurrentWalletBalance(response?.walletBalance);
          }else{
            setCurrentWalletBalance('NA');
          }
        }else{
          setCurrentWalletBalance('NA');
        }
        setWalletbalanceLoading(false);
      })
    }
    response?.paymentStatus === paymentStatusEnum.PENDING && response?.walletCode && (response?.walletCode === balanceWalletListEnum.BPCL || response?.walletCode === balanceWalletListEnum.HAPPAY) && response?.vehicleNumber && getWalletBalance()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [response?.vehicleNumber])

  useEffect(()=>{
    if(response?.paymentStatus === paymentStatusEnum.PENDING && response?.walletCode && response?.walletCode===walletListEnum.IOCL && response?.vehicleNumber){
      appDispatch(getIoclMobileNumber({ vehicleNumber: response?.vehicleNumber })).then((resp: any) => {
        if (resp?.code === 200) {
          setWalletbalanceLoading(true);
          let queryParams: any = {
            walletName: response?.walletCode,
            vehicleNo: response?.vehicleNumber,
            cardNumber: resp?.details?.cardPan
          }
          appDispatch(getCurrentWalletBalance(queryParams)).then((response: any) => {
            if (response) {
              if(response?.walletBalance){
                setCurrentWalletBalance(response?.walletBalance);
              }else{
                setCurrentWalletBalance('NA');
              }
            }else{
              setCurrentWalletBalance('NA');
            }
          })
        }else{
          setWalletbalanceLoading(false);
          setCurrentWalletBalance('NA');
        }
      })
    }
  },[response?.vehicleNumber])

  useEffect(()=>{
    if(response?.paymentStatus === paymentStatusEnum.PENDING && response?.walletCode && response?.walletCode===walletListEnum.JIOBP && response?.vehicleNumber){
      setWalletbalanceLoading(true);
      appDispatch(getJioBPCardNumber({ vehicleNumber: response?.vehicleNumber })).then((jioCardNumberResponse: any) => {
        if (jioCardNumberResponse?.code === 200 && !isNullValue(jioCardNumberResponse?.details?.data)) {
          appDispatch(getJioBPMobileNumber({ vehicleNumber: response?.vehicleNumber, cardNumber: jioCardNumberResponse?.details?.data?.[0]?.cardNo })).then((jioMobileNumberResponse: any) => {
            if(jioMobileNumberResponse?.code === "200"){
              if (jioMobileNumberResponse?.details) {
                let queryParams: any = {
                  walletName: response?.walletCode,
                  vehicleNo: response?.vehicleNumber,
                  cardNumber: jioCardNumberResponse?.details?.data?.[0]?.cardNo,
                  mobileNumber: jioMobileNumberResponse?.details.mobileNumber
                }
                appDispatch(getCurrentWalletBalance(queryParams)).then((response: any) => {
                  if (response) {
                    if(response?.walletBalance){
                      setCurrentWalletBalance(response?.walletBalance);
                    }else{
                      setCurrentWalletBalance('NA');
                    }
                  }else{
                    setCurrentWalletBalance('NA');
                  }
                  setWalletbalanceLoading(false);
                })
              } else {
                setWalletbalanceLoading(false);
                setCurrentWalletBalance('NA');
              }
            }else{
              setWalletbalanceLoading(false);
              setCurrentWalletBalance('NA');
            }
          })
        } else {
          setWalletbalanceLoading(false);
          setCurrentWalletBalance('NA');
        }
      })
    }
  },[response?.vehicleNumber])

  useEffect(() => {
    setStatesLoading(false);
    if (( response?.walletCode === walletListEnum.BPCL || response?.walletCode === walletListEnum.JIOBP ) && editable) {
      appDispatch(getStateList()).then((resp: any) => {
        let objectList: any = []
        if (resp) {
          resp.map((item: any) => {
            objectList.push({
              "label": item.stateName,
              "value": item.stateCode
            });
          })
          setStateList(objectList);
        } else {
          setStateList([]);
        }
      });
    }
    setStatesLoading(true);
  }, [response.walletCode, editable])

  const stopPollingLoader = () => {
    setPollingLoader(false);
    setCancelPollingLoader(false);
    setLoading(false);
  }

  const fetchPollingData = (params: any) => {
    return appDispatch(orchestrationToken({ orchestrationId: params.orchestrationId })).then((response: any) => {
      return response;
    })
  }

  const stopPollingData = () => {
    setPollingLoader(false);
    if (isGoboltCashReconcileClicked) {
      handleUploadDocumentModalClose();
      history.push(requestListingRoute + requestListingStatusEnum.PAID);
    } else {
      appDispatch(hideDuplicateEntryModal());
      setLoading(false);
      history.push(requestListingRoute)
    }
  }

  const cancelStopPollingData = () => {
    setCancelPollingLoader(false);
    history.push(requestListingRoute + requestListingStatusEnum.PAID)
  }

  const stopPollingDataSubmitOtp = () => {
    setApproveMobileNumber(false);
    setPollingLoader(false);
    dispatch(refreshList());
  }

  const stopPollingDataResolve = () => {
    setResolveAction(false)
    setPollingLoader(false);
    history.push(requestListingRoute + requestListingStatusEnum.UNKNOWN)
  }

  const getUploadDocumentButton = () =>{
    return (
      <Button
        buttonStyle="btn-orange upload-radius-btn"
        title="Upload Document"
        leftIcon={<Publish />}
        loading={response?.repairId && (loading || pollingLoader)}
        onClick={() => {
          let queryParams: any = {
            paymentId: id
          };
          let promiseArray = [appDispatch(getRequestDetails(queryParams))];
          Promise.all(promiseArray).then((response: any) => {
            if (response?.[0]) {
              setUploadDocuments(response?.[0]?.documentLinks)
              setOpenUploadDocumentsModal(true);
            }else{
              setUploadDocuments({});
              setOpenUploadDocumentsModal(true);
            }
          })
        }}
      />
    );
  }

  const isUploadDocumentModalOpen = () => {
    if (editable 
      || isRequestFromMaintenanceModule(response?.repairId)
      || isReactiveRepairMaintenenceRequest(response?.maintenanceType)
      || isPaidGobolCashWalletReconcileRequest(response?.walletCode, response?.paymentStatus, isGoboltCashReconcileClicked)) {
          return openUploadDocumentsModal;
      } else {
        return appReducer?.showUploadDocumentsModal;
      }
  }

  function handleUploadDocumentModalClose() {
    setOpenUploadDocumentsModal(false);
    if (isRequestFromMaintenanceModule(response?.repairId)) {
      dispatch(refreshList());
    } else if (isPaidGobolCashWalletReconcileRequest(response?.walletCode, response?.paymentStatus, isGoboltCashReconcileClicked)) {
      setIsGoboltCashReconcileClicked(false);
      setOnCancel(true);
    } else {
      appDispatch(hideUploadDocumentsModal());
      setOnCancel(true);
    }
  }

  function handleUploadDocumentModalSubmit(setUploadLoading?: Function) {
    if (isPaidGobolCashWalletReconcileRequest(response?.walletCode, response?.paymentStatus, isGoboltCashReconcileClicked)) {
      const queryParams: any = {
        paymentId: response?.paymentId,
        walletCode: response?.walletDetails?.walletCode,
        fromStatus: response?.paymentStatus
      }
      appDispatch(reconcileCamionsWalletRequest(queryParams)).then((response: any) => {
        if (response) {
          setPollingLoader(true);
          appDispatch(pollStart({
            params: { orchestrationId: response.orchestrationId },
            asyncFetch: fetchPollingData,
            stopPollingData: stopPollingData,
            stopPollingLoader: stopPollingLoader,
            stopDocUploading: () => setUploadLoading && setUploadLoading(false),
            requestType: 'ReconcileGoboltCashRequest'
          }));
        } else {
          setUploadLoading && setUploadLoading(false);
        }
      })
    } else {
      !editable && (!response?.repairId && !isReactiveRepairMaintenenceRequest(response?.maintenanceType)) && history.push(requestListingRoute);
      appDispatch(showAlert("Documents Successfully Submitted", true));
    }
  }

  return (
    <div className="request-detail--wrapper">
      

        <DuplicateEntryModal
          open={editable ? duplicateEntryModal.open : appReducer?.showDuplicateEntryModal}
          onClose={() => {
            if(editable){
              setDuplicateEntryModal({open: false, message: ""});
            }else{
              appDispatch(hideDuplicateEntryModal());
            }
          }}
          onApply={() => {
            if(editable){
              setSelectedItem(response)
              const params: any = modifyRequestParams(response, walletAmounts, fuelDetails, adBlueDetails, totalRequestedAmount, remarks, stateFuelRate, utrNumber, confirmMessage);
              setLoading(true)
              appDispatch(modifyRequest(params)).then((response: any) => {
                if (response) {
                  if(response.code === 6){
                    setLoading(false);
                    return;
                  } 
                  response.message && appDispatch(showAlert(response.message, true))
                  dispatch(refreshList());
                  setEditable(false);
                  setDuplicateEntryModal({open: false, message: ""});
                }
                setLoading(false);
              })
            }else{
              if (response?.walletDetails?.walletCode === walletListEnum.BPCL || 
                  response?.walletDetails?.walletCode === walletListEnum.HAPPAY ||
                  response?.walletDetails?.walletCode === walletListEnum.IOCL ||
                  response?.walletDetails?.walletCode === walletListEnum.JIOBP) {
                setSelectedItem(response)
                setRequestAction({
                  ...requestAction,
                  onClickedApprove: true
                })
              } else {
                const params: any = {
                  paymentId: response?.paymentId,
                  walletCode: response?.walletDetails?.walletCode,
                  fromStatus: response?.paymentStatus
                }
                if(!isNullValue(confirmMessage)){
                  params['isDuplicate'] = confirmMessage;
                }
                setLoading(true);
                appDispatch(createApproveRequest(params)).then((response: any) => {
                  if (response) {
                    setPollingLoader(true);
                    appDispatch(pollStart({
                      params: { orchestrationId: response.orchestrationId },
                      asyncFetch: fetchPollingData,
                      stopPollingData: stopPollingData,
                      stopPollingLoader: stopPollingLoader,
                      requestType: 'Approve'
                    }));
                  }
                })
              }
            }
          }}
          alertMessage={editable ? duplicateEntryModal.message : appReducer?.duplicateEntryModalMessage}
          confirmMessage={confirmMessage}
          setConfirmMessage={setConfirmMessage}
          loading={loading || pollingLoader}
          buttonTitle={editable ? "Modify" : "Approve"}
        />

      <RequestActionModal
        actionType={requestAction}
        open={requestAction}
        selectedItem={selectedItem}
        onSuccess={(response: any) => {
          if (requestAction['onClickedApprove'] && selectedItem.walletCode === walletListEnum.HAPPAY) {
            setPollingLoader(true);
            appDispatch(pollStart({
              params: { orchestrationId: response?.orchestrationId },
              asyncFetch: fetchPollingData,
              stopPollingData: stopPollingData,
              stopPollingLoader: stopPollingLoader,
              requestType: 'ApproveHappay'
            }));
          } else if (requestAction['onClickedApprove']) {
            setPollingLoader(true);
            appDispatch(pollStart({
              params: { orchestrationId: response?.orchestrationId },
              asyncFetch: fetchPollingData,
              stopPollingData: stopPollingData,
              stopPollingLoader: stopPollingLoader,
              requestType: 'Approve'
            }));
          }
          if (requestAction['onClickedCancelPaid']) {
            setCancelPollingLoader(true);
            appDispatch(pollStart({
              params: { orchestrationId: response?.orchestrationId },
              asyncFetch: fetchPollingData,
              stopPollingData: cancelStopPollingData,
              stopPollingLoader: stopPollingLoader,
              requestType: 'Cancel'
            }));
          }
          setRequestAction({
            ...requestAction,
            onClickedReject: false,
            onClickedCancel: false,
            onClickedApprove: false,
            onClickedCancelPaid: false
          });
        }}
        onClose={() => {
          setRequestAction({
            ...requestAction,
            onClickedReject: false,
            onClickedCancel: false,
            onClickedApprove: false,
            onClickedCancelPaid: false
          });
        }}
        confirmMessage={confirmMessage}
      />

      <ResolveActionModal
        open={resolveAction}
        onClose={() => {
          setResolveAction(false)
        }}
        onSuccess={(response:any) => {
          setPollingLoader(true);
          appDispatch(pollStart({
            params: { orchestrationId: response?.orchestrationId },
            asyncFetch: fetchPollingData,
            stopPollingData: stopPollingDataResolve,
            stopPollingLoader: stopPollingLoader,
            requestType: 'ResolveAction'
          }));
        }}
        selectedItem={response}
        pollingLoader={pollingLoader}
      />

      <ApproveMobileNumberModal
        open={approveMobileNumber}
        onClose={() => {
          setApproveMobileNumber(false)
        }}
        onSuccess={(queryParams: any) => {
          setPollingLoader(true);
          appDispatch(pollStart({
            params: queryParams,
            asyncFetch: fetchPollingData,
            stopPollingData: stopPollingDataSubmitOtp,
            stopPollingLoader: stopPollingLoader,
            requestType: `${response?.walletDetails?.walletCode}SubmitNumber`
          }));
        }}
        selectedItem={response}
        pollingLoader={pollingLoader}
      />
      {(openUploadDocumentsModal || appReducer?.showUploadDocumentsModal) && (
        <UploadDocumentModal
          open={isUploadDocumentModalOpen()}
          onClose={handleUploadDocumentModalClose}
          onApply={handleUploadDocumentModalSubmit}
          title={"Upload Document"}
          paymentRequestId={(editable || response?.repairId || isPaidGobolCashWalletReconcileRequest(response?.walletCode, response?.paymentStatus, isGoboltCashReconcileClicked) || isReactiveRepairMaintenenceRequest(response?.maintenanceType)) ? response?.id : appReducer?.paymentRequestId}
          uploadDocuments={uploadDocuments}
          setUploadDocuments={setUploadDocuments}
          walletCode={(editable || response?.repairId || isReactiveRepairMaintenenceRequest(response?.maintenanceType) || isPaidGobolCashWalletReconcileRequest(response?.walletCode, response?.paymentStatus, isGoboltCashReconcileClicked)) ? response?.walletCode : appReducer?.walletCode}
          repairId={response?.repairId}
          isUploadMandatory={isPaidGobolCashWalletReconcileRequest(response?.walletCode, response?.paymentStatus, isGoboltCashReconcileClicked) || isReactiveRepairMaintenenceRequest(response?.maintenanceType)}
          isGoboltCashReconcileRequest={isPaidGobolCashWalletReconcileRequest(response?.walletCode, response?.paymentStatus, isGoboltCashReconcileClicked)}
          isReactiveRepairRequest={isReactiveRepairMaintenenceRequest(response?.maintenanceType)}
        />
      )}

      <ViewDocumentModal
        open={openViewDocumentsModal}
        onClose={()=>{
          setOpenViewDocumentsModal(false);
        }}
        fileLinks={documentLinks}
      />

      <div className="filter-wrap">
        <Filter
          pageTitle={
            <>
              <div className="d-flex align-item-center legacy-heading">
                <div className="legacy-currency">
                  <span>₹</span>
                </div>
                <div className="legacy-balance">
                  <span className="legacy-name">{response && getPageLabelName(response)}</span>
                  <p className="m-0 legacy-price">{`₹ ${(totalRequestedAmount) || 0}`}</p>
                </div>
              </div>
            </>
          }
          buttonStyle={isMobile ? "btn-detail-mob" : "btn-detail btn-rounded"}
          buttonTitle={isMobile ? " " : "Back"}
          leftIcon={<KeyboardBackspace />}
          disable={pollingLoader}
          onClick={() => {
            history.goBack();
          }}
        >
        </Filter>
      </div>

      <PageContainer>
        { <>
          {loading ? (
            <CardContentSkeleton
              row={3}
              column={3}
            />
          ) : (
            <>
              {isReactiveRepairMaintenenceRequest(response?.maintenanceType) ? (
                <ReactiveRepairData 
                  setOpenViewDocumentsModal={setOpenViewDocumentsModal} 
                  setDocumentLinks={setDocumentLinks} 
                  data={response?.reactiveRepair} 
                />
              ) : (
                <TripData 
                  showBpclMobileNumber={showBpclMobileNumber} 
                  response={response} 
                  actionButton={actionButton}
                  tripStatus={tripStatus}
                  setApproveMobileNumber={setApproveMobileNumber}
                  setDocumentLinks={setDocumentLinks}
                  setOpenViewDocumentsModal={setOpenViewDocumentsModal}
                />
              )
              }
            </>
          )}
          { loading ? (
              <CardContentSkeleton
                row={3}
                column={3}
              />
              ) :(
                !isReactiveRepairMaintenenceRequest(response?.maintenanceType) && 
                <VehicleDetailsCard vehicleDetails={vehicleDetails} />
              )
          }
          {loading ? (
            <CardContentSkeleton
              row={2}
              column={4}
            />
          ) : (
              (!isReactiveRepairMaintenenceRequest(response?.maintenanceType) &&
                response?.walletCode === walletListEnum.CAMIONS_RAZOR_PAY) && (
                <PayoutDetailsCard 
                  response={response} 
                  status={response?.paymentStatus}
                />
              )
            )
          }
          {
            !isObjectEmpty(response) 
            && !isReactiveRepairMaintenenceRequest(response?.maintenanceType) 
            && (response?.paymentStatus === paymentStatusEnum.PENDING || response?.paymentStatus === paymentStatusEnum.APPROVED ) && (
              <PreviousPaymentsRequests
                paymentsLoading={paymentsLoading}
                previousPayments={previousPayments}
              />
            )
          }

          <Card className="card-wrapper request-listing--shipmentCode">
            {loading ? (
              <CardContentSkeleton
                row={2}
                column={4}
              />
            ) : (
              editable ?
                <>
                  <div className="bpcl-wallet--heading d-block d-md-flex">
                    <div className="d-flex align-item-center">
                      <h6>{response?.walletName ? `${response?.walletName}` : "NA"}</h6>
                      <span className="ml-2 detail-tooltip-table">
                        {
                          (response?.walletCode && response?.walletCode===walletListEnum.OPS_WALLET && !isReactiveRepairMaintenenceRequest(response?.maintenanceType)) && (
                            <CustomTooltipTable 
                              tableColumn={getGoboltTooltipTableColumns()} 
                              tableData={getGoboltTooltipData(response)}
                              showStringValue={true}
                            /> 
                          )
                        }
                      </span>
                    </div>
                    <div className="d-flex align-items-center">
                      <div>
                        {(
                          getUploadDocumentButton()
                        )}
                      </div>
                      <div className="upload-request">
                        {
                          response?.paymentStatus === paymentStatusEnum.PENDING && showBalanceField(response?.walletCode) && (
                            <div className="d-flex align-item-center legacy-heading upload-payment">
                              <div className="legacy-currency">
                                <span><AccountBalanceWalletRounded /></span>
                              </div>
                              <div>
                                <span className="legacy-price">₹ {currentWalletBalance !== "" ? currentWalletBalance : <img src={'/images/Loading.svg'} alt={'Balance Loading'} />}</span>
                              </div>
                            </div>
                          )
                        }
                      </div>
                    </div>
                  </div>
                  <CardContent>
                    <div className="custom-form-row row align-items-end">
                      {showAmountField(response?.walletCode, "tollAmount") && <div className="form-group col-md-3">
                        <NumberEditText
                          label={tollAmountLabel}
                          placeholder={tollAmountLabel}
                          value={walletAmounts.tollAmount}
                          decimalScale={2}
                          maxLength={10}
                          onChange={(text: any) => {
                            setWalletAmounts({
                              ...walletAmounts,
                              tollAmount: text,
                            })
                          }}
                          disabled={isRequestFromMaintenanceModule(response?.repairId)}
                        />
                      </div>}
                      {showAmountField(response?.walletCode, "challanAmount") && <div className="form-group col-md-3">
                        <NumberEditText
                          label={challanAmountLabel}
                          placeholder={challanAmountLabel}
                          value={walletAmounts.challanAmount}
                          decimalScale={2}
                          maxLength={10}
                          onChange={(text: any) => {
                            setWalletAmounts({
                              ...walletAmounts,
                              challanAmount: text
                            })
                          }}
                          disabled={isRequestFromMaintenanceModule(response?.repairId)}
                        />
                      </div>}
                      {showAmountField(response?.walletCode, "loadingAmount") && <div className="form-group col-md-3">
                        <NumberEditText
                          label={loadingAmountLabel}
                          placeholder={loadingAmountLabel}
                          value={walletAmounts.loadingAmount}
                          decimalScale={2}
                          maxLength={10}
                          onChange={(text: any) => {
                            setWalletAmounts({
                              ...walletAmounts,
                              loadingAmount: text
                            })
                          }}
                          disabled={isRequestFromMaintenanceModule(response?.repairId)}
                        />
                      </div>}
                      {showAmountField(response?.walletCode, "unloadingAmount") && <div className="form-group col-md-3">
                        <NumberEditText
                          label={unloadingAmountLabel}
                          placeholder={unloadingAmountLabel}
                          value={walletAmounts.unloadingAmount}
                          decimalScale={2}
                          maxLength={10}
                          onChange={(text: any) => {
                            setWalletAmounts({
                              ...walletAmounts,
                              unloadingAmount: text
                            })
                          }}
                          disabled={isRequestFromMaintenanceModule(response?.repairId)}
                        />
                      </div>}
                      {showAmountField(response?.walletCode, "foodingAmount") && <div className="form-group col-md-3">
                        <NumberEditText
                          label={foodingAmountLabel}
                          placeholder={foodingAmountLabel}
                          decimalScale={2}
                          value={walletAmounts.foodingAmount}
                          maxLength={10}
                          onChange={(text: any) => {
                            setWalletAmounts({
                              ...walletAmounts,
                              foodingAmount: text
                            })
                          }}
                          disabled={isRequestFromMaintenanceModule(response?.repairId)}
                        />
                      </div>}
                      {showAmountField(response?.walletCode, "dallaAmount") && <div className="form-group col-md-3">
                        <NumberEditText
                          label={advanceAmountLabel}
                          placeholder={advanceAmountLabel}
                          value={walletAmounts.dallaAmount}
                          decimalScale={2}
                          maxLength={10}
                          onChange={(text: any) => {
                            setWalletAmounts({
                              ...walletAmounts,
                              dallaAmount: text
                            })
                          }}
                          disabled={isRequestFromMaintenanceModule(response?.repairId)}
                        />
                      </div>}
                      {showAmountField(response?.walletCode, "maintainceAmount") && <div className="form-group col-md-3">
                        <NumberEditText
                          disabled={true}
                          label={maintainceAmountLabel}
                          placeholder={maintainceAmountLabel}
                          value={walletAmounts.maintainceAmount}
                          decimalScale={2}
                          maxLength={10}
                          onChange={(text: any) => {
                            setWalletAmounts({
                              ...walletAmounts,
                              maintainceAmount: text
                            })
                          }}
                        />
                      </div>}
                      {showAmountField(response?.walletCode, "accidentSettlemantAmount") && <div className="form-group col-md-3">
                        <NumberEditText
                          label={accidentSettlemantAmountLabel}
                          placeholder={accidentSettlemantAmountLabel}
                          value={walletAmounts.accidentSettlemantAmount}
                          decimalScale={2}
                          maxLength={10}
                          onChange={(text: any) => {
                            setWalletAmounts({
                              ...walletAmounts,
                              accidentSettlemantAmount: text
                            })
                          }}
                          disabled={isRequestFromMaintenanceModule(response?.repairId)}
                        />
                      </div>}
                      {showAmountField(response?.walletCode, "fuelAmount") && <div className="form-group col-md-3">
                        <NumberEditText
                          label={fuelAmountLabel}
                          placeholder={fuelAmountLabel}
                          value={walletAmounts.fuelAmount}
                          disabled
                          decimalScale={2}
                          maxLength={7}
                          onChange={() => {
                          }}
                        />
                      </div>}
                      {showAmountField(response?.walletCode, "fuelQuantity") && <div className="form-group col-md-3">
                        <NumberEditText
                          label={fuelQuantityLabel}
                          placeholder={fuelQuantityLabel}
                          value={fuelDetails.fuelQuantity}
                          mandatory={(response?.walletCode === walletListEnum.BPCL && response?.walletCode === walletListEnum.JIOBP) ? ((fuelDetails.fuelRate || selectedState) ? true : false) : (fuelDetails.fuelRate ? true : false)}
                          error={error.fuelQuantity}
                          decimalScale={2}
                          maxLength={6}
                          onChange={(text: any) => {
                            setFuelDetails({
                              ...fuelDetails,
                              fuelQuantity: text
                            })
                            setError({})
                          }}
                          disabled={isRequestFromMaintenanceModule(response?.repairId)}
                        />
                      </div>}
                      {showAmountField(response?.walletCode, "stateList") && <div className="form-group col-md-3">
                        <div className="input-wrap">
                          <AutoComplete
                            label={"State for Fuel"}
                            isClearable={true}
                            placeHolder={"Select State"}
                            value={selectedState}
                            error={error.stateList}
                            options={stateList}
                            mandatory={(fuelDetails.fuelQuantity || fuelDetails.fuelRate) ? true : false}
                            onChange={(element: OptionType) => {
                              setSelectedState(element);
                              if(element!=null){
                                let queryParams: any = {
                                  stateCode: element.value,
                                  walletCode: response?.walletCode
                                }
                                setLoading(true);
                                appDispatch(getStateFuelRate(queryParams)).then((response: any) => {
                                  if (response && response.suggestiveFuelRate) {
                                    setStateFuelRate({
                                      ...stateFuelRate,
                                      stateCode: element.label,
                                      stateRate: response.suggestiveFuelRate
                                    })
                                  } else {
                                    setStateFuelRate(null);
                                  }
                                })
                                setLoading(false);
                                setError({});
                              }else{
                                setStateFuelRate({});
                                setError({});
                              }
                            }}
                            isDisabled={isRequestFromMaintenanceModule(response?.repairId)}
                          />
                        </div>
                      </div>}
                      {showAmountField(response?.walletCode, "stateFuelRate") && <div className="form-group col-md-3">
                        <div className="row">
                          <div className="col">
                            <NumberEditText
                              label={fuelRatePerLitreLabel}
                              placeholder={"Suggestive Rates"}
                              mandatory={(fuelDetails.fuelQuantity || stateFuelRate?.stateRate || selectedState) ? true : false}
                              value={stateFuelRate?.stateRate && Numeral(stateFuelRate?.stateRate).format("0,0.00")}
                              decimalScale={2}
                              maxLength={7}
                              disabled
                              onChange={() => {
                              }}
                            />
                          </div>
                          
                          {showAmountField(response?.walletCode, "fuelRate") && <div className="col hide-label">
                            <NumberEditText
                              label={fuelRatePerLitreLabel}
                              placeholder={( response?.walletCode === walletListEnum.BPCL || response?.walletCode === walletListEnum.JIOBP)? "Actual Rate" : fuelRatePerLitreLabel}
                              value={fuelDetails.fuelRate && Numeral(fuelDetails.fuelRate).format("0,0.00")}
                              decimalScale={2}
                              maxLength={6}
                              error={error.fuelRate}
                              onChange={(text: any) => {
                                if (text>=1000) {
                                  setError({
                                    fuelRate : "Value cannot exceed 999.99"
                                  })
                                }
                                else {
                                  setFuelDetails({
                                    ...fuelDetails,
                                    fuelRate: text
                                  })
                                  setError({})
                                }
                              }}
                              disabled={isRequestFromMaintenanceModule(response?.repairId)}
                            />
                          </div>}
                        </div>
                      </div>}
                      {(response?.walletCode !==walletListEnum.BPCL && response?.walletCode !==walletListEnum.JIOBP ) && showAmountField(response?.walletCode, "fuelRate") && <div className="form-group col-md-3">
                        <NumberEditText
                          label={fuelRatePerLitreLabel}
                          placeholder={fuelRatePerLitreLabel}
                          value={fuelDetails.fuelRate && Numeral(fuelDetails.fuelRate).format("0,0.00")}
                          decimalScale={2}
                          maxLength={6}
                          error={error.fuelRate}
                          mandatory={fuelDetails.fuelQuantity ? true : false}
                          onChange={(text: any) => {
                              setFuelDetails({
                                ...fuelDetails,
                                fuelRate: text
                              })
                              setError({})
                          }}
                          disabled={isRequestFromMaintenanceModule(response?.repairId)}
                        />
                      </div>}
                      {showAmountField(response?.walletCode, "adblueAmount") && <div className="form-group col-md-3">
                        <NumberEditText
                          label={adblueAmountLabel}
                          placeholder={adblueAmountLabel}
                          value={walletAmounts.adblueAmount}
                          decimalScale={2}
                          maxLength={7}
                          disabled
                          onChange={() => {
                          }}
                        />
                      </div>}
                      {showAmountField(response?.walletCode, "adblueQuantity") && <div className="form-group col-md-3">
                        <NumberEditText
                          label={adblueQuantityLabel}
                          placeholder={adblueQuantityLabel}
                          value={adBlueDetails.adblueQuantity}
                          mandatory={adBlueDetails.adblueRate ? true : false}
                          error={error.adblueQuantity}
                          decimalScale={2}
                          maxLength={6}
                          onChange={(text: any) => {
                            setAdBlueDetails({
                              ...adBlueDetails,
                              adblueQuantity: text
                            })
                            setError({})
                          }}
                          disabled={isRequestFromMaintenanceModule(response?.repairId)}
                        />
                      </div>}
                      {showAmountField(response?.walletCode, "adblueRate") && <div className="form-group col-md-3">
                        <NumberEditText
                          label={adblueRatePerLitreLabel}
                          placeholder={adblueRatePerLitreLabel}
                          value={adBlueDetails.adblueRate && Numeral(adBlueDetails.adblueRate).format("0,0.00")}
                          mandatory={adBlueDetails.adblueQuantity ? true : false}
                          error={error.adblueRate}
                          decimalScale={2}
                          maxLength={10}
                          onChange={(text: any) => {
                            if (text >= 1000) {
                              setError({
                                adblueRate: "Value cannot exceed 999.99"
                              })
                            }
                            else {
                              setAdBlueDetails({
                                ...adBlueDetails,
                                adblueRate: text
                              })
                              setError({})
                            }
                          }}
                          disabled={isRequestFromMaintenanceModule(response?.repairId)}
                        />
                      </div>}
                      <div className="form-group col-md-3">
                        <EditText
                          label={remarksLabel}
                          placeholder={remarksLabel}
                          value={remarks}
                          maxLength={250}
                          onChange={(text: string) => {
                            setRemarks(
                              text
                            );
                          }}
                        />
                      </div>
                      {response?.walletCode === walletListEnum.CAMIONS_RAZOR_PAY &&
                        <div className="form-group col-md-3">
                          <EditText
                            label={utrNoLabel}
                            placeholder={utrNoLabel}
                            value={utrNumber}
                            maxLength={500}
                            onChange={(text: string) => {
                              setUtrNumber(text);
                            }}
                          />
                        </div>
                      }
                    </div>
                  </CardContent>
                </>
                :
                <>
                    <div className="bpcl-wallet--heading d-block d-md-flex">
                    <div className="d-flex align-item-center">
                      <h6>{getWalletNameInDetails(response)}</h6>
                      <span className="ml-2 detail-tooltip-table">
                        {
                          (response?.walletCode && response?.walletCode===walletListEnum.OPS_WALLET && !isReactiveRepairMaintenenceRequest(response?.maintenanceType)) && (
                            <CustomTooltipTable 
                              tableColumn={getGoboltTooltipTableColumns()} 
                              tableData={getGoboltTooltipData(response)}
                              showStringValue={true}
                            /> 
                          )
                        }
                      </span>
                    </div>
                    <div className="d-flex align-items-center">
                      {(response?.repairId || isReactiveRepairMaintenenceRequest(response?.maintenanceType))&& response?.paymentStatus === paymentStatusEnum.PENDING && (
                        getUploadDocumentButton()
                      )}
                      {response?.paymentStatus === paymentStatusEnum.PENDING && (
                        showBalanceField(response?.walletCode) && (
                          <div className="upload-request">
                            <div className="d-flex align-items-center legacy-heading upload-payment">
                              <div className="legacy-currency">
                                <span><AccountBalanceWalletRounded /></span>
                              </div>
                              <div>
                                <span className="legacy-name">Current Wallet Balance </span>
                                <span className="legacy-price">₹ {currentWalletBalance !== "" ? currentWalletBalance : <img src={'/images/Loading.svg'} alt={'Balance Loading'} />}</span>
                              </div>
                            </div>
                          </div>
                          )
                        )
                      }
                      {showGoboltCashReconcileDocuments(response?.paymentStatus, response?.walletCode, response?.documentLinks) && (
                        <>
                          <span className="mr-3"><h6 style={{fontSize: "16px"}}>Recon Doc -</h6></span>
                          <button
                            style={{"background":'none'}}
                            className={'uploadedDocIcon'}
                            onClick={()=>{
                              setDocumentLinks(response?.documentLinks?.['goboltCashReconcile']);
                              setOpenViewDocumentsModal(true);
                            }}
                          >
                            <img src="/images/InsertPhotoOutlined.svg" alt="InsertPhotoOutlined" />
                            <p>{response?.documentLinks?.['goboltCashReconcile']?.[0]?.totalDocumentCount}</p>
                          </button>
                        </>    
                      )}
                    </div>
                  </div>
                  <CardContent>
                      {isReactiveRepairMaintenenceRequest(response?.maintenanceType) ? (
                        <ReactiveRepairMaintenanceRequestInfo
                          response={response}
                        />
                      ) : (
                        <RequestAmounts
                          response={response}
                          walletCode={response?.walletCode}
                          setDocumentLinks={setDocumentLinks}
                          setOpenViewDocumentsModal={setOpenViewDocumentsModal}
                        />
                      )}
                  </CardContent>
                </>
            )}
          </Card>
          <div className="button-list mb-4 pb-5 mb-md-2 pb-md-2">
            {(!editable && (!response?.repairId || isPaidMaintenanceRequestForHappayOrCamionsRazorPay(response)) && (
                ( actionButton['cancel'] && response?.paymentStatus === paymentStatusEnum.PENDING) || (
                    (response?.paymentStatus === paymentStatusEnum.PAID) && 
                    (
                      response?.walletDetails?.walletCode === walletListEnum.BPCL ||
                      response?.walletDetails?.walletCode === walletListEnum.HAPPAY ||
                      response?.walletDetails?.walletCode === walletListEnum.IOCL ||
                      (response?.walletDetails?.walletCode === walletListEnum.CAMIONS_RAZOR_PAY
                        && response?.payoutInformation?.payoutStatus.toUpperCase() === 'REVERSED') ||
                      response?.walletDetails?.walletCode === walletListEnum.JIOBP
                    ) && actionButton['cancelPaid']
                  ) || (
                    actionButton['goboltCashCanceller'] && response?.paymentStatus === paymentStatusEnum.PAID && 
                    response?.walletDetails?.walletCode === walletListEnum.GOBOLT_CASH
                  )
              )) &&
              <Button
                buttonStyle="btn-grey--cancel btn-action"
                title={"Cancel"}
                disable={loading || cancelPollingLoader || pollingLoader}
                loading={loading || cancelPollingLoader}
                leftIcon={<ClearIcon />}
                onClick={() => {
                  setSelectedItem(response)
                  if (response?.paymentStatus === paymentStatusEnum.PAID) {
                    setRequestAction({
                      ...requestAction,
                      onClickedCancelPaid: true
                    })
                  } else {
                    setRequestAction({
                      ...requestAction,
                      onClickedCancel: true
                    })
                  }
                }}
              />}

            {(
              response?.walletCode===walletListEnum.OPS_WALLET && 
              response?.paymentStatus === paymentStatusEnum.PAID && 
              actionButton['cancelPaid']
            ) && (
              <Button
                buttonStyle="btn-grey--cancel btn-action"
                title={"Cancel"}
                disable={loading || cancelPollingLoader || pollingLoader}
                loading={loading || cancelPollingLoader}
                leftIcon={<ClearIcon />}
                onClick={() => {
                  setSelectedItem(response)
                  setRequestAction({
                    ...requestAction,
                    onClickedCancelPaid: true
                  })
                }}
              />
            )}
            {(!editable && (actionButton['modify'] && response?.paymentStatus === paymentStatusEnum.PENDING) && !isReactiveRepairMaintenenceRequest(response?.maintenanceType)) &&
              <Button
                buttonStyle="btn-orange "
                title={"Modify"}
                disable={loading || pollingLoader}
                leftIcon={<Edit />}
                onClick={() => {
                  setEditable(true)
                }}
              />}
            {(!editable && (actionButton['reject'] && response?.paymentStatus === paymentStatusEnum.PENDING)) &&
              <Button
                buttonStyle="btn-red--reject btn-action"
                title={"Reject"}
                disable={loading || pollingLoader}
                leftIcon={<ClearIcon />}
                onClick={() => {
                  setSelectedItem(response)
                  setRequestAction({
                    ...requestAction,
                    onClickedReject: true
                  })
                }}
              />}
            {(!editable && (actionButton['approve'] && response?.paymentStatus === paymentStatusEnum.PENDING)) &&
              <Button
                buttonStyle="btn-blue "
                title={"Approve"}
                loading={loading || pollingLoader}
                leftIcon={<CheckCircleIcon />}
                onClick={() => {
                  if (response?.walletDetails?.walletCode === walletListEnum.BPCL || response?.walletDetails?.walletCode === walletListEnum.HAPPAY ||
                        response?.walletDetails?.walletCode === walletListEnum.IOCL || response?.walletDetails?.walletCode === walletListEnum.JIOBP ) {
                    setSelectedItem(response)
                    setRequestAction({
                      ...requestAction,
                      onClickedApprove: true
                    })
                  } else {
                    const params: any = {
                      paymentId: response?.paymentId,
                      walletCode: response?.walletDetails?.walletCode,
                      opsWalletEntityId: response?.opsWalletBeneficiaryId,
                      fromStatus: response?.paymentStatus
                    }
                    setLoading(true)
                    appDispatch(createApproveRequest(params)).then((response: any) => {
                      if (response) {
                        setPollingLoader(true);
                        appDispatch(pollStart({
                          params: { orchestrationId: response.orchestrationId },
                          asyncFetch: fetchPollingData,
                          stopPollingData: stopPollingData,
                          stopPollingLoader: stopPollingLoader,
                          requestType: 'Approve'
                        }));
                      }
                      setLoading(false)
                    })
                  }
                }}
                disable={(response?.walletDetails?.walletCode === walletListEnum.BPCL || response?.walletDetails?.walletCode === walletListEnum.IOCL || response?.walletDetails?.walletCode === walletListEnum.JIOBP)
                            && !(showBpclMobileNumber())}
                customView={
                  <InfoTooltip
                    title={<>{response?.walletDetails?.walletCode} new mobile number need to <br /> update for approve payment.</>}
                    placement={"top"}
                    disableInMobile={"false"}
                    infoText={"Approve"}
                  />}
                mobileNumberApproved={(response?.walletDetails?.walletCode === walletListEnum.BPCL ||
                                         response?.walletDetails?.walletCode === walletListEnum.IOCL || response?.walletDetails?.walletCode === walletListEnum.JIOBP) &&
                                      !(showBpclMobileNumber())}
              />}

            {(!editable && (actionButton['unknownResolve'] && response?.paymentStatus === paymentStatusEnum.UNKNOWN)) &&
              <Button
                buttonStyle="btn-blue"
                title={"Resolve"}
                loading={loading || pollingLoader}
                leftIcon={<CheckCircleIcon />}
                onClick={() => {
                  setSelectedItem(response)
                  setResolveAction(true);
                }}
              />}
              {(actionButton?.['goboltCashReconcile'] &&
              response?.paymentStatus === paymentStatusEnum.PAID && 
              response?.walletDetails?.walletCode === walletListEnum.GOBOLT_CASH) && (
                <Button
                  buttonStyle="btn-blue"
                  title={"Reconcile"}
                  loading={loading || pollingLoader || cancelPollingLoader}
                  leftIcon={<CheckCircleIcon />}
                  onClick={() => {
                    setUploadDocuments(response?.documentLinks)
                    setIsGoboltCashReconcileClicked(true);
                    setOpenUploadDocumentsModal(true);
                  }}
                />
              )}

          </div>

          {editable &&
            <>
              <div className="button-list mb-4 pb-5 mb-md-2 pb-md-2">
                <Button
                  buttonStyle="btn-grey--cancel btn-action"
                  title={"Cancel"}
                  loading={loading}
                  leftIcon={<ClearIcon />}
                  onClick={() => {
                    setOnCancel(true);
                    setEditable(false);
                    response?.totalRequestAmount && setTotalRequestedAmount(response?.totalRequestAmount.toFixed(2));
                    setRemarks(response?.remarks);
                    setUtrNumber(response?.razorPayUtrNo)
                  }}
                />
                <Button
                  buttonStyle="btn-blue mr-0"
                  title={"Submit"}
                  loading={loading}
                  leftIcon={<CheckCircleIcon />}
                  disable={Number(totalRequestedAmount) === 0 ? true : false}
                  onClick={() => {
                    const validate = validateData(walletAmounts, fuelDetails, adBlueDetails, response?.walletName)
                    let validateTotals;
                    validate ?
                      (validateTotals = validateTotalAmounts(response?.walletDetails?.walletCode, totalRequestedAmount))
                      : (validateTotals = false);
                    if (validate && validateTotals) {
                      setSelectedItem(response)
                      const params: any = modifyRequestParams(response, walletAmounts, fuelDetails, adBlueDetails, totalRequestedAmount, remarks, stateFuelRate, utrNumber);
                      setLoading(true)
                      appDispatch(modifyRequest(params)).then((response: any) => {
                        if (response) {
                          if(response.code===6){
                            setDuplicateEntryModal({open: true, message: response.message});
                          }else{
                            response.message && appDispatch(showAlert(response.message, true))
                            dispatch(refreshList());
                            setEditable(false);
                          }
                        }
                        setLoading(false);
                      })
                    }
                  }}
                />
              </div>
            </>
          }
        </>}
      </PageContainer>

    </div >
  );

  function validateData(walletAmounts: any, fuelDetails: any, adBlueDetails: any, walletName: any) {
    if (fuelAndAdBlueWalletModify.includes(walletName)) {
      if ((isNullValueOrZero(fuelDetails.fuelRate) && isNullValueOrZero(fuelDetails.fuelQuantity)) && (isNullValueOrZero(adBlueDetails.adblueRate) && isNullValueOrZero(adBlueDetails.adblueQuantity))) {
        return true;
      } else {
        if (isNullValueOrZero(walletAmounts?.fuelAmount)) {
          if (isNullValueOrZero(fuelDetails.fuelRate) && !isNullValueOrZero(fuelDetails.fuelQuantity)) {
            setError({
              fuelRate: "Enter fuel rate"
            });
            return false;
          } else if (!isNullValueOrZero(fuelDetails.fuelRate) && isNullValueOrZero(fuelDetails.fuelQuantity)) {
            setError({
              fuelQuantity: "Enter fuel quantity"
            });
            return false;
          }
        }
        if (isNullValueOrZero(walletAmounts?.adblueAmount)) {
          if (isNullValueOrZero(adBlueDetails.adblueRate) && !isNullValueOrZero(adBlueDetails.adblueQuantity)) {
            setError({
              adblueRate: "Enter adblue rate"
            });
            return false;
          } else if (!isNullValueOrZero(adBlueDetails.adblueRate) && isNullValueOrZero(adBlueDetails.adblueQuantity)) {
            setError({
              adblueQuantity: "Enter adblue quantity"
            });
            return false;
          }
        }
        if (fuelDetails.fuelQuantity > FUEL_QUANTITY_LIMIT) {
          setError({
            fuelQuantity: "Cannot be more than 999.99"
          });
          return false;
        }
        if(walletName==="BPCL Wallet" || walletName==="JioBP Wallet"){
          if(fuelDetails?.fuelRate && !stateFuelRate?.stateRate){
            setError({
              stateList: "Please select the state."
            });
            return false;
          }else if(stateFuelRate?.stateRate && !fuelDetails?.fuelRate){
            setError({
              fuelRate: "Enter fuel rate."
            });
            return false;
          }
          if (checkBPCLVariance(stateFuelRate?.stateRate, fuelDetails?.fuelRate, walletName)) {
            if(walletName==="JioBP Wallet" || walletName==walletListEnum.JIOBP){
              setError({
                fuelRate: "Variance allowed is 0.55%."
              });
            }else{
              setError({
                fuelRate: "Variance allowed is 0.55%."
              });
            }
            return false;
          }
        }else{
          if (fuelDetails.fuelRate > FUEL_RATE_LIMIT) {
            setError({
              fuelRate: "Cannot be more than 150 ₹/Ltr."
            });
            return false;
          }
        }
        if (adBlueDetails.adblueQuantity > ADBLUE_QUANTITY_LIMIT) {
          setError({
            adblueQuantity: "Cannot be more than 101.00"
          });
          return false;
        }
        if (adBlueDetails.adblueRate > ADBLUE_RATE_LIMIT) {
          setError({
            adblueRate: "Cannot be more than 100 ₹/Ltr."
          });
          return false;
        }
        return true;
      }
    } else if (fuelWalletModify.includes(walletName)) {
      if (fuelDetails) {
        if (isNullValueOrZero(fuelDetails.fuelRate) && !isNullValueOrZero(fuelDetails.fuelQuantity)) {
          setError({
            fuelRate: "Enter fuel rate"
          });
          return false;
        } else if (!isNullValueOrZero(fuelDetails.fuelRate) && isNullValueOrZero(fuelDetails.fuelQuantity)) {
          setError({
            fuelQuantity: "Enter fuel quantity"
          });
          return false;
        } else if (fuelDetails.fuelQuantity > FUEL_QUANTITY_LIMIT) {
          setError({
            fuelQuantity: "Cannot be more than 999.99"
          });
          return false;
        } else if (fuelDetails.fuelRate > FUEL_RATE_LIMIT) {
          setError({
            fuelRate: "Cannot be more than 150 ₹/Ltr."
          });
          return false;
        }
        return true;
      }
    } else {
      if (adBlueDetails) {
        if (isNullValueOrZero(adBlueDetails.adblueRate) && !isNullValueOrZero(adBlueDetails.adblueQuantity)) {
          setError({
            adblueRate: "Enter adblue rate"
          });
          return false;
        } else if (!isNullValueOrZero(adBlueDetails.adblueRate) && isNullValueOrZero(adBlueDetails.adblueQuantity)) {
          setError({
            adblueQuantity: "Enter adblue quantity"
          });
          return false;
        } else if (adBlueDetails.adblueQuantity > ADBLUE_QUANTITY_LIMIT) {
          setError({
            adblueQuantity: "Cannot be more than 101.00"
          });
          return false;
        } else if (adBlueDetails.adblueRate > ADBLUE_RATE_LIMIT) {
          setError({
            adblueRate: "Cannot be more than 100 ₹/Ltr."
          });
          return false;
        }
        return true
      }
    }
    return true;
  }

  function showBpclMobileNumber() {
    if (response?.requestedBpclMobileNumber) {
      if (response?.bpclMobileNumberIsApproved) {
        return true;
      } else {
        return false;
      }
    }
    if (response?.requestedIoclMobileNumber) {
      if (response?.ioclMobileNumberIsApproved) {
        return true;
      } else {
        return false;
      }
    }
    if (response?.requestedJiobpMobileNumber) {
      if (response?.jiobpMobileNumberIsApproved) {
        return true;
      } else {
        return false;
      }
    }
    return true;
  }

  function validateTotalAmounts(walletType: any, totalRequestAmount: any) {
    if (walletType && totalRequestAmount !== 0) {
      if (walletType === walletListEnum.MCPL) {
        if (totalRequestAmount > MCPL_TOTAL_REQUEST_LIMIT) {
          appDispatch(showAlert(mcplLimitMessage));
          return false;
        }
      } else if (walletType === walletListEnum.HUB_ADBLUE) {
        if (totalRequestAmount > ADBLUE_TOTAL_REQUEST_LIMIT) {
          appDispatch(showAlert(adBlueLimitMessage));
          return false;
        }
      } else if (walletType === walletListEnum.OM_PETRO_MART_CASH) {
        if (totalRequestAmount > OM_PETRO_MART_CASH_LIMIT) {
          appDispatch(showAlert(omPetroMartCashLimitMessage));
          return false;
        }
      } else if (walletType === walletListEnum.OM_PETRO_MART_DIESEL) {
        if (totalRequestAmount > OM_PETRO_MART_DIESEL_LIMIT) {
          appDispatch(showAlert(omPetroMartDieselLimitMessage));
          return false;
        }
      } else if (walletType === walletListEnum.CONTINENTAL_PETROLEUMS) {
        if (totalRequestAmount > CONTINENTAL_PETROLEUMS_TOTAL_REQUEST_LIMIT) {
          appDispatch(showAlert(continentalPetroleumsMessage));
          return false;
        }
      }
      return true;
    }
    return false;
  }
}
export default RequestDetails;