import { makeSty<PERSON>, Tab, Tabs } from "@material-ui/core";
import { CheckCircle, FilterList } from "@material-ui/icons";
import React, { useEffect, useReducer } from "react";
import { useDispatch } from "react-redux";
import { useHistory, useParams } from "react-router";
import { headerMenuButtons, requestListingStatusArray, rowsPerPageOptions } from "../../base/constant/ArrayList";
import { bulkApproveRequestRoute, requestDetailsRoute, requestListingRoute } from "../../base/constant/RoutePath";
import { useSearchParams } from "../../base/hooks/useSearchParams";
import { requestFilters } from "../../base/moduleUtility/FilterUtils";
import { getAdvanceFilterChips, useQuery } from "../../base/utility/Routerutils";
import { isObjectEmpty } from "../../base/utility/StringUtils";
import { isMobile } from "../../base/utility/ViewUtils";
import Chips from "../../component/chips/Chips";
import FileAction from "../../component/fileAction/FileAction";
import Filter from "../../component/filter/Filter";
import PageContainer from "../../component/pageContainer/PageContainer";
import { TabPanel } from "../../component/tabs/TabPanel";
import Button from "../../component/widgets/button/Button";
import ExpendableCardList from "../../component/widgets/cardlist/ExpendableCardList";
import TableCollapseList from "../../component/widgets/tableView/tableCollapseList/TableCollapseList";
import UploadAlertBox from "../../modals/UploadAlertBox/UploadAlertBox";
import { setHeaderMenu } from "../../redux/actions/AppActions";
import { getRequestList } from "../../serviceActions/RequestServiceAction";
import { requestChildrenTableColumns, requestListingTableColumns } from "../../templates/RequestListingTemplate";
import "./RequestListing.scss";
import RequestListingFilters from "./RequestListingFilters";
import { handleViewDocumentModal, hideLoading, refreshList, setCurrentPage, setResponse, setRowPerPage, setSelectedTab, showLoading, toggleFilter } from "./requestModuleRedux/RequestActions";
import RequestReducer, { REQUEST_MODULE_STATE } from "./requestModuleRedux/RequestReducer";
import { getPaymentTabStatus } from "./requestModuleUtility/RequestUtility";
import ViewDocumentModal from "../../modals/ViewDocumentModal/ViewDocumentModal";
import Axios from "axios";

const useStyles = makeStyles({
  indicator: {
    background: "none",
  },
  tabs: {
    "& button[aria-selected='true']": {
      margin: "0 20px",
      borderBottom: "3px solid #F7931E",
    },
  },
});

function RequestListing() {
  const appDispatch = useDispatch();
  const classes = useStyles();
  const { id } = useParams<any>();
  const params = useQuery();
  const history = useHistory();
  const [state = REQUEST_MODULE_STATE, dispatch] = useReducer(RequestReducer, REQUEST_MODULE_STATE);
  const [filterState, addFiltersQueryParams, removeFiltersQueryParams] = useSearchParams(requestFilters);

  appDispatch(setHeaderMenu(headerMenuButtons[1]));

  useEffect(() => {
    const cancelToken = Axios.CancelToken.source();
    
    const getList = async () => {
      let queryParams: any = {
        page: state.currentPage,
        size: state.pageSize,
        paymentStatus: id ? getPaymentTabStatus(requestListingStatusArray.indexOf(id)) : requestListingStatusArray[0]
      };
      if (!isObjectEmpty(filterState.params)) {
        queryParams = Object.assign(queryParams, filterState.params);
      }
      if (queryParams && queryParams.queryFieldLabel) {
        delete queryParams["queryFieldLabel"];
      }
      dispatch(setSelectedTab(id ? requestListingStatusArray.indexOf(id) : 0));
      dispatch(showLoading());
      try {
        const response = await appDispatch(getRequestList(queryParams, cancelToken));
        if (response && !Axios.isCancel(response)) {
          dispatch(setResponse(response));
        }
      } catch (error) {
        if (!Axios.isCancel(error)) {
          console.error('Error fetching request list:', error);
        }
      } finally {
        // Always hide loading unless the request was cancelled
        if (!cancelToken.token.reason) {
          dispatch(hideLoading());
        }
      }
    };
    getList();

    return () => {
      // Operation canceled due to new request
      if (cancelToken) {
        cancelToken.cancel();
        // Hide loading when component unmounts or dependencies change
        dispatch(hideLoading());
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.refreshList, state.currentPage, state.pageSize, history.location.search, id]);

  return (
    <div className="request-listing--wrapper">
      <RequestListingFilters
        open={state.openFilter}
        filerChips={filterState.chips}
        filerParams={filterState.params}
        paymentStatus={state.selectedTabName}
        onApplyFilter={(filterChips: any, filterParams: any) => {
          dispatch(refreshList());
          dispatch(toggleFilter());
          addFiltersQueryParams(filterChips, filterParams);
        }}
        onClose={() => {
          dispatch(toggleFilter());
        }}
      />

      <div className="filter-wrap">
        {!isMobile && <Filter
          pageTitle={"Request Listing"}
        >
          {!isMobile && <Button
            buttonStyle={"btn-blue "}
            title={isMobile ? " " : "Filter"}
            leftIcon={<FilterList />}
            onClick={() => {
              dispatch(toggleFilter());
            }}
          />}


          {/* <FileAction
            options={[
              {
                menuTitle: "Bulk Approve",
                Icon: CheckCircle,
                onClick: () => {
                  history.push({
                    pathname: bulkApproveRequestRoute,
                  });
                },
              },
            ]}
          /> */}
        </Filter>}
      </div>

      <div className="tab-nav">
        {
          <div className="d-flex filter-tab">
            <div className="filter-mob">
              <Tabs
                value={state.selectedTabIndex}
                className={classes.tabs}
                classes={{ indicator: classes.indicator }}
                onChange={(event: any, newValue: any) => {
                  if (newValue !== state.selectedTabIndex) {
                    dispatch(setSelectedTab(newValue));
                    dispatch(setCurrentPage(1));
                    dispatch(setRowPerPage(rowsPerPageOptions[0]));
                    history.replace({
                      pathname:
                        requestListingRoute + requestListingStatusArray[newValue],
                      search: params.toString(),
                    });
                  }
                }}
                variant="scrollable"
                scrollButtons={isMobile ? "on" : "off"}
              >
                {requestListingStatusArray.map((element, index) => (
                  <Tab key={index} label={element} />
                ))}
              </Tabs>
            </div>
            {isMobile && <div className="mob-icon-list">
              <FileAction
                options={[
                  {
                    menuTitle: "Filter",
                    Icon: FilterList,
                    onClick: () => {
                      dispatch(toggleFilter());
                    },
                    className: "menu-file-top menu-file-outline",
                  },
                  {
                    menuTitle: "Bulk Approve",
                    Icon: CheckCircle,
                    onClick: () => {
                      history.push({
                        pathname: bulkApproveRequestRoute,
                      });
                    },
                    className: "menu-file-bottom menu-file-outline",
                  },
                ]}
              />
            </div>}

          </div>
        }

        <TabPanel value={state.selectedTabIndex} index={state.selectedTabIndex}>
          {pageContent()}
        </TabPanel>
      </div>
    </div>
  );

  function pageContent() {
    return (
      <>
        <UploadAlertBox />
        
        {state.isViewDocumentModalOpen && (
          <ViewDocumentModal
            open={state.isViewDocumentModalOpen}
            onClose={() => {
              dispatch(handleViewDocumentModal(false, []));
            }}
            fileLinks={state.documentLinks}
          />
        )}
        
        <PageContainer loading={state.loading} listData={state.listData}>
          {!isObjectEmpty(getAdvanceFilterChips(filterState.chips)) &&
            Object.keys(getAdvanceFilterChips(filterState.chips)).map(
              (element: any, index: any) => (
                <Chips
                  key={index}
                  label={filterState.chips[element]}
                  onDelete={() => {
                    dispatch(refreshList());
                    if (
                      element === "requestFromDate" ||
                      element === "requestToDate"
                    ) {
                      let secondKey =
                        element === "requestFromDate"
                          ? "requestToDate"
                          : "requestFromDate";
                      let extraMobileKey =
                        element === "query"
                          ? ["queryField", "queryFieldLabel"]
                          : [];
                      removeFiltersQueryParams([
                        element,
                        secondKey,
                        ...extraMobileKey,
                      ]);
                    } else {
                      removeFiltersQueryParams([element]);
                    }
                  }}
                />
              )
            )}
          {
            isMobile ? 
              <ExpendableCardList
                listData={state.listData}
                tableColumns={requestListingTableColumns(onClickDetailsButton, state.selectedTabIndex)}
                isNextPage={state.pagination && state.pagination.next}
                childElementKey={"reactiveRepair"} 
                childTableColumns={requestChildrenTableColumns(dispatch)}
                onReachEnd={() => {
                  dispatch(setCurrentPage(state.pagination.next))
                }}
              />
              :
              <TableCollapseList
                tableColumns={requestListingTableColumns(onClickDetailsButton, state.selectedTabIndex)}
                currentPage={state.currentPage}
                rowsPerPage={state.pageSize}
                rowsPerPageOptions={rowsPerPageOptions}
                listData={state.listData}
                onChangePage={(_event: any, page: number) => {
                  dispatch(setCurrentPage(page))
                }}
                onRowsPerPageChange={(event: any) => {
                  dispatch(setRowPerPage(event.target.value))
                }}
                childElementKey='reactiveRepair' 
                childrenColumns={requestChildrenTableColumns(dispatch)}
                totalCount={state.pagination && state.pagination.count}
              />
          }
        </PageContainer>
      </>
    );
  }

  function onClickDetailsButton(element: any) {
    history.push({
      pathname: requestDetailsRoute + element.paymentId,
      state: { details: element, tabName: state.selectedTabName },
    });
  }
}

export default RequestListing;
