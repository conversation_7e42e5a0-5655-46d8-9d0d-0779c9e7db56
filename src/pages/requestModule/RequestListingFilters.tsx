import { DatePicker } from "@material-ui/pickers";
import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { FilterWalletList, paymentStatusEnum } from "../../base/constant/ArrayList";
import { setAutoCompleteListWithData, setAutoCompleteListWithoutLabelAndValue } from "../../base/moduleUtility/DataUtils";
import { convertDateFormat, convertDateToServerFromDate, convertDateToServerToDate, displayDateFormatter } from "../../base/utility/DateUtils";
import { isFilterNullValue, isNullValue, isObjectEmpty } from "../../base/utility/StringUtils";
import AutoComplete from "../../component/widgets/AutoComplete";
import EditText from "../../component/widgets/EditText";
import NumberEditText from "../../component/widgets/NumberEditText";
import { OptionType } from "../../component/widgets/widgetsInterfaces";
import FilterContainer from "../../modals/FilterModal/FilterContainer";
import { getLocations, getVehicleNumbersList } from "../../serviceActions/RequestServiceAction";

interface RequestListingFiltersProps {
    open: boolean,
    onClose: any,
    onApplyFilter: any
    filerChips: any,
    filerParams: any,
    paymentStatus: string
}

function RequestListingFilters(props: RequestListingFiltersProps) {
    const appDispatch = useDispatch();
    const { open, onClose, onApplyFilter, filerParams, filerChips, paymentStatus } = props;
    const [filterValues, setFilterValues] = React.useState<any | undefined>({});
    const [filterParams, setFilterParams] = React.useState<any | undefined>({});
    const [error, setError] = React.useState<any>({});
    const [isFilterChanged, setIsFilterChanged] = React.useState<boolean>(false);
    const [vehicleNumberList, setVehicleNumberList] = React.useState<Array<OptionType> | undefined>(undefined);
    const [originList, setOriginList] = React.useState<Array<OptionType> | undefined>(undefined);
    const [destinationList, setDestinationList] = React.useState<Array<OptionType> | undefined>(undefined);

    useEffect(() => {
        if (open) {
            setFilterValues(filerChips);
            setFilterParams(filerParams);
            setIsFilterChanged(false);
        }
        // eslint-disable-next-line
    }, [open]);

    useEffect(() => {
        const getData = async () => {
            let promiseArr = [appDispatch(getVehicleNumbersList()), appDispatch(getLocations())]
            Promise.all(promiseArr).then((response: any) => {
                if (response && response[0]) {
                    if (response && response[0]) {
                        setVehicleNumberList(setAutoCompleteListWithoutLabelAndValue(response[0]))
                    } else {
                        setVehicleNumberList(undefined)
                    }
                }
                if (response && response[1]) {
                    if (response && response[1]) {
                        setOriginList(setAutoCompleteListWithData(response[1], "location_name", "location_name"))
                        setDestinationList(setAutoCompleteListWithData(response[1], "location_name", "location_name"))
                    } else {
                        setOriginList(undefined)
                        setDestinationList(undefined)
                    }
                }
            })
        }
        getData();
        // eslint-disable-next-line
    }, [])

    return (
        <FilterContainer
            open={open}
            onClose={() => {
                onClose();
                setError({});
            }}
            onClear={() => {
                setFilterValues({});
                setFilterParams({});
                setError({});
            }}
            onApply={onApply}
        >
            <div className="filter-form-row">
                <div className="form-group">
                    <EditText
                        label={'Order Code'}
                        placeholder={'Enter Order Code'}
                        value={filterValues.orderCode && filterValues.orderCode}
                        error={error.orderCode}
                        maxLength={50}
                        onChange={(text: any) => {
                            setValues({ orderCode: text }, { orderCode: text.trim() });
                        }}
                    />
                </div>

                <div className="form-group">
                    <AutoComplete
                        label={'Vehicle Number'}
                        placeHolder={'Enter Vehicle Number'}
                        value={filterValues.vehicleNumberLabel ? {
                            label: filterValues.vehicleNumberLabel,
                            value: filterParams.vehicleNumber
                        } : undefined}
                        error={error.vehicleNumber}
                        options={vehicleNumberList}
                        onChange={(item: OptionType) => {
                            setValues({ vehicleNumberLabel: item.label }, { vehicleNumber: item.value });
                        }}
                    />
                </div>

                <div className="form-group">
                    <EditText
                        label={'Trip Code'}
                        placeholder={'Enter Trip Code'}
                        value={filterValues.tripCode}
                        error={error.tripCode}
                        maxLength={50}
                        onChange={(text: any) => {
                            setValues({ tripCode: text }, { tripCode: text.trim() });
                        }}
                    />
                </div>

                <div className="form-group">
                    <EditText
                        label={'Payment ID'}
                        placeholder={'Payment ID'}
                        value={filterValues.paymentId}
                        error={error.paymentId}
                        maxLength={50}
                        onChange={(text: any) => {
                            setValues({ paymentId: text }, { paymentId: text.trim() });
                        }}
                    />
                </div>

                {paymentStatus.toUpperCase() !== paymentStatusEnum.APPROVED && (
                    <div className="form-group">
                        <AutoComplete
                            label="Select Wallet"
                            placeHolder="Select Wallet"
                            isClearable
                            value={filterValues.wallet ? {
                                label: filterValues.wallet,
                                value: filterParams.walletCode
                            } : undefined}
                            options={FilterWalletList}
                            onChange={(value: OptionType) => {
                                setValues({ wallet: value?.label }, { walletCode: value?.value })
                            }}
                        />
                    </div>
                )}

                <div className="form-group">
                    <AutoComplete
                        label={'Origin'}
                        placeHolder={'Origin'}
                        value={filterValues.originLabel ? {
                            label: filterValues.originLabel,
                            value: filterParams.originName
                        } : undefined}
                        error={error.origin}
                        options={originList}
                        onChange={(item: OptionType) => {
                            setValues({ originLabel: item.label }, { originName: item.value });
                        }}
                    />
                </div>
                <div className="form-group">
                    <AutoComplete
                        label={'Destination'}
                        placeHolder={'Destination'}
                        value={filterValues.destinationLabel ? {
                            label: filterValues.destinationLabel,
                            value: filterParams.destinationName
                        } : undefined}
                        error={error.desntination}
                        options={destinationList}
                        onChange={(item: OptionType) => {
                            setValues({ destinationLabel: item.label }, { destinationName: item.value });
                        }}
                    />
                </div>
                <div className="form-group">
                    <NumberEditText
                        label={'Mobile Number'}
                        placeholder={'Mobile Number'}
                        value={filterValues.mobileNumber}
                        error={error.mobileNumber}
                        maxLength={10}
                        onChange={(text: any) => {
                            setValues({ mobileNumber: text }, { driverMobileNumber: text });
                        }}
                    />
                </div>

                <div className="form-group">
                    <EditText
                        label={'Request Raised By'}
                        placeholder={'Request Raised By'}
                        value={filterValues.createdBy && filterValues.createdBy}
                        error={error.createdBy}
                        maxLength={50}
                        onChange={(text: any) => {
                            setValues({ createdBy: text }, { createdBy: text.trim() });
                        }}
                    />
                </div>

                <div className="form-group">
                    <div className="input-wrap">
                        <label className="d-flex align-items-center">{"Request Raised From"}</label>
                        <DatePicker
                            className="custom-date-picker"
                            placeholder={'From Date'}
                            hiddenLabel
                            helperText={error.fromDateTime}
                            format={displayDateFormatter}
                            value={filterParams.fromDateTime || null}
                            maxDate={filterParams.toDateTime}
                            onChange={(date: any) => {
                                setValues({ requestFromDate: convertDateFormat(date, displayDateFormatter) }, { fromDateTime: convertDateToServerFromDate(date) });
                            }}
                        />
                    </div>
                </div>
                <div className="form-group">
                    <div className="input-wrap">
                        <label className="d-flex align-items-center">{"Request Raised To"}</label>
                        <DatePicker
                            className="custom-date-picker"
                            placeholder={'To Date'}
                            hiddenLabel
                            helperText={error.toDateTime}
                            format={displayDateFormatter}
                            value={filterParams.toDateTime || null}
                            minDate={filterParams.fromDateTime}
                            onChange={(date: any) => {
                                setValues({ requestToDate: convertDateFormat(date, displayDateFormatter) }, { toDateTime: convertDateToServerToDate(date) });
                            }}
                        />
                    </div>
                </div>
            </div>
        </FilterContainer>
    );

    function setValues(chips: any, params?: any) {
        setFilterValues({
            ...filterValues,
            ...chips
        });
        setError({});
        params && setFilterParams({
            ...filterParams,
            ...params
        });
        setIsFilterChanged(true);
    }

    function onApply() {
        if (!isFilterNullValue(filterValues.orderCode) && isNullValue(filterParams.orderCode)) {
            setError({ orderCode: 'Enter valid Order Code' });
            return;
        }
        if (isNullValue(filterValues.requestFromDate) && !isNullValue(filterValues.requestToDate)) {
            setError({ fromDateTime: "Enter valid from date" });
            return;
        } else if (isNullValue(filterValues.requestToDate) && !isNullValue(filterValues.requestFromDate)) {
            setError({ toDateTime: "Enter valid to date" });
            return;
        }

        if (!isObjectEmpty(filterParams)) {
            if (isFilterChanged) {
                setError({});
                onApplyFilter(filterValues, filterParams);
            } else {
                setError({});
                onClose();
            }
        } else {
            setError({ orderCode: "Enter valid order code" });
        }
    }
}

export default RequestListingFilters;
