import React from 'react';
import { Card, CardContent } from '@material-ui/core';
import Information from '../../../component/information/Information';
import { payoutIdTitle, statusTitle, utrNoLabel, beneficiaryNameTitle, accountNoTitle, ifscTtitle, contactNameTitle } from '../../../base/constant/MessageUtils';
import { InfoTooltip } from '../../../component/widgets/tooltip/InfoTooltip';
import { paymentStatusEnum } from '../../../base/constant/ArrayList';

type PayoutDetailsCardProps = {
  response: any;
  status: string;
};

const PayoutDetailsCard = (props: PayoutDetailsCardProps) => {
  const { response, status } = props;

  return (
    <Card className="card-wrapper request-listing--orderDetails">
      <div className="bpcl-wallet--heading">
        <div className="id-wrap d-md-flex">
          <span className="title-label">Payout Details</span>
        </div>
      </div>
      <CardContent>
        <div className="custom-form-row row">
          {status !== paymentStatusEnum.PENDING && (
            <>
              <div className="col-md-3 card-group col-6">
                <Information
                  title={payoutIdTitle}
                  text={response?.payoutInformation?.payoutId || 'NA'}
                  customView={
                    <InfoTooltip
                      title={response?.payoutInformation?.payoutId || 'NA'}
                      placement={"top"}
                      disableInMobile={"false"}
                      infoText={response?.payoutInformation?.payoutId || 'NA'}
                    />
                  }
                />
              </div>
              <div className="col-md-3 card-group col-6">
                <Information
                  title={statusTitle}
                  text={response?.payoutInformation?.payoutStatus || 'NA'}
                />
              </div>
              <div className="col-md-3 card-group col-6">
                <Information
                  title={utrNoLabel}
                  text={response?.payoutInformation?.utrNo || 'NA'}
                  customView={
                    <InfoTooltip
                      title={response?.payoutInformation?.utrNo || 'NA'}
                      placement={"top"}
                      disableInMobile={"false"}
                      infoText={response?.payoutInformation?.utrNo || 'NA'}
                    />
                  }
                />
              </div>
            </>
          )}
          <div className="col-md-3 card-group col-6">
            <Information
              title={contactNameTitle}
              text={response?.contact?.name || 'NA'}
              customView={
                <InfoTooltip
                  title={response?.contact?.name || 'NA'}
                  placement={"top"}
                  disableInMobile={"false"}
                  infoText={response?.contact?.name || 'NA'}
                />
              }
            />
          </div>
          {response?.vpa ? (
            <>
              <div className="col-md-3 card-group col-6">
                <Information
                  title={beneficiaryNameTitle}
                  text={response?.vpa?.username || 'NA'}
                  customView={
                    <InfoTooltip
                      title={response?.vpa?.username || 'NA'}
                      placement={"top"}
                      disableInMobile={"false"}
                      infoText={response?.vpa?.username || 'NA'}
                    />
                  }
                />
              </div>
              <div className="col-md-3 card-group col-6">
                <Information
                  title={'VPA'}
                  text={response?.vpa?.address || 'NA'}
                  customView={
                    <InfoTooltip
                      title={response?.vpa?.address || 'NA'}
                      placement={"top"}
                      disableInMobile={"false"}
                      infoText={response?.vpa?.address || 'NA'}
                    />
                  }
                />
              </div>
            </>
          ) : (
              <>
                <div className="col-md-3 card-group col-6">
                  <Information
                    title={beneficiaryNameTitle}
                    text={response?.bankAccount?.accountHolderName || 'NA'}
                    customView={
                      <InfoTooltip
                        title={response?.bankAccount?.accountHolderName || 'NA'}
                        placement={"top"}
                        disableInMobile={"false"}
                        infoText={response?.bankAccount?.accountHolderName || 'NA'}
                      />
                    }
                  />

                </div>
                <div className="col-md-3 card-group col-6">
                  <Information
                    title={accountNoTitle}
                    text={response?.bankAccount?.accountNumber || 'NA'}
                    customView={
                      <InfoTooltip
                        title={response?.bankAccount?.accountNumber || 'NA'}
                        placement={"top"}
                        disableInMobile={"false"}
                        infoText={response?.bankAccount?.accountNumber || 'NA'}
                      />
                    }
                  />
                </div>
                <div className="col-md-3 card-group col-6">
                  <Information
                    title={ifscTtitle}
                    text={response?.bankAccount?.ifsc || 'NA'}
                    customView={
                      <InfoTooltip
                        title={response?.bankAccount?.ifsc || 'NA'}
                        placement={"top"}
                        disableInMobile={"false"}
                        infoText={response?.bankAccount?.ifsc || 'NA'}
                      />
                    }
                  />
                </div>
              </>
            )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PayoutDetailsCard;