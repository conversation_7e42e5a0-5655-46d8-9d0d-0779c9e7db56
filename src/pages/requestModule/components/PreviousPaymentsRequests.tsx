import React from 'react';
import CardContentSkeleton from '../../../component/widgets/cardContentSkeleton/CardContentSkeleton';
import { Card, CardContent } from '@material-ui/core';
import TableList from '../../../component/widgets/tableView/TableList';
import DataNotFound from '../../../component/error/DataNotFound';
import { isNullValue, isObjectEmpty } from '../../../base/utility/StringUtils';
import getPastEntryColumns from '../../../templates/RequestAmountTemplate';
import Numeral from 'numeral';

interface PreviousPaymentsRequestsModel{
  paymentsLoading: any,
  previousPayments: any
}

const PreviousPaymentsRequests = (props: PreviousPaymentsRequestsModel) => {
  const {paymentsLoading, previousPayments} = props
  return (
    <>
      {
        (paymentsLoading) ? (
          <CardContentSkeleton
            row={2}
            column={4}
          />
        ) : (
          <>
            {
              !isObjectEmpty(previousPayments?.paidReconciledDetails) ? (
                <div className="bpcl-wallet bpcl-vehicle">
                  <Card className="card-wrapper">
                    <div className="bpcl-wallet--heading d-block d-md-flex">
                      <h6>{`Previous Payment Requests`}</h6>
                    </div>
                    <CardContent>
                      <div className="table-detail-listing inp-tableList scroll-table">
                        <TableList
                          tableColumns={getPastEntryColumns()}
                          currentPage={0}
                          rowsPerPage={25}
                          rowsPerPageOptions={[]}
                          listData={previousPayments?.paidReconciledDetails}
                          onChangePage={() => { }}
                          onRowsPerPageChange={() => { }}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card className="card-wrapper">
                  <div className="bpcl-wallet--heading d-block d-md-flex">
                    <h6>{`Previous Payment Requests`}</h6>
                  </div>
                  <CardContent>
                    <DataNotFound
                      imageId={"payment-not-found"}
                      image={"/images/info_icon.svg"}
                      customWarning={"No Data Available"}
                      message={"No previous payment records corresponding to the vehicle"}
                    />
                  </CardContent>
                </Card>
              )
            }
          </>
        )
      }
      {
        <>
          {
            (paymentsLoading) ? (
              <CardContentSkeleton
                row={3}
                column={3}
              />
            ) : (
              <>
                {
                  !isObjectEmpty(previousPayments?.invalidPendingDetails) && (
                    <div className="bpcl-wallet bpcl-vehicle">
                      <Card className="card-wrapper">
                        <div className="bpcl-wallet--heading">
                          <h6 className="bpcl-wallet--heading-left">{`Invalid Pending Payment Requests`}</h6>
                          <div className="d-flex align-item-center legacy-heading upload-payment">
                            <div>
                              <span className="legacy-name">Total Amount</span>
                              <span className="legacy-price">
                                ₹ {!isNullValue(previousPayments?.totalInvalidPendingAmount) ? Numeral(previousPayments.totalInvalidPendingAmount).format("0,0.00") : "N/A"}
                              </span>
                            </div>
                          </div>
                        </div>
                        <CardContent>
                          <div className="table-detail-listing inp-tableList scroll-table">
                            <TableList
                              tableColumns={getPastEntryColumns()}
                              currentPage={0}
                              rowsPerPage={25}
                              rowsPerPageOptions={[]}
                              listData={previousPayments?.invalidPendingDetails}
                              onChangePage={() => { }}
                              onRowsPerPageChange={() => { }}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  )
                }
              </>
            )
          }
        </>
      }
    </>
  )
}

export default PreviousPaymentsRequests