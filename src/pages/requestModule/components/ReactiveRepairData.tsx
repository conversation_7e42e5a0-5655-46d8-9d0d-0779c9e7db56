import React, { useState } from 'react'
import { Card, CardContent } from '@material-ui/core'
import TableList from '../../../component/widgets/tableView/TableList'
import repairRequestTableColumns from '../../../templates/RepairRequestTemplate'
import { VehicleDetailsModal } from './VehicleDetailsModal'

interface ReactiveRepairDataModel {
  setDocumentLinks: Function,
  setOpenViewDocumentsModal: Function,
  data: any
}

interface VehicleDetailsModalDataModel {
  open: boolean,
  vehicleNumber: string
}

const initVehicleDetailsModalData: VehicleDetailsModalDataModel = {
  open: false,
  vehicleNumber: ''
}

const ReactiveRepairData = (props: ReactiveRepairDataModel) => {
  const { setDocumentLinks, setOpenViewDocumentsModal, data } = props;
  const [vehicleDetailsModalData, setVehicleDetailsModalData] = useState<VehicleDetailsModalDataModel>({ ...initVehicleDetailsModalData });

  const closeVehicleDetailsModal = () => {
    setVehicleDetailsModalData({...initVehicleDetailsModalData})
  }

  return (
    <>
      {
        vehicleDetailsModalData.open && (
          <VehicleDetailsModal 
            open={vehicleDetailsModalData.open}
            vehicleNumber={vehicleDetailsModalData.vehicleNumber}
            onClose={closeVehicleDetailsModal}
          />
        ) 
      }
      <div className="bpcl-vehicle">
        <Card className="card-wrapper">
          <div className="bpcl-wallet--heading d-block d-md-flex">
            <h6>{`Repair Requests`}</h6>
          </div>
          <CardContent>
            <div className="table-detail-listing">
              <TableList
                tableColumns={repairRequestTableColumns(setDocumentLinks, setOpenViewDocumentsModal, setVehicleDetailsModalData)}
                currentPage={0}
                rowsPerPage={25}
                rowsPerPageOptions={[]}
                listData={data}
                onChangePage={() => { }}
                onRowsPerPageChange={() => { }}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  )
}

export default ReactiveRepairData