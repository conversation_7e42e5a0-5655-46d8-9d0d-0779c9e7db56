import React from 'react'
import { Card, CardContent } from '@material-ui/core'
import { isEmptyArray, isNullValue, isObjectEmpty } from '../../../base/utility/StringUtils'
import Information from '../../../component/information/Information'
import { InfoTooltip } from '../../../component/widgets/tooltip/InfoTooltip'
import Button from '../../../component/widgets/button/Button'
import { convertDateFormat, displayDateTimeFormatter } from '../../../base/utility/DateUtils'
import DataNotFound from '../../../component/error/DataNotFound'
import { approvedAtLabel, approvedByLabel, destinationLabel, driverMobileNumberLabel, driverNameLabel, gbCodeLabel, orderCodeLabel, originLabel, raisedAtLabel, raisedByLabel, reconciledAtLabel, reconciledByLabel, tripCodeLabel, tripStatusLabel } from '../../../base/constant/MessageUtils'
import { paymentStatusEnum, walletListEnum } from '../../../base/constant/ArrayList'
import { Visibility } from '@material-ui/icons'
import { useDispatch } from 'react-redux'
import { getDriverLicense } from '../../../serviceActions/RequestServiceAction'
import { getDriverDocLinks } from '../requestModuleUtility/RequestUtility'
import { showAlert } from '../../../redux/actions/AppActions'

interface TripDataPropsModel {
  setApproveMobileNumber: Function,
  showBpclMobileNumber: Function,
  actionButton: any,
  tripStatus: any,
  response: any,
  setDocumentLinks: Function,
  setOpenViewDocumentsModal: Function
}

const TripData = (props: TripDataPropsModel) => {
  const appDispatch = useDispatch();
  const { setApproveMobileNumber, showBpclMobileNumber, actionButton, tripStatus, response, setDocumentLinks, setOpenViewDocumentsModal } = props;
  return (
    <Card className="card-wrapper request-listing--orderDetails">
      {!isObjectEmpty(response) ? (
        <>
          <div className="bpcl-wallet--heading">
            <div className="id-wrap d-md-flex">
              <span className="title-label">Vehicle Number :</span>
              <div className="title-value">{response?.vehicleNumber}</div>
            </div>
            {!isNullValue(response?.repairId) &&
              <div className="id-wrap d-md-flex">
                <span className="title-label" style={{ opacity: 0.5 }}>Repair Id :</span>
                <div className="title-value">{response.repairId}</div>
              </div>
            }
            {!isNullValue(response?.fuelRequestId) &&
              <div className="id-wrap d-md-flex">
                <span className="title-label">Request ID :</span>
                <div className="title-value">{response.fuelRequestId}</div>
              </div>
            }
          </div>
          <CardContent>
            <div className="custom-form-row row">
              <div className="col-md-3 card-group col-6">
                <Information
                  title={tripCodeLabel}
                  text={response.tripCode}
                />
              </div>
              <div className="col-md-3 card-group col-6">
                <Information
                  title={orderCodeLabel}
                  text={response.orderCode}
                />
              </div>
              <div className="col-md-3 card-group col-6">
                <Information
                  title={originLabel}
                  text={response.originName}
                  customView={
                    <InfoTooltip
                      title={response.originName || "....."}
                      placement={"top"}
                      disableInMobile={"false"}
                      infoText={response.originName || "....."}
                    />
                  }
                />
              </div>
              <div className="col-md-3 card-group col-6">
                <Information
                  title={destinationLabel}
                  text={response.destinationName}
                  customView={
                    <InfoTooltip
                      title={response.destinationName || "....."}
                      placement={"top"}
                      disableInMobile={"false"}
                      infoText={response.destinationName || "....."}
                    />
                  }
                />
              </div>
              <div className="col-md-3 card-group col-6">
                <Information
                  title={driverNameLabel}
                  text={response.driverName}
                  customView={
                    <InfoTooltip
                      title={response.driverName || "....."}
                      placement={"top"}
                      disableInMobile={"false"}
                      infoText={response.driverName || "....."}
                    />
                  }
                />
              </div>
              <div className="col-md-3 card-group col-6 align-items-end d-flex">
                <Information
                  className="mr-2"
                  title={gbCodeLabel}
                  text={response.driverCode}
                />
                <Button
                  leftIcon={<Visibility />}
                  title="View"
                  buttonStyle="btn-outline-orange btn-small btn-square"
                  onClick={()=>{
                    const getDriverLicenseDetails = () => {
                      const getDriverDocsParams = {
                        driver_code: response?.driverCode
                      }
                      setDocumentLinks([]);
                      appDispatch(getDriverLicense(getDriverDocsParams)).then((resp: any) => {
                        let errorMsg = '';
                        if (resp && resp.code.status === 200) {
                          const docLinks = getDriverDocLinks(resp);
                          if(!isEmptyArray(docLinks)){
                            setDocumentLinks(docLinks);
                            setOpenViewDocumentsModal(true);
                          }else{
                            errorMsg = 'No Document Found';
                          }
                        }else{
                          errorMsg = resp?.code?.message;
                        }
                        !isNullValue(errorMsg) && appDispatch(showAlert(errorMsg));
                      })
                    }
                    !isNullValue(response?.driverCode) && getDriverLicenseDetails()
                  }}
                />
              </div>
              {showBpclMobileNumber() && response?.walletDetails?.walletCode === walletListEnum.BPCL &&
                <div className="col-md-3 card-group col-6">
                  <Information
                    title={"BPCL Mobile Number"}
                    text={response.bpclMobileNumber}
                  />
                </div>
              }
              {showBpclMobileNumber() && response?.walletDetails?.walletCode === walletListEnum.IOCL &&
                <div className="col-md-3 card-group col-6">
                  <Information
                    title={"IOCL Mobile Number"}
                    text={response.ioclMobileNumber}
                  />
                </div>
              }
              {showBpclMobileNumber() && response?.walletDetails?.walletCode === walletListEnum.JIOBP &&
                <div className="col-md-3 card-group col-6">
                  <Information
                    title={"JioBP Mobile Number"}
                    text={response.jiobpMobileNumber}
                  />
                </div>
              }
              {response?.walletDetails?.walletCode === walletListEnum.BPCL && !(showBpclMobileNumber()) &&
                <>
                  <div className="col-md-3 card-group col-6">
                    <Information
                      title={"BPCL Mobile Number(Old)"}
                      text={response.bpclMobileNumber}
                    />
                  </div>
                  <div className="col-md-3 card-group col-6">
                    <Information
                      title={
                        <>
                          BPCL Mobile Number(New)
                          {actionButton['approve'] && response?.paymentStatus === paymentStatusEnum.PENDING &&
                            <Button
                              buttonStyle="btn btn--approve"
                              title="Approve"
                              onClick={() => {
                                setApproveMobileNumber(true)
                              }}
                            />
                          }
                        </>
                      }
                      text={response?.requestedBpclMobileNumber}
                    />
                  </div>
                </>}
              {response?.walletDetails?.walletCode === walletListEnum.IOCL && !(showBpclMobileNumber()) &&
                <>
                  <div className="col-md-3 card-group col-6">
                    <Information
                      title={"IOCL Mobile Number(Old)"}
                      text={response.ioclMobileNumber}
                    />
                  </div>
                  <div className="col-md-3 card-group col-6">
                    <Information
                      title={
                        <>
                          IOCL Mobile Number(New)
                          {actionButton['approve'] && response?.paymentStatus === paymentStatusEnum.PENDING &&
                            <Button
                              buttonStyle="btn btn--approve"
                              title="Approve"
                              onClick={() => {
                                setApproveMobileNumber(true)
                              }}
                            />
                          }
                        </>
                      }
                      text={response?.requestedIoclMobileNumber}
                    />
                  </div>
                </>}
              {response?.walletDetails?.walletCode === walletListEnum.JIOBP && !(showBpclMobileNumber()) &&
                <>
                  <div className="col-md-3 card-group col-6">
                    <Information
                      title={"JioBP Mobile Number(Old)"}
                      text={response.jiobpMobileNumber}
                    />
                  </div>
                  <div className="col-md-3 card-group col-6">
                    <Information
                      title={
                        <>
                          JioBP Mobile Number(New)
                          {actionButton['approve'] && response?.paymentStatus === paymentStatusEnum.PENDING &&
                            <Button
                              buttonStyle="btn btn--approve"
                              title="Approve"
                              onClick={() => {
                                setApproveMobileNumber(true)
                              }}
                            />
                          }
                        </>
                      }
                      text={response?.requestedJiobpMobileNumber}
                    />
                  </div>
                </>
              }
              <div className="col-md-3 card-group col-6">
                <Information
                  title={driverMobileNumberLabel}
                  text={response.driverMobileNumber}
                />
              </div>
              <div className="col-md-3 card-group col-6">
                <Information
                  title={raisedByLabel}
                  text={response.createdBy}
                  customView={
                    <InfoTooltip
                      title={response.createdBy || "....."}
                      placement={"top"}
                      disableInMobile={"false"}
                      infoText={response.createdBy || "....."}
                    />
                  }
                />
              </div>
              <div className="col-md-3 card-group col-6">
                <Information
                  title={raisedAtLabel}
                  text={response.createdAt && convertDateFormat(response.createdAt, displayDateTimeFormatter)}
                />
              </div>
              {(!isNullValue(response?.approvedBy) && !(response?.paymentStatus === paymentStatusEnum.PENDING || response?.paymentStatus === paymentStatusEnum.REJECTED)) && <div className="col-md-3 card-group col-6">
                <Information
                  title={approvedByLabel}
                  text={response.approvedBy}
                />
              </div>}
              {(!isNullValue(response?.approvedAt) && !(response?.paymentStatus === paymentStatusEnum.PENDING || response?.paymentStatus === paymentStatusEnum.REJECTED)) && <div className="col-md-3 card-group col-6">
                <Information
                  title={approvedAtLabel}
                  text={response.approvedAt && convertDateFormat(response.approvedAt, displayDateTimeFormatter)}
                />
              </div>}
              {response?.paymentStatus === paymentStatusEnum.RECONCILED && (
                <>
                  <div className="col-md-3 card-group col-6">
                    <Information
                      title={reconciledByLabel}
                      text={response.reconciledBy || "NA"}
                      customView={
                        <InfoTooltip
                          title={response.reconciledBy || "NA"}
                          placement={"top"}
                          disableInMobile={"false"}
                          infoText={response.reconciledBy || "NA"}
                        />
                      }
                    />
                  </div>
                  <div className="col-md-3 card-group col-6">
                    <Information
                      title={reconciledAtLabel}
                      text={response.reconciledAt ? convertDateFormat(response.reconciledAt, displayDateTimeFormatter) : "NA"}
                    />
                  </div>
                </>
              )}
              <div className="col-md-3 card-group col-6">
                <Information
                  title={tripStatusLabel}
                  valueClassName={"orange-text"}
                  text={tripStatus}
                />
              </div>
            </div>
          </CardContent>
        </>
      ) : (
        <DataNotFound />
      )}
    </Card>
  )
}

export default TripData