import React from 'react'
import { Card, CardContent } from '@material-ui/core'
import Information from '../../../component/information/Information'
import DataNotFound from '../../../component/error/DataNotFound'
import { isNullValue, isObjectEmpty } from '../../../base/utility/StringUtils'
import { getVehicleDetailsMapper } from '../../../templates/VehicleDetailsCardTemplate'
import { OverflowTip } from '../../../component/widgets/tooltip/OverFlowToolTip'

interface VehicleDetailsCardProps {
  vehicleDetails: any
}

const VehicleDetailsCard = (props: VehicleDetailsCardProps) => {
  const { vehicleDetails } = props;
  const vehicleDetailsMapper = getVehicleDetailsMapper();
  return (
    <Card className="card-wrapper request-listing--orderDetails">
      <div className="bpcl-wallet--heading">
        <div className="id-wrap d-md-flex">
          <span className="title-label">Vehicle Details</span>
        </div>
      </div>
      <CardContent>
        <div className="row">
          {!isObjectEmpty(vehicleDetails) ? (
            <>
              {
                vehicleDetailsMapper.map((item, index) => (
                  <>
                    {item.customView ? (
                      item.customView(vehicleDetails)
                    ) : (
                      <Information
                        title={item.title}
                        customView={
                          <OverflowTip 
                            text={`${isNullValue(vehicleDetails[item.key]) ? 'NA' : vehicleDetails[item.key]}`}
                          />
                        }
                        className="col-6 col-md-3 mb-3"
                      />
                    )}
                  </>
                ))
              }
            </>
          ) : <DataNotFound />}
        </div>
      </CardContent>
    </Card>
  )
}

export default VehicleDetailsCard;