import React, { useEffect, useState } from 'react'
import ModalContainer from '../../../modals/ModalContainer';
import { vehicleDetailsTitle } from '../../../base/constant/MessageUtils';
import { useDispatch } from 'react-redux';
import { fetchVehicleDetails } from '../requestModuleUtility/RequestUtility';
import CardContentSkeleton from '../../../component/widgets/cardContentSkeleton/CardContentSkeleton';
import Information from '../../../component/information/Information';
import DataNotFound from '../../../component/error/DataNotFound';
import { isNullValue, isObjectEmpty } from '../../../base/utility/StringUtils';
import { getVehicleDetailsMapper } from '../../../templates/VehicleDetailsCardTemplate';
import { OverflowTip } from '../../../component/widgets/tooltip/OverFlowToolTip';

interface VehicleDetailsModalProps {
  open: boolean,
  vehicleNumber: string,
  onClose: () => void
}

const customViewClasses = {
  registration_date: 'col-md-6 pb-3 mt-3 mb-md-0 border-bottom',
  unladen_weight: 'col-md-6 pb-3 mt-3 mb-md-0'
}

export const VehicleDetailsModal = (props: VehicleDetailsModalProps) => {
  const { open, vehicleNumber, onClose } = props;
  const [vehicleDetails, setVehicleDetails] = useState<any>({});
  const [loading, setLoading] = useState<any>(false);
  const appDispatch = useDispatch();
  const vehicleDetailsMapper = getVehicleDetailsMapper(customViewClasses);

  useEffect(() => {
    if (open) {
      setLoading(true);
      fetchVehicleDetails(appDispatch, vehicleNumber, setVehicleDetails).then(() => {
        setLoading(false);
      });
    }
  }, [open])

  return (
    <ModalContainer
      open={open}
      title={vehicleDetailsTitle}
      onClose={onClose}
    >
      {
        loading ? (
          <CardContentSkeleton
            row={4}
            column={2}
          />
        ) : (
          <div className="container">
            <div className="row">
              {!isObjectEmpty(vehicleDetails) ? (
                <>
                  {
                    vehicleDetailsMapper.map((item, index) => (
                      <>
                        {item.customView ? (
                          item.customView(vehicleDetails)
                        ) : (
                          <Information
                            title={item.title}
                            customView={
                              <OverflowTip 
                                text={`${isNullValue(vehicleDetails[item.key]) ? 'NA' : vehicleDetails[item.key]}`}
                              />
                            }
                            className="col-md-6 pb-3 mt-3 mb-md-0 border-bottom"
                          />
                        )}
                      </>
                    ))
                  }
                </>
              ) : <DataNotFound />}
            </div>
          </div>
        )
      }
    </ModalContainer>
  )
}
