import { ArrowRightAlt } from "@material-ui/icons";
import React from "react";
import { useDispatch } from "react-redux";
import { newMobileNumberLabel, oldMobileNumberLabel } from "../../../base/constant/MessageUtils";
import { isNullValue } from "../../../base/utility/StringUtils";
import EditText from "../../../component/widgets/EditText";
import NumberEditText from "../../../component/widgets/NumberEditText";
import ModalContainer from "../../../modals/ModalContainer";
import { showAlert } from "../../../redux/actions/AppActions";
import "./UpdateMobileNumberModal.scss";

interface UpdateMobileNumberModalProps {
    open: boolean
    title: string
    onClose: any
    mobileNumberDetails?: any,
    setMobileNumberDetails: Function,
    setUpdateMobileNumber: Function,
    newMobileNumber: number,
}

function UpdateMobileNumberModal(props: UpdateMobileNumberModalProps) {
    const appDispatch = useDispatch();
    const { open, title, onClose, mobileNumberDetails, setMobileNumberDetails, setUpdateMobileNumber, newMobileNumber} = props;
    const [userParams, setUserParams] = React.useState<any>({});
    const [error, setError] = React.useState<any>({});
    // const [loading, setLoading] = React.useState<boolean>(false);

    return (
        <ModalContainer
            title={title}
            styleName={"update-mobileNumber-modal"}
            secondaryButtonTitle={"Submit"}
            secondaryButtonStyle={"btn-blue"}
            open={open}
            // loading={loading}
            onApply={() => {
            }}
            secondaryButtonLeftIcon={<ArrowRightAlt />}
            onClose={() => {
                clearData();
                onClose();
            }}
            onClear={() => {
                if (validateData()) {
                    setMobileNumberDetails({
                      ...mobileNumberDetails,
                    newMobileNumber: newMobileNumber});
                    setUpdateMobileNumber(false);
                    appDispatch(showAlert("Your Request Successfully Submitted", true))
                    setUserParams({})
                }
            }}
        >

            <div className="custom-form-row row driver-number">
                <div className="form-group col-12 label-down">
                    <EditText
                        label={oldMobileNumberLabel}
                        placeholder={oldMobileNumberLabel}
                        disabled
                        maxLength={10}
                        value={mobileNumberDetails.mobileNumber}
                        onChange={(text: any) => {
                        }}
                    />
                </div>

                <div className="form-group col-12 label-down">
                    <NumberEditText
                        label={newMobileNumberLabel}
                        placeholder={newMobileNumberLabel}
                        mandatory
                        maxLength={10}
                        error={error.newMobileNumber}
                        value={newMobileNumber}
                        disabled={true}
                        onChange={(text: any) => {
                            // setError({});
                            // setUserParams({
                            //     ...userParams,
                            //     newMobileNumber: text
                            // });
                        }}
                    />
                </div>
            </div>
        </ModalContainer >
    );

    function clearData() {
        setUserParams({});
        setError({});
    }

    function validateData() {
        if (userParams) {
            if (isNullValue(newMobileNumber)) {
                setError({
                    newMobileNumber: "Enter valid Mobile Number"
                })
                return false;
            } else if (mobileNumberDetails.mobileNumber === newMobileNumber){
                setError({
                    newMobileNumber: "New Mobile Number should be different"
                })
                return false;
            }
            return true;
        }
        return false;
    }
}

export default UpdateMobileNumberModal;
