.update-mobileNumber-modal {
  .MuiDialog-paper {
    min-width: 560px;
    max-width: 560px;
    position: relative;
    @media screen and (max-width: 767px) {
      min-width: 100%;
    }
  }

  .MuiDialogContent-root {
    padding-top: 4px;
  }

  .MuiDialogActions-root {
    justify-content: flex-end;
    @media screen and (max-width: 767px) {
      text-align: left;
    }
  }
  .label-down {
    .input-wrap {
      .d-flex {
        margin-bottom: 5px;
        label {
          margin-bottom: 0;
        }
      }
    }
  }
  .driver-number {
    margin-top: 25px;
  }
  .otp--wrapper {
    .MuiFormControl-root {
      position: relative;
    }
    & > div:last-child {
      position: initial;
      width: 500px;
      background-color: red;
      height: 10px;
    }
  }
  .approve--btn {
    position: absolute;
    bottom: 3px;
    width: 100px;
    box-shadow: 0px 2px 3px #0000001a;
    border-radius: 4px;
    right: 18px;
    background: #e48c3b;
  }
  .resend-otp--wrapper {
    position: absolute;
    bottom: 30px;
    left: 25px;
  }
  .resend-otp--btn {
    color: #f7931e;
    text-decoration: underline;
    cursor: pointer;
  }
  .resend-otp--text {
    span {
      color: #f7931e;
    }
  }
}
