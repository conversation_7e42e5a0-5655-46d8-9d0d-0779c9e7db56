import { ArrowRightAlt } from "@material-ui/icons";
import React from "react";
import { useDispatch } from "react-redux";
import { isNullValue } from "../../../../base/utility/StringUtils";
import Button from "../../../../component/widgets/button/Button";
import EditText from "../../../../component/widgets/EditText";
import NumberEditText from "../../../../component/widgets/NumberEditText";
import ModalContainer from "../../../../modals/ModalContainer";
import { getBpclOtp, updateBpclMobileNumber, updateIoclMobileNumber, updateJioBPMobileNumber } from "../../../../serviceActions/RequestServiceAction";
import { walletListEnum } from "../../../../base/constant/ArrayList";

import "./ApproveMobileNumberModal.scss";

interface ApproveMobileNumberModalProps {
    open: boolean
    onClose: any
    onSuccess: any,
    selectedItem?: any,
    pollingLoader?: boolean,
}

function ApproveMobileNumberModal(props: ApproveMobileNumberModalProps) {
    const appDispatch = useDispatch();
    const { open, onClose, onSuccess, selectedItem, pollingLoader } = props;
    const [userParams, setUserParams] = React.useState<any>({});
    const [loading, setLoading] = React.useState<boolean>(false);
    const [error, setError] = React.useState<any>({});
    const [counter, setCounter] = React.useState<number>(60);
    const [showTimer, setShowTimer] = React.useState<boolean>(false);
    const [showOtpButton, setShowOtpButton] = React.useState<boolean>(false);
    const [intervalId, setIntervalId] = React.useState<any>(null)

    const timer = () => {
        let time: number = 60;
        setCounter(60);
        setShowOtpButton(true);

        if (intervalId) {
            clearInterval(intervalId);
        }

        let id = setInterval(setTimer, 1000);

        function setTimer() {
            if (time === 0) {
                setShowOtpButton(false);
                clearInterval(id);
            } else {
                setIntervalId(id)
                setCounter((prev) => prev - 1)
                time--;
            }
        }
    }

    const updateMobileNumber = () => {
        if (selectedItem.walletCode === walletListEnum.BPCL && validateData()) {
            let queryParams: any = {
                otp: userParams?.otp,
                paymentId: selectedItem.paymentId,
                walletCode: selectedItem.walletCode
            }
            setLoading(true);
            appDispatch(updateBpclMobileNumber(queryParams)).then((response: any) => {
                if (response && response?.code === 200) {
                    let orchestrationQueryParams: any = {
                        orchestrationId: response.details.orchestrationId,
                    }
                    onSuccess(orchestrationQueryParams);
                    clearData();
                }
                setLoading(false);
            })
        }

        if (selectedItem.walletCode === walletListEnum.IOCL) {
            let queryParams: any = {
                paymentId: selectedItem.paymentId,
                walletCode: selectedItem.walletCode
            }
            setLoading(true);
            appDispatch(updateIoclMobileNumber(queryParams)).then((response: any) => {
                if (response && response?.code === 200) {
                    let orchestrationQueryParams: any = {
                        orchestrationId: response.details.orchestrationId,
                    }
                    onSuccess(orchestrationQueryParams);
                    clearData();
                }
                setLoading(false);
            })
        }

        if (selectedItem.walletCode === walletListEnum.JIOBP) {
            let queryParams: any = {
                paymentId: selectedItem.paymentId,
                walletCode: selectedItem.walletCode
            }
            setLoading(true);
            appDispatch(updateJioBPMobileNumber(queryParams)).then((response: any) => {
                if (response && response?.code === 200) {
                    let orchestrationQueryParams: any = {
                        orchestrationId: response.details.orchestrationId,
                    }
                    onSuccess(orchestrationQueryParams);
                    clearData();
                }
                setLoading(false);
            })
        }
    }

    const getOtp = () => {
        appDispatch(getBpclOtp()).then((response: any) => {
            if (response && response?.code === "200") {
                setShowTimer(true)
                timer();
            }
        })
    }

    return (
        <ModalContainer
            title={`${selectedItem.walletCode} Mobile Number Update`}
            styleName={"update-mobileNumber-modal"}
            secondaryButtonTitle={"Submit"}
            secondaryButtonStyle={"btn-blue"}
            open={open}
            secondaryButtonLoading={loading || pollingLoader}
            onApply={() => {
            }}
            secondaryButtonLeftIcon={<ArrowRightAlt />}
            onClose={() => {
                clearData();
                onClose();
            }}
            onClear={() => {
                updateMobileNumber();
            }}
            secondaryButtonDisable={!userParams.otp && selectedItem.walletCode === walletListEnum.BPCL}
        >

            <div className="custom-form-row row driver-number">
                <div className="form-group col-12 label-down">
                    <EditText
                        label={`${selectedItem.walletCode} Mobile Number (Old)`}
                        placeholder={`${selectedItem.walletCode} Mobile Number (Old)`}
                        disabled
                        maxLength={10}
                        value={getMobileNumber("old")}
                        onChange={(text: any) => {
                        }}
                    />
                </div>

                <div className="form-group col-12 label-down">
                    <EditText
                        label={`${selectedItem.walletCode} Mobile Number (New)`}
                        placeholder={`${selectedItem.walletCode} Mobile Number (New)`}
                        disabled
                        maxLength={10}
                        value={getMobileNumber("new")}
                        onChange={(text: any) => {
                        }}
                    />
                </div>

                {selectedItem.walletCode === walletListEnum.BPCL &&
                    <div className="form-group col-12 label-down otp--wrapper">
                        <NumberEditText
                            label={"OTP"}
                            placeholder={"Enter OTP"}
                            maxLength={6}
                            value={userParams.otp}
                            error={error.otp}
                            onChange={(text: any) => {
                                setError({});
                                setUserParams({
                                    ...userParams,
                                    otp: text
                                });
                            }}
                        />
                        <Button
                            title={"Get OTP"}
                            buttonStyle={"btn-orange approve--btn"}
                            onClick={() => {
                                getOtp();
                            }}
                            disable={showOtpButton}
                        />
                    </div>
                }
                <div className="resend-otp--wrapper">
                    {showTimer && (counter === 0 ?

                        <p onClick={() => {
                            getOtp();
                        }
                        } className="resend-otp--btn">Resend OTP</p> :
                        <p className="resend-otp--text"> Resend OTP in <span>{counter < 10 ? `00:0${counter}` : `00:${counter}`}</span> </p>
                    )}
                </div>
            </div>
        </ModalContainer >
    );

    function clearData() {
        setUserParams({});
        setError({});
        setShowTimer(false);
        setShowOtpButton(false);
    }

    function validateData() {
        if (userParams) {
            if (isNullValue(userParams.otp) || userParams.otp.length < 6) {
                setError({
                    otp: "Enter valid OTP"
                })
                return false;
            }
            return true;
        }
        return false;
    }
    function getMobileNumber(typeOfNumber: string){
        if(selectedItem.walletCode === walletListEnum.BPCL){
            if(typeOfNumber==='old'){
                return selectedItem.bpclMobileNumber;
            }else{
                return selectedItem.requestedBpclMobileNumber;
            }
        }
        if(selectedItem.walletCode === walletListEnum.IOCL){
            if(typeOfNumber==='old'){
                return selectedItem.ioclMobileNumber;
            }else{
                return selectedItem.requestedIoclMobileNumber;
            }
        }
        if(selectedItem.walletCode === walletListEnum.JIOBP){
            if(typeOfNumber==='old'){
                return selectedItem.jiobpMobileNumber;
            }else{
                return selectedItem.requestedJiobpMobileNumber;
            }
        }
    }

}

export default ApproveMobileNumberModal;
