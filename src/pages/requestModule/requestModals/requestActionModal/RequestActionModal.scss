.request-action-modal {
  .MuiDialog-container {
    .MuiDialog-paper {
      min-width: 560px;
      max-width: 560px;
      @media screen and (max-width: 767px) {
        min-width: 100%;
      }
      .MuiDialogContent-root {
        padding: 0;
        overflow: hidden;
        .form-group {
          margin-bottom: 0;
        }
      }
    }
    .MuiDialogActions-root {
      padding: 5px 20px 20px 20px;
    }
  }
  &--wrapper {
    .legacy-heading {
      padding: 8px 0;
      .legacy-currency {
        background-color: #006cc9;
        align-self: center;
        span {
          color: #ffffff;
        }
      }
    }
    > div {
      &:not(:last-child) {
        border-bottom: 1px solid #e9eff4;
      }
      &:first-child,
      &:last-child.form-group {
        padding-left: 20px;
        padding-right: 0;
      }
    }

    .row {
      padding: 10px 0 10px 5px;
      margin: 0;

      .media {
        font-weight: 500;
      }
    }
    div.comment-box {
      margin-top: 16px;
      .input-wrap {
        padding-bottom: 20px;
        .MuiFormHelperText-root{
          padding-right: 20px;
        }
        label {
          margin-bottom: 10px;
        }
      }
    }
  }
}
