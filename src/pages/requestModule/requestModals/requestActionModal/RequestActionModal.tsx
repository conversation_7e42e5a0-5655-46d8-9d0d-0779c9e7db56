import CheckCircleIcon from '@material-ui/icons/CheckCircle';
import ClearIcon from "@material-ui/icons/Clear";
import React from "react";
import { useDispatch } from "react-redux";
import { useHistory } from "react-router";
import { commonModalArray, paymentStatusEnum, walletListEnum } from "../../../../base/constant/ArrayList";
import { driverMonileNoLabel, driverNameLabel, raisedAtLabel, raisedByLabel } from "../../../../base/constant/MessageUtils";
import { requestListingRoute } from "../../../../base/constant/RoutePath";
import { convertDateFormat, displayDateTimeFormatter } from "../../../../base/utility/DateUtils";
import { isNullValue } from "../../../../base/utility/StringUtils";
import Information from "../../../../component/information/Information";
import EditText from "../../../../component/widgets/EditText";
import ModalContainer from "../../../../modals/ModalContainer";
import { showAlert } from "../../../../redux/actions/AppActions";
import { createApproveRequest } from '../../../../serviceActions/OpsWorkflowServiceAction';
import { cancelRequest, cancelRequestPaid, cancelRequestPaidGoboltCash, rejectRequest } from "../../../../serviceActions/RequestServiceAction";
import "./RequestActionModal.scss";
import { isPaidReactiveMaintenanceRequest } from '../../requestModuleUtility/RequestUtility';

interface RequestActionModalProps {
  open: any;
  onClose: any;
  onSuccess: any;
  actionType?: any;
  selectedItem: any;
  confirmMessage?: any;
}

function RequestActionModal(props: RequestActionModalProps) {
  const appDispatch = useDispatch();
  const history = useHistory()
  const { open, onClose, selectedItem, actionType, onSuccess, confirmMessage } = props;
  const [userParams, setUserParams] = React.useState<any>({});
  const [error, setError] = React.useState<any>({});
  const [loading, setLoading] = React.useState<any>(false);

  return (
    <ModalContainer
      title={getPageTitle()}
      styleName={"request-action-modal"}
      secondaryButtonTitle={getButtonTitle()}
      secondaryButtonStyle={getButtonStyle()}
      secondaryButtonLoading={loading}
      open={getOpenValue()}
      loading={loading}
      secondaryButtonLeftIcon={getButtonIcon()}
      onClose={() => {
        clearData();
        onClose();
        setUserParams({})
      }}
      onApply={() => {
      }}
      onClear={() => {
        const validate = validateData();
        if (validate === false) {
          //Hit orchestrator API for BPCL and HAPPAY
          if (actionType['onClickedApprove']) {
            const params: any = {
              paymentId: selectedItem?.paymentId,
              walletCode: selectedItem?.walletDetails?.walletCode,
              fromStatus: selectedItem?.paymentStatus
            }
            if(!isNullValue(confirmMessage)){
              params['isDuplicate'] = confirmMessage;
            }
            setLoading(true)
            appDispatch(createApproveRequest(params)).then((response: any) => {
              if (response) {
                onSuccess(response, selectedItem?.walletDetails?.walletCode);
              }
              setLoading(false)
            })
          }
        } else if (validate === true) {
          const params: any = {
            comments: userParams?.comments,
            paymentId: selectedItem?.paymentId
          }
          if (getPageTitle() === commonModalArray[0]) {
            setLoading(true);
            appDispatch(rejectRequest(params)).then((response: any) => {
              if (response) {
                onSuccess();
                response.message && appDispatch(showAlert(response.message, "true"));
                history.push(requestListingRoute)
              }
              setLoading(false);
            })
          } else if (getPageTitle() === commonModalArray[1] && actionType['onClickedCancel']) {
            setLoading(true);
            appDispatch(cancelRequest(params)).then((response: any) => {
              if (response) {
                onSuccess()
                response.message && appDispatch(showAlert(response.message, "true"));
                history.push(requestListingRoute)
              }
              setLoading(false);
            })
          } else if (getPageTitle() === commonModalArray[1] && actionType['onClickedCancelPaid']) {
            params.walletCode = selectedItem?.walletCode;
            setLoading(true);
            if(selectedItem?.walletCode === walletListEnum.GOBOLT_CASH){
              appDispatch(cancelRequestPaidGoboltCash(params)).then((response: any) => {
                if (response) {
                  onSuccess(response)
                }
                setLoading(false);
              })
            }else{
              appDispatch(cancelRequestPaid(params)).then((response: any) => {
                if (response) {
                  onSuccess(response)
                }
                setLoading(false);
              })
            }
          } else {
            const params: any = {
              paymentId: selectedItem?.paymentId,
              walletCode: selectedItem?.walletDetails?.walletCode,
              fromStatus: selectedItem?.paymentStatus
            }
            if(!isNullValue(confirmMessage)){
              params['isDuplicate'] = confirmMessage;
            }
            //Hit Orchestrator API
            setLoading(true)
            appDispatch(createApproveRequest(params)).then((response: any) => {
              if (response) {
                onSuccess(response);
              }
              setLoading(false)
            })
          }
        }
        setUserParams({})
      }}
    >
      <div className="request-action-modal--wrapper">
        <div className="d-flex align-item-center legacy-heading">
          <div className="legacy-currency">
            <span>₹</span>
          </div>
          <div className="legacy-balance">
            <span className="legacy-name">Request Amount</span>
            <p className="m-0 legacy-price">{`₹ ${(selectedItem.totalRequestAmount)?.toFixed(2) || 0}`}</p>
          </div>
        </div>
        <div className="custom-form-row row">
          <div className="col-md-6 col-6">
            <Information
              title={driverNameLabel}
              text={selectedItem.driverName}
            />
          </div>
          <div className="col-md-6 col-6">
            <Information
              title={driverMonileNoLabel}
              text={selectedItem.driverMobileNumber}
            />
          </div>
        </div>
        <div className="custom-form-row row">
          <div className="col-md-6 col-6">
            <Information
              title={raisedByLabel}
              text={selectedItem.createdBy}
            />
          </div>
          <div className="col-md-6 col-6">
            <Information
              title={raisedAtLabel}
              text={selectedItem.createdAt && convertDateFormat(selectedItem.createdAt, displayDateTimeFormatter)}
            />
          </div>
        </div>
        {isShowComments(actionType, selectedItem) && <div className="form-group col-12 comment-box">
          <EditText
            label={"Comments"}
            placeholder={"Comments"}
            mandatory
            maxLength={100}
            error={error.comments}
            value={userParams?.comments}
            onChange={(text: any) => {
              setUserParams({
                ...userParams,
                comments: text,
              });
              setError({});
            }}
          />
        </div>}
      </div>
    </ModalContainer >
  );

  function clearData() {
    setUserParams({});
    setError({});
  }

  function validateData() {
    if (actionType) {
      if (actionType.onClickedReject || actionType.onClickedCancel || actionType.onClickedCancelPaid) {
        if (isNullValue(userParams?.comments)) {
          setError({ comments: "Enter comments" });
          return false;
        }
      }
      return true;
    }
    return true;
  }

  function isShowComments(actionType: any, selectedItem: any) {
    if (selectedItem.paymentStatus === 'PENDING'){
      if (actionType.onClickedCancel || actionType.onClickedReject){
        return true;
      }
    } else if (selectedItem.walletCode === walletListEnum.BPCL || selectedItem.walletCode === walletListEnum.HAPPAY ||
               selectedItem.walletCode === walletListEnum.IOCL || selectedItem.walletCode === walletListEnum.CAMIONS_RAZOR_PAY 
               || selectedItem.walletCode === walletListEnum.JIOBP || selectedItem.walletCode === walletListEnum.GOBOLT_CASH
               || (selectedItem.paymentStatus === paymentStatusEnum.PAID && selectedItem.walletCode === walletListEnum.OPS_WALLET)) {
      if (actionType.onClickedCancelPaid || actionType.onClickedCancel || actionType.onClickedReject) {
        return true;
      }
    } else {
      return false;
    }
    return false;
  }

  function getOpenValue() {
    if (open) {
      if (open.onClickedReject) {
        return true;
      } else if (open.onClickedCancel || open.onClickedCancelPaid) {
        return true;
      } else if (actionType.onClickedApprove) {
        return true;
      } else {
        return false;
      }
    }
    return false
  }

  function getButtonTitle() {
    if (actionType) {
      if (actionType.onClickedReject) {
        return "Reject"
      } else if (actionType.onClickedCancel || actionType.onClickedCancelPaid) {
        return "Cancel"
      } else if (actionType.onClickedApprove) {
        return "Pay"
      } else {
        return ""
      }
    }
    return ""
  }

  function getButtonStyle() {
    if (actionType) {
      if (actionType.onClickedReject) {
        return "btn-red--reject btn-action"
      } else if (actionType.onClickedCancel || actionType.onClickedCancelPaid) {
        return "btn-grey--cancel btn-action"
      } else if (actionType.onClickedApprove) {
        return "btn-blue"
      } else {
        return ""
      }
    }
    return ""
  }

  function getButtonIcon() {
    if (actionType) {
      if (actionType.onClickedApprove) {
        return <CheckCircleIcon />
      } else {
        return <ClearIcon />
      }
    }
  }


  function getPageTitle() {
    if (actionType) {
      if (actionType.onClickedReject) {
        return commonModalArray[0];
      } else if (actionType.onClickedCancel || actionType.onClickedCancelPaid) {
        return commonModalArray[1];
      } else if (actionType.onClickedApprove) {
        return commonModalArray[2];
      } else {
        return ""
      }
    }
    return ""
  }
}

export default RequestActionModal;
