import React from 'react';
import { useDispatch } from 'react-redux';
import { statusPaidOptionList, statusPendingOptionList } from '../../../../base/constant/ArrayList';
import AutoComplete from '../../../../component/widgets/AutoComplete';
import EditText from '../../../../component/widgets/EditText';
import ModalContainer from '../../../../modals/ModalContainer';
import { changeUnknownStatus } from '../../../../serviceActions/RequestServiceAction';
import "./ResolveActionModal.scss";

interface ResolveActionModalProps  {
    open: boolean;
    onClose: Function;
    onSuccess: Function;
    selectedItem: any;
    pollingLoader:boolean;
};

function ResolveActionModal(props: ResolveActionModalProps) {
    const { open, onClose, onSuccess, selectedItem, pollingLoader } = props;
    const [status, setStatus] = React.useState<any>(undefined);
    const [loading, setLoading] = React.useState<any>(false);
    const appDispatch = useDispatch();

    return (
        <ModalContainer
            open={open}
            styleName = {'resolveModal'}
            onClose={() => {
                setStatus(undefined);
                onClose()
            }}
            onApply={() => {
                const params: any = {
                    paymentId: selectedItem?.paymentId,
                    walletCode: selectedItem?.walletDetails?.walletCode,
                    paymentStatus: (selectedItem?.approvedAt && selectedItem?.approvedBy) ? (status.value === 'SUCCESS' ? 'CANCELLED' : 'PAID') : (status.value === 'SUCCESS' ? 'PAID' : 'PENDING')
                }
                setLoading(true);
                appDispatch(changeUnknownStatus(params)).then((response: any) => {
                    if (response?.code === 200) {
                        onSuccess(response.details);
                    }
                    setLoading(false)
                })
            }}
            title={'Resolve'}
            primaryButtonTitle={'Proceed'}
            primaryButtonStyle={'btn-orange proceed-btn'}
            loading={loading || pollingLoader}
            primaryButtonDisable={status===undefined}
        >
          <div className='custom-form-row'>
            <div className='form-group'>
                <AutoComplete
                    label={'Transaction Status'}
                    placeHolder={'Select'}
                    onChange={(element: any) => {
                        setStatus(element);
                    }}
                        options={(selectedItem?.approvedAt && selectedItem?.approvedBy) ? statusPaidOptionList : statusPendingOptionList}
                    value={status}
                />

            </div>
            {status &&
                <div className='form-group'>
                    <EditText
                        label={'Request Status'}
                        placeholder={''}
                            value={(selectedItem?.approvedAt && selectedItem?.approvedBy) ? (status.value === 'SUCCESS' ? 'Move the request to Cancelled ' : 'Move the request to Paid') : (status.value === 'SUCCESS' ? 'Move the request to Paid' : 'Move the request to Pending')}
                        onChange={() => {

                        }}
                        maxLength={150}
                        disabled
                    />
                </div>
            }
          </div>
        </ModalContainer>
    );
}


export default ResolveActionModal;