import RequestModuleTypes from "./RequestTypes";

export const toggleFilter = () => ({
    type: RequestModuleTypes.TOGGLE_FILTER,
});

export const toggleModal = () => ({
    type: RequestModuleTypes.TOGGLE_MODAL,
});

export const setSelectedElement = (value: any) => ({
    type: RequestModuleTypes.SELECTED_ELEMENT,
    value,
});

export const setSelectedTab = (value: any) => ({
    type: RequestModuleTypes.SELECTED_TAB_INDEX,
    value
});

export const setResponse = (response: any) => ({
    type: RequestModuleTypes.SET_RESPONSE,
    response,
});

export const setCurrentPage = (value: any) => ({
    type: RequestModuleTypes.SET_CURRENT_PAGE,
    value
});

export const refreshList = () => ({
    type: RequestModuleTypes.REFRESH_LIST,
});

export const setRowPerPage = (value: any) => ({
    type: RequestModuleTypes.SET_ROW_PER_PAGE,
    value
});

export const showLoading = () => ({
    type: RequestModuleTypes.SHOW_LOADING,
});

export const hideLoading = () => ({
    type: RequestModuleTypes.HIDE_LOADING,
});

export const toggleBulkUpload = () => ({
    type: RequestModuleTypes.TOGGLE_BULK_UPLOAD,
});

export const setCheckedListResponse = (response: any) => ({
    type: RequestModuleTypes.SET_CHECKED_RESPONSE,
    response,
});

export const setWalletType = (value: any) => ({
    type: RequestModuleTypes.SET_WALLET_TYPE,
    value
})

export const handleViewDocumentModal = (isOpen: boolean, documentLinks: any) => ({
    type: RequestModuleTypes.HANDLE_DOCUMENT_MODAL,
    isOpen,
    documentLinks
})
