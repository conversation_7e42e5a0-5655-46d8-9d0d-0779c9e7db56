import { createReducer } from "reduxsauce";
import { requestListingStatusArray, rowsPerPageOptions } from "../../../base/constant/ArrayList";
import { isMobile } from "../../../base/utility/ViewUtils";
import RequestModuleTypes from "./RequestTypes";

interface RequestModuleState {
    openFilter: boolean,
    openBulkUpload: boolean,
    selectedItem: any,
    pagination: any,
    listData: any,
    openModal: boolean,
    selectedTabIndex: number,
    selectedTabName: any
    currentPage: number,
    refreshList: boolean,
    loading: boolean,
    pageSize: number,
    filterParams: any,
    filterChips: any,
    walletType: any,
    isViewDocumentModalOpen: boolean,
    documentLinks: Array<any>;
}

export const REQUEST_MODULE_STATE: RequestModuleState = {
    openFilter: false,
    openBulkUpload: false,
    selectedItem: undefined,
    pagination: undefined,
    listData: undefined,
    openModal: false,
    currentPage: 1,
    selectedTabIndex: 0,
    selectedTabName: requestListingStatusArray[0],
    refreshList: false,
    loading: false,
    pageSize: rowsPerPageOptions[0],
    filterParams: {},
    filterChips: {},
    walletType: {
        label: "GoBoLT Wallet (MCPL)",
        value: "MCPL",
    },
    isViewDocumentModalOpen: false,
    documentLinks: []
}

const toggleFilterReducer = (state = REQUEST_MODULE_STATE) => ({
    ...state,
    openFilter: !state.openFilter
});

const toggleModalReducer = (state = REQUEST_MODULE_STATE) => ({
    ...state,
    openModal: !state.openModal
});

const setSelectedElementReducer = (state = REQUEST_MODULE_STATE, action: any) => ({
    ...state,
    selectedItem: action.value
});

const setResponseReducer = (state = REQUEST_MODULE_STATE, action: any) => ({
    ...state,
    pagination: action.response && action.response.pagination,
    listData: isMobile ?
        (state.listData ? [...state.listData, ...action.response && action.response.data] : action.response && action.response.data)
        : action.response && action.response.data,
});

const setCurrentPageReducer = (state = REQUEST_MODULE_STATE, action: any) => ({
    ...state,
    currentPage: action.value
});

const refreshListReducer = (state = REQUEST_MODULE_STATE) => ({
    ...state,
    refreshList: !state.refreshList,
    currentPage: 1,
    listData: undefined,
});

const setRowPerPageReducer = (state = REQUEST_MODULE_STATE, action: any) => ({
    ...state,
    pageSize: action.value,
    currentPage: 1,
    listData: undefined,
});

const showLoadingReducer = (state = REQUEST_MODULE_STATE) => ({
    ...state,
    loading: true
});

const hideLoadingReducer = (state = REQUEST_MODULE_STATE) => ({
    ...state,
    loading: false
});

const toggleBulkUploadReducer = (state = REQUEST_MODULE_STATE) => ({
    ...state,
    openBulkUpload: !state.openBulkUpload
});

const setSelectedTabReducer = (state = REQUEST_MODULE_STATE, action: any) => ({
    ...state,
    selectedTabIndex: action.value,
    selectedTabName: requestListingStatusArray[action.value],
    listData: undefined,
});

const setCheckedListResponse = (state = REQUEST_MODULE_STATE, action: any) => ({
    ...state,
    listData: action.response
})

const setWalletTypeReducer = (state = REQUEST_MODULE_STATE, action: any) => ({
    ...state,
    walletType: action.value
})

const handleViewDocumentModalReducer = (state = REQUEST_MODULE_STATE, action: any) => ({
    ...state,
    isViewDocumentModalOpen: action.isOpen,
    documentLinks: action.isOpen ? action.documentLinks : []
})

const ACTION_HANDLERS = {
    [RequestModuleTypes.TOGGLE_FILTER]: toggleFilterReducer,
    [RequestModuleTypes.TOGGLE_MODAL]: toggleModalReducer,
    [RequestModuleTypes.SELECTED_ELEMENT]: setSelectedElementReducer,
    [RequestModuleTypes.SET_RESPONSE]: setResponseReducer,
    [RequestModuleTypes.SET_CURRENT_PAGE]: setCurrentPageReducer,
    [RequestModuleTypes.REFRESH_LIST]: refreshListReducer,
    [RequestModuleTypes.SET_ROW_PER_PAGE]: setRowPerPageReducer,
    [RequestModuleTypes.SHOW_LOADING]: showLoadingReducer,
    [RequestModuleTypes.HIDE_LOADING]: hideLoadingReducer,
    [RequestModuleTypes.TOGGLE_BULK_UPLOAD]: toggleBulkUploadReducer,
    [RequestModuleTypes.SELECTED_TAB_INDEX]: setSelectedTabReducer,
    [RequestModuleTypes.SET_CHECKED_RESPONSE]: setCheckedListResponse,
    [RequestModuleTypes.SET_WALLET_TYPE]: setWalletTypeReducer,
    [RequestModuleTypes.HANDLE_DOCUMENT_MODAL]: handleViewDocumentModalReducer,

}

export default createReducer(REQUEST_MODULE_STATE, ACTION_HANDLERS);