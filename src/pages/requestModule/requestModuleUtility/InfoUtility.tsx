import { InfoRounded } from "@material-ui/icons";
import React from "react"
import { InfoTooltip } from "../../../component/widgets/tooltip/InfoTooltip";
import "./InfoUtility.scss";

export function InfoUtility(){
    return(
        <div className="fixed-tooltip">
            <InfoTooltip
                title={<span>Please verify the amount<br/> before proceeding</span>}
                placement={"right"}
                disableInMobile={"false"}
                infoText={<InfoRounded />}
                className="fixed-tooltip-content"
                arrow={true}
                open={true}
                style={{
                    tooltip:{
                        border: '1px solid #0769D9',
                        padding: '4px 8px',
                        margin: '13px 12px'
                    },
                    arrow:{
                        fontSize: '16px',
                        color: '#0769D9',
                        top: '6px !important'
                    },
                    popper:{
                        zIndex: '1'
                    }
                }}
                
            />
        </div>
    )
}