import React, { Dispatch } from 'react'
import Information from '../../../component/information/Information';
import { OverflowTip } from '../../../component/widgets/tooltip/OverFlowToolTip';

interface ReactiveRepairMaintenanceRequestInfoProps {
    response: any,
}

const ReactiveRepairMaintenanceRequestInfo = (props: ReactiveRepairMaintenanceRequestInfoProps) => {
    const { response } = props;

    const renderInformation = (title: string, text: any, customView?: React.ReactNode) => (
        <div className="col-md-4 col-lg-3 card-group col-6">
            <Information
                title={title}
                text={text}
                customView={customView}
            />
        </div>
    );

    return (
        <div className="custom-form-row row wallet-box">

            {renderInformation("Vendor Name", undefined, <OverflowTip text={response?.opsWalletVendorName || 'NA'} />,)}

            {renderInformation("Beneficiary", response?.opsWalletBeneficiaryName || 'NA')}

            {renderInformation("Repair Type", response?.repairType)}

            {renderInformation("Total Amount", response?.totalRequestAmount ? `₹ ${response?.totalRequestAmount.toFixed(2)}` : '₹ 0.00')}

            {renderInformation("Account Number", response?.opsWalletBeneficiaryAccountNo || 'NA')}

            {renderInformation("IFSC Code", response?.opsWalletBeneficiaryIfscCode || 'NA')}
        </div>
    )
}

export default ReactiveRepairMaintenanceRequestInfo