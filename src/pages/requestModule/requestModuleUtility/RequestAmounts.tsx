import React from "react";
import Information from "../../../component/information/Information";
import { InfoTooltip } from "../../../component/widgets/tooltip/InfoTooltip";
import { showAmountField } from "./RequestUtility";
import { STANDARD_AMOUNT_LIMIT } from "../../../base/constant/limits";
import { InfoUtility } from "./InfoUtility";
import { walletListEnum } from "../../../base/constant/ArrayList";
import "./RequestAmounts.scss";
import { utrNoLabel } from "../../../base/constant/MessageUtils";

interface RequestAmountProps {
  response: any,
  walletCode: any,
  setDocumentLinks: any,
  setOpenViewDocumentsModal: any,
}

function RequestAmounts(props: RequestAmountProps) {
  const { response, walletCode, setDocumentLinks, setOpenViewDocumentsModal } = props;
  const documentLinks = response?.documentLinks;
  return (
    <div className="custom-form-row row wallet-box">
      {showAmountField(walletCode, "tollAmount") && 
      <div className="col-md-4 col-lg-3 card-group col-6">
        <Information
          title={"Toll Amount"}
          text={(response?.walletDetails?.tollAmount && "₹ " + (response?.walletDetails?.tollAmount).toFixed(2)) || "₹ 0.00"}
        />
        {response?.walletDetails?.tollAmount > STANDARD_AMOUNT_LIMIT && response.paymentStatus ==="PENDING" && <InfoUtility />}
        {documentLinks && documentLinks['tollAmount'] &&  (
          <button
            style={{"background":'none'}}
            onClick={()=>{
              setDocumentLinks(documentLinks?.['tollAmount']);
              setOpenViewDocumentsModal(true);
            }}
          >
            <img src="/images/InsertPhotoOutlined.svg" alt="InsertPhotoOutlined" />
            <p>{documentLinks['tollAmount']?.[0]?.totalDocumentCount}</p>
          </button>
        )}
      </div>}
      
      {showAmountField(walletCode, "challanAmount") && <div className="col-md-4 col-lg-3 card-group col-6">
        <Information
          title={"Challan Amount"}
          text={(response?.walletDetails?.challanAmount && "₹ " + (response?.walletDetails?.challanAmount).toFixed(2)) || "₹ 0.00"}
        />
        {response?.walletDetails?.challanAmount > STANDARD_AMOUNT_LIMIT && response.paymentStatus === "PENDING" && <InfoUtility />}
        {documentLinks && documentLinks['challanAmount'] &&  (
          <button
            style={{"background":'none'}}
            onClick={()=>{
              setDocumentLinks(documentLinks?.['challanAmount']);
              setOpenViewDocumentsModal(true);
            }}
          >
            <img src="/images/InsertPhotoOutlined.svg" alt="InsertPhotoOutlined" />
            <p>{documentLinks['challanAmount']?.[0]?.totalDocumentCount}</p>
          </button>
        )}
      </div>}
      
      {showAmountField(walletCode, "loadingAmount") && <div className="col-md-4 col-lg-3 card-group col-6">
        <Information
          title={"Loading Amount"}
          text={(response?.walletDetails?.loadingAmount && "₹ " + (response?.walletDetails?.loadingAmount).toFixed(2)) || "₹ 0.00"}
        />
        {response?.walletDetails?.loadingAmount > STANDARD_AMOUNT_LIMIT && response.paymentStatus === "PENDING" && <InfoUtility />}
        {documentLinks && documentLinks['loadingAmount'] &&  (
          <button
            style={{"background":'none'}}
            onClick={()=>{
              setDocumentLinks(documentLinks?.['loadingAmount']);
              setOpenViewDocumentsModal(true);
            }}
          >
            <img src="/images/InsertPhotoOutlined.svg" alt="InsertPhotoOutlined" />
            <p>{documentLinks['loadingAmount']?.[0]?.totalDocumentCount}</p>
          </button>
        )}
      </div>}
      {showAmountField(walletCode, "unloadingAmount") && <div className="col-md-4 col-lg-3 card-group col-6">
        <Information
          title={"Unloading Amount"}
          text={(response?.walletDetails?.unloadingAmount && "₹ " + (response?.walletDetails?.unloadingAmount).toFixed(2)) || "₹ 0.00"}
        />
        {response?.walletDetails?.unloadingAmount > STANDARD_AMOUNT_LIMIT && response.paymentStatus === "PENDING" && <InfoUtility />}
        {documentLinks && documentLinks['unloadingAmount'] &&  (
          <button
            style={{"background":'none'}}
            onClick={()=>{
              setDocumentLinks(documentLinks?.['unloadingAmount']);
              setOpenViewDocumentsModal(true);
            }}
          >
            <img src="/images/InsertPhotoOutlined.svg" alt="InsertPhotoOutlined" />
            <p>{documentLinks['unloadingAmount']?.[0]?.totalDocumentCount}</p>
          </button>
        )}
      </div>}
      {showAmountField(walletCode, "foodingAmount") && <div className="col-md-4 col-lg-3 card-group col-6">
        <Information
          title={"Fooding Amount"}
          text={(response?.walletDetails?.foodingAmount && "₹ " + (response?.walletDetails?.foodingAmount).toFixed(2)) || "₹ 0.00"}
        />
        {response?.walletDetails?.foodingAmount > STANDARD_AMOUNT_LIMIT && response.paymentStatus === "PENDING" && <InfoUtility />}
        {documentLinks && documentLinks['foodingAmount'] &&  (
          <button
            style={{"background":'none'}}
            onClick={()=>{
              setDocumentLinks(documentLinks?.['foodingAmount']);
              setOpenViewDocumentsModal(true);
            }}
          >
            <img src="/images/InsertPhotoOutlined.svg" alt="InsertPhotoOutlined" />
            <p>{documentLinks['foodingAmount']?.[0]?.totalDocumentCount}</p>
          </button>
        )}
      </div>}
      {showAmountField(walletCode, "dallaAmount") && <div className="col-md-4 col-lg-3 card-group col-6">
        <Information
          title={"Advance Amount"}
          text={(response?.walletDetails?.dallaAmount && "₹ " + (response?.walletDetails?.dallaAmount).toFixed(2)) || "₹ 0.00"}
        />
        {response?.walletDetails?.dallaAmount > STANDARD_AMOUNT_LIMIT && response.paymentStatus === "PENDING" && <InfoUtility />}
        {documentLinks && documentLinks['dallaAmount'] &&  (
          <button
            style={{"background":'none'}}
            onClick={()=>{
              setDocumentLinks(documentLinks?.['dallaAmount']);
              setOpenViewDocumentsModal(true);
            }}
          >
            <img src="/images/InsertPhotoOutlined.svg" alt="InsertPhotoOutlined" />
            <p>{documentLinks['dallaAmount']?.[0]?.totalDocumentCount}</p>
          </button>
        )}
      </div>}
     
      {showAmountField(walletCode, "maintainceAmount") && <div className="col-md-4 col-lg-3 card-group col-6">
        <Information
          title={"Maintenance Amount"}
          text={(response?.walletDetails?.maintainceAmount && "₹ " + (response?.walletDetails?.maintainceAmount).toFixed(2)) || "₹ 0.00"}
        />
        {response?.walletDetails?.maintainceAmount > STANDARD_AMOUNT_LIMIT && response.paymentStatus === "PENDING" && <InfoUtility />}
        {documentLinks && documentLinks['maintenanceAmount'] &&  (
          <button
            style={{"background":'none'}}
            onClick={()=>{
              setDocumentLinks(documentLinks?.['maintenanceAmount']);
              setOpenViewDocumentsModal(true);
            }}
          >
            <img src="/images/InsertPhotoOutlined.svg" alt="InsertPhotoOutlined" />
            <p>{documentLinks['maintenanceAmount']?.[0]?.totalDocumentCount}</p>
          </button>
        )}
      </div>}
     
      {showAmountField(walletCode, "accidentSettlemantAmount") && <div className="col-md-4 col-lg-3 card-group col-6">
        <Information
          title={"Accident Settlement Amount"}
          text={(response?.walletDetails?.accidentSettlemantAmount && "₹ " + (response?.walletDetails?.accidentSettlemantAmount).toFixed(2)) || "₹ 0.00"}
        />
        {response?.walletDetails?.accidentSettlemantAmount > STANDARD_AMOUNT_LIMIT && response.paymentStatus === "PENDING" && <InfoUtility />}
        {documentLinks && documentLinks['accidentSettlementAmount'] &&  (
          <button
            style={{"background":'none'}}
            onClick={()=>{
              setDocumentLinks(documentLinks?.['accidentSettlementAmount']);
              setOpenViewDocumentsModal(true);
            }}
          >
            <img src="/images/InsertPhotoOutlined.svg" alt="InsertPhotoOutlined" />
            <p>{documentLinks['accidentSettlementAmount']?.[0].totalDocumentCount}</p>
          </button>
        )}
      </div>}
      {showAmountField(walletCode, "stateList") && <div className="col-md-3 card-group col-6">
        <Information
          title={"State for Fuel"}
          text={response?.walletDetails?.stateForFuel}
          customView={
            <InfoTooltip
              title={response?.walletDetails?.stateForFuel || "NA"}
              placement={"top"}
              disableInMobile={"false"}
              infoText={response?.walletDetails?.stateForFuel || "NA"}
            />
          }
        />
      </div>}
      {showAmountField(walletCode, "fuelAmount") && <div className="col-md-4 col-lg-3 card-group col-6">
        <Information
          title={"Fuel Amount"}
          text={(response?.walletDetails?.fuelAmount && "₹ " + (response?.walletDetails?.fuelAmount).toFixed(2)) || "₹ 0.00"}
        />
        {documentLinks && documentLinks['fuelAmount'] &&  (
          <button
            style={{"background":'none'}}
            onClick={()=>{
              setDocumentLinks(documentLinks?.['fuelAmount']);
              setOpenViewDocumentsModal(true);
            }}
          >
            <img src="/images/InsertPhotoOutlined.svg" alt="InsertPhotoOutlined" />
            <p>{documentLinks['fuelAmount']?.[0]?.totalDocumentCount}</p>
          </button>
        )}
      </div>}
      
      {showAmountField(walletCode, "fuelQuantity") && <div className="col-md-4 col-lg-3 card-group col-6">
        <Information
          title={"Fuel Quantity"}
          text={(response?.walletDetails?.fuelQuantity && (response?.walletDetails?.fuelQuantity).toFixed(2)) || "NA"}
        />
      </div>}
      {showAmountField(walletCode, "fuelRate") && <div className="col-md-4 col-lg-3 card-group col-6">
        <Information
          title={"Fuel Rate Per Litre"}
          text={(response?.walletDetails?.fuelRate && "₹ " + (response?.walletDetails?.fuelRate).toFixed(2)) || "₹ 0.00"}
          customView={
            (walletCode === walletListEnum.BPCL || walletCode === walletListEnum.JIOBP) &&
              <>
                <span>{(response?.walletDetails?.fuelRate && "₹ " + (response?.walletDetails?.fuelRate).toFixed(2)) || "₹ 0.00"}</span>
                <InfoTooltip
                  title={"Suggestive Fuel Rate"}
                  placement={"top"}
                  disableInMobile={"false"}
                  className="bpcl-wallet--tooltip"
                  children={(response?.walletDetails?.suggestiveFuelRate && <><br/>{"₹ " + (response?.walletDetails?.suggestiveFuelRate).toFixed(2)}</>) || <><br/>{"NA"}</>}
                />
              </>
          }
        />
      </div>}
      {showAmountField(walletCode, "adblueAmount") && <div className="col-md-4 col-lg-3 card-group col-6">
        <Information
          title={"AdBlue Amount"}
          text={(response?.walletDetails?.adblueAmount && "₹ " + (response?.walletDetails?.adblueAmount).toFixed(2)) || "₹ 0.00"}
        />
         {documentLinks && documentLinks['adblueAmount'] &&  (
          <button
            style={{"background":'none'}}   
            onClick={()=>{
              setDocumentLinks(documentLinks?.['adblueAmount']);
              setOpenViewDocumentsModal(true);
            }}
          >
            <img src="/images/InsertPhotoOutlined.svg" alt="InsertPhotoOutlined" />
            <p>{documentLinks['adblueAmount']?.[0]?.totalDocumentCount}</p>
          </button>
        )}
      </div>}
     
      {showAmountField(walletCode, "adblueQuantity") && <div className="col-md-4 col-lg-3 card-group col-6">
        <Information
          title={"AdBlue Quantity"}
          text={(response?.walletDetails?.adblueQuantity && (response?.walletDetails?.adblueQuantity).toFixed(2)) || "NA"}
        />
      </div>}
      {showAmountField(walletCode, "adblueRate") && <div className="col-md-4 col-lg-3 card-group col-6">
        <Information
          title={"AdBlue Rate Per Litre"}
          text={(response?.walletDetails?.adblueRate && "₹ " + (response?.walletDetails?.adblueRate).toFixed(2)) || "₹ 0.00"}
        />
      </div>}
      <div className="col-md-4 col-lg-3 card-group col-6">
        <Information
          title={"Remarks"}
          text={(response?.remarks)}
          customView={
            <InfoTooltip
              title={response?.remarks || "NA"}
              placement={"top"}
              disableInMobile={"false"}
              infoText={response?.remarks || "NA"}
            />
          }
        />
      </div>
      {walletCode === walletListEnum.CAMIONS_RAZOR_PAY &&
        <div className="col-md-4 col-lg-3 card-group col-6">
          <Information
            title={utrNoLabel}
            text={(response?.razorPayUtrNo)}
            customView={
              <InfoTooltip
                title={response?.razorPayUtrNo || "NA"}
                placement={"top"}
                disableInMobile={"false"}
                infoText={response?.razorPayUtrNo || "NA"}
              />
            }
          />
        </div>
      }
    </div>
  );
}

export default RequestAmounts;
