import { paymentStatusEnum, requestListingStatusEnum, balanceWalletListEnum, walletListEnum, fuelWalletCodesList, thirdPartyWalletCodesList, UploadDocumentList, walletStatusListEnum } from "../../../base/constant/ArrayList";
import { BPCLVariancePercentage, jioBPVariancePercentage } from "../../../base/moduleUtility/ConstantValues";
import { isObjectEmpty,isNullValue } from "../../../base/utility/StringUtils";
import { getVehicleDetails } from "../../../serviceActions/RequestServiceAction";

export const getPageLabelName = (value: any) => {
    if (value) {
        if (value.paymentStatus) {
            if (value.paymentStatus === paymentStatusEnum.PENDING || value.paymentStatus === paymentStatusEnum["INVALID PENDING"] || value.paymentStatus === paymentStatusEnum.UNKNOWN) return "Request Amount";
            else if (value.paymentStatus === paymentStatusEnum.RECONCILED || value.paymentStatus === paymentStatusEnum["INVALID RECONCILED"]) return "Reconciled Amount";
            else if (value.paymentStatus === paymentStatusEnum.PAID) return "Paid Amount";
            else if (value.paymentStatus === paymentStatusEnum.REJECTED) return "Rejected Amount";
            else if (value.paymentStatus === paymentStatusEnum.CANCELLED) return "Cancelled Amount";
            else return "";
        }
        return "Request Amount"
    }
    return "";
}

export const getPaymentTabStatus = (value: any) => {
    let status = ""
    switch (value) {
        case 0:
            status = requestListingStatusEnum.PENDING
            break;
        case 1:
            status = requestListingStatusEnum.APPROVED
            break;
        case 2:
            status = requestListingStatusEnum.PAID
            break;
        case 3:
            status = requestListingStatusEnum.RECONCILED
            break;
        case 4:
            status = requestListingStatusEnum.REJECTED
            break;
        case 5:
            status = requestListingStatusEnum.CANCELLED
            break;
        case 6:
            status = requestListingStatusEnum["INVALID PENDING"]
            break;
        case 7:
            status = requestListingStatusEnum["INVALID RECONCILED"]
            break;
        case 8:
            status = requestListingStatusEnum.UNKNOWN
            break;
        default:
            status = ""
    }
    return status
}

export function round_decimals_up(number: any, decimals: any = 2) {
    let temp = Number(number)
    let tempNum = 10 ** (decimals + 1);
    let digit = (tempNum * temp) % 10;
    if (digit === 5) {
        return Math.ceil(temp * (10 ** decimals)) / 10 ** decimals
    } else {
        return temp.toFixed(decimals);
    }
}

export const showAmountField = (walletCode: string, key: string) => {
    if (walletCode && key) {
        if (walletCode === walletListEnum.BPCL || walletCode === walletListEnum.JIOBP) {
            if (key === 'tollAmount' || key === 'challanAmount' || key === 'loadingAmount' || key === 'unloadingAmount'
                || key === 'foodingAmount' || key === 'dallaAmount' || key === 'maintainceAmount' || key === 'accidentSettlemantAmount'
                || key === 'fuelAmount' || key === 'fuelQuantity' || key === 'fuelRate'
                || key === 'adblueAmount' || key === 'adblueQuantity' || key === 'adblueRate'
                || key === 'stateList' || key === 'stateFuelRate' || key === 'actualStateFuelRate') {
                return true;
            }
        }else if (walletCode === walletListEnum.HAPPAY || walletCode === walletListEnum.IOCL  || walletCode === walletListEnum.CAMIONS_RAZOR_PAY || walletCode === walletListEnum.OPS_WALLET) {
            if (key === 'tollAmount' || key === 'challanAmount' || key === 'loadingAmount' || key === 'unloadingAmount'
                || key === 'foodingAmount' || key === 'dallaAmount' || key === 'maintainceAmount' || key === 'accidentSettlemantAmount'
                || key === 'fuelAmount' || key === 'fuelQuantity' || key === 'fuelRate'
                || key === 'adblueAmount' || key === 'adblueQuantity' || key === 'adblueRate') {
                return true;
            }
        } else if (walletCode === walletListEnum.OM_PETRO_MART_CASH || walletCode === walletListEnum.GOBOLT_CASH) {
            if (key === 'tollAmount' || key === 'challanAmount' || key === 'loadingAmount' || key === 'unloadingAmount'
                || key === 'foodingAmount' || key === 'dallaAmount' || key === 'maintainceAmount' || key === 'accidentSettlemantAmount'
                || key === 'adblueAmount' || key === 'adblueQuantity' || key === 'adblueRate') {
                return true;
            } else {
                return false;
            }
        } else if (walletCode === walletListEnum.MCPL || walletCode === walletListEnum.OM_PETRO_MART_DIESEL || walletCode === walletListEnum.CONTINENTAL_PETROLEUMS || walletCode === walletListEnum.GOBOLT_FUEL_PUMP) {
            if (key === 'fuelAmount' || key === 'fuelQuantity' || key === 'fuelRate') {
                return true;
            } else {
                return false;
            }
        } else if (walletCode === walletListEnum.HUB_ADBLUE) {
            if (key === 'adblueAmount' || key === 'adblueQuantity' || key === 'adblueRate') {
                return true;
            } else {
                return false;
            }
        }
    }
    return false
}

export const createRequestParams = (userParams: any, vehicleTrips: any, selectTrip: any, walletAmounts: any, fuelDetails: any, adBlueDetails: any, totalRequestedAmount: any, mobileNumberDetails: any, remarks: string, stateFuelRate: any, utrNumber?: string, confirmMessage?: string) => {
    const selectedTrip: any = vehicleTrips.find((item: any) => selectTrip === item.orderCode);
    const params: any = {
        customerName: selectedTrip.customerName,
        customerCode: selectedTrip.customerCode,
        destinationCode: selectedTrip.destinationCode,
        destinationName: selectedTrip.destinationName,
        originCode: selectedTrip.originCode,
        originName: selectedTrip.originName,
        driverCode: selectedTrip.driverCode,
        driverMobileNumber: selectedTrip.driverMobileNumber,
        driverName: selectedTrip.driverName,
        orderCode: selectedTrip.orderCode,
        tripCode: selectedTrip.tripCode,
        tripStatus: selectedTrip.tripStatus,
    }
    if (totalRequestedAmount) {
        params.totalRequestAmount = round_decimals_up(totalRequestedAmount, 2)
    }
    if (userParams?.vehicleNumber) {
        params.vehicleNumber = userParams?.vehicleNumber?.value
    }
    if (userParams?.wallet?.value === walletListEnum.BPCL) {
        params.bpclMobileNumber = mobileNumberDetails.mobileNumber;
    }
    if (userParams?.wallet?.value === walletListEnum.BPCL && !isObjectEmpty(mobileNumberDetails.newMobileNumber)){
        params.requestedBpclMobileNumber = mobileNumberDetails.newMobileNumber;
    }
    if (userParams?.wallet?.value === walletListEnum.IOCL) {
        params.ioclMobileNumber = mobileNumberDetails.mobileNumber;
    }
    if (userParams?.wallet?.value === walletListEnum.IOCL && !isObjectEmpty(mobileNumberDetails.newMobileNumber)){
        params.requestedIoclMobileNumber = mobileNumberDetails.newMobileNumber;
    }
    if (userParams?.wallet?.value === walletListEnum.JIOBP && !isObjectEmpty(mobileNumberDetails.newMobileNumber)){
        params.requestedJiobpMobileNumber = mobileNumberDetails.newMobileNumber;
    }
    if (userParams?.wallet?.value === walletListEnum.JIOBP) {
        params.jiobpMobileNumber = mobileNumberDetails.mobileNumber;
    }
    if (!isNullValue(remarks)) {
        params.remarks = remarks;
    }
    if (selectedTrip?.vehicleRequiredDateTime) {
        params.vehicleRequiredDateTime = selectedTrip?.vehicleRequiredDateTime;
    }
    if(!isNullValue(confirmMessage)){
        params.isDuplicate = confirmMessage;
    }
    if (userParams.wallet.value === walletListEnum.CAMIONS_RAZOR_PAY && !isNullValue(utrNumber)) {
        params.razorPayUtrNo = utrNumber;
    }
    params.walletType = {
        walletCode: userParams.wallet.value,
        accidentSettlemantAmount: walletAmounts.accidentSettlemantAmount ? Number(walletAmounts.accidentSettlemantAmount) : undefined,
        challanAmount: walletAmounts.challanAmount ? Number(walletAmounts.challanAmount) : undefined,
        dallaAmount: walletAmounts.dallaAmount ? Number(walletAmounts.dallaAmount) : undefined,
        foodingAmount: walletAmounts.foodingAmount ? Number(walletAmounts.foodingAmount) : undefined,

        loadingAmount: walletAmounts.loadingAmount ? Number(walletAmounts.loadingAmount) : undefined,
        maintainceAmount: walletAmounts.maintainceAmount ? Number(walletAmounts.maintainceAmount) : undefined,
        tollAmount: walletAmounts.tollAmount ? Number(walletAmounts.tollAmount) : undefined,
        unloadingAmount: walletAmounts.unloadingAmount ? Number(walletAmounts.unloadingAmount) : undefined,

        fuelAmount: walletAmounts.fuelAmount ? Number(walletAmounts.fuelAmount) : undefined,
        fuelQuantity: fuelDetails.fuelQuantity ? Number(fuelDetails.fuelQuantity) : undefined,
        fuelRate: fuelDetails.fuelRate ? round_decimals_up(fuelDetails.fuelRate, 2) : undefined,

        adblueAmount: walletAmounts.adblueAmount ? Number(walletAmounts.adblueAmount) : undefined,
        adblueQuantity: adBlueDetails.adblueQuantity ? Number(adBlueDetails.adblueQuantity) : undefined,
        adblueRate: adBlueDetails.adblueRate ? round_decimals_up(adBlueDetails.adblueRate, 2) : undefined,

        stateForFuel: (userParams.wallet.value === walletListEnum.BPCL || userParams.wallet.value === walletListEnum.JIOBP) ? stateFuelRate?.stateCode : undefined,
        suggestiveFuelRate: (userParams.wallet.value === walletListEnum.BPCL || userParams.wallet.value === walletListEnum.JIOBP) ? stateFuelRate?.stateRate : undefined,
    }

    if (userParams?.wallet?.value === walletListEnum.CAMIONS_RAZOR_PAY) {
        params.contact = {
            contact_id: userParams?.payoutContact?.data?.id,
            contact: userParams?.payoutContact?.data?.contact,
            name: userParams?.payoutContact?.data?.name,
            email: userParams?.payoutContact?.data?.email,
            type: userParams?.payoutContact?.data?.type?.toUpperCase(),
        }

        if (userParams?.payoutMethod?.data?.accountType === 'bank_account') {
            params.bank_account = {
                fund_account_id: userParams?.payoutMethod?.data?.id,
                ifsc: userParams?.payoutMethod?.data?.bankAccount?.ifsc,
                account_number: userParams?.payoutMethod?.data?.bankAccount?.accountNumber,
                account_holder_name: userParams?.payoutMethod?.data?.bankAccount?.name,
                bank_name: userParams?.payoutMethod?.data?.bankAccount?.bankName,
            }
        } else if (userParams?.payoutMethod?.data?.accountType === 'vpa') {
            params.vpa = {
                fund_account_id: userParams?.payoutMethod?.data?.id,
                username: userParams?.payoutMethod?.data?.vpa?.username,
                handle: userParams?.payoutMethod?.data?.vpa?.handle,
                address: userParams?.payoutMethod?.data?.vpa?.address,
            }
        }
    }
    return params
}


export const modifyRequestParams = (vehcileDetails: any, walletAmounts: any, fuelDetails: any, adBlueDetails: any, totalRequestedAmount: any, remarks: string, stateFuelRate: any, utrNumber?: string, confirmMessage?: string) => {
    const params: any = {
        paymentId: vehcileDetails.paymentId,
        totalRequestAmount: round_decimals_up(totalRequestedAmount, 2),
    }
    if (!isNullValue(remarks)) {
        params.remarks = remarks;
    }
    if(!isNullValue(confirmMessage)){
        params.isDuplicate = confirmMessage;
    }
    if (vehcileDetails?.walletCode === walletListEnum.CAMIONS_RAZOR_PAY && !isNullValue(utrNumber)) {
        params.razorPayUtrNo = utrNumber;
    }
    params.walletType = {
        walletCode: vehcileDetails.walletDetails?.walletCode,
        accidentSettlemantAmount: walletAmounts.accidentSettlemantAmount ? Number(walletAmounts.accidentSettlemantAmount) : undefined,
        challanAmount: walletAmounts.challanAmount ? Number(walletAmounts.challanAmount) : undefined,
        dallaAmount: walletAmounts.dallaAmount ? Number(walletAmounts.dallaAmount) : undefined,
        foodingAmount: walletAmounts.foodingAmount ? Number(walletAmounts.foodingAmount) : undefined,

        loadingAmount: walletAmounts.loadingAmount ? Number(walletAmounts.loadingAmount) : undefined,
        maintainceAmount: walletAmounts.maintainceAmount ? Number(walletAmounts.maintainceAmount) : undefined,
        tollAmount: walletAmounts.tollAmount ? Number(walletAmounts.tollAmount) : undefined,
        unloadingAmount: walletAmounts.unloadingAmount ? Number(walletAmounts.unloadingAmount) : undefined,

        fuelAmount: walletAmounts.fuelAmount ? Number(walletAmounts.fuelAmount) : undefined,
        fuelQuantity: fuelDetails.fuelQuantity ? Number(fuelDetails.fuelQuantity) : undefined,
        fuelRate: fuelDetails.fuelRate ? round_decimals_up(fuelDetails.fuelRate, 2) : undefined,

        adblueAmount: walletAmounts.adblueAmount ? Number(walletAmounts.adblueAmount) : undefined,
        adblueQuantity: adBlueDetails.adblueQuantity ? Number(adBlueDetails.adblueQuantity) : undefined,
        adblueRate: adBlueDetails.adblueRate ? round_decimals_up(adBlueDetails.adblueRate, 2) : undefined,

        stateForFuel: (vehcileDetails.walletDetails?.walletCode === walletListEnum.BPCL|| vehcileDetails.walletDetails?.walletCode === walletListEnum.JIOBP) ? stateFuelRate?.stateCode : undefined,
        suggestiveFuelRate: (vehcileDetails.walletDetails?.walletCode === walletListEnum.BPCL|| vehcileDetails.walletDetails?.walletCode === walletListEnum.JIOBP) ? stateFuelRate?.stateRate : undefined
    }
    return params;
}

export const getUploadDocumentsParams = (files: any, paymentRequestId: string, elementType: string, walletCode: string) => {
    var data = new FormData();
    for(const file of files){
        if(typeof file==='object' && file!==null){
            data.append(`files[]`, file);
        }
    }
    data.append("document_type", elementType);
    data.append("payment_request_id", paymentRequestId);
    data.append("wallet_code",walletCode);
    return data;
}

export const showBalanceField = (walletCode: string) => {
    if (walletCode) {
        if (walletCode === balanceWalletListEnum.BPCL || walletCode === balanceWalletListEnum.HAPPAY || walletCode === balanceWalletListEnum.IOCL || walletCode === balanceWalletListEnum.JIOBP) {
            return true;
        }
    }
    return false;
}

export const getDeleteDocumentParams = (file: any, paymentRequestId: any, elementType: any) =>{
    const params : any = {
        'payment_request_id': paymentRequestId,
        'uuid': file?.uuid,
        'document_type': elementType
    }
    return params;
}

export const getIOCLBalance = (walletCode: string, cardPan: string, mobileNumber?: number) =>{
    if(walletCode === balanceWalletListEnum.IOCL){
        if(!isNullValue(cardPan)){
            return true;
        }else{
            return false;
        }
    }else if(walletCode === balanceWalletListEnum.JIOBP){
        if(!isNullValue(cardPan) && !isNullValue(mobileNumber)){
            return true;
        }else{
            return false;
        }
    }else{
        return true;
    }
}

export const getUploadFields = (walletCode: string, repairId: string, isGoboltCashReconcileRequest?: boolean) => {
    if(!isNullValue(repairId)){
        return [
            {
                'label': "Maintenance Amount",
                'value': "maintenance_amount"
            }
        ]
    }
    if (isGoboltCashReconcileRequest) {
        return [
            {
                'label': "Upload Any Supporting Document",
                'value': "gobolt_cash_reconcile"
            }
        ]
    }
    if(fuelWalletCodesList.includes(walletCode)){
        return [
            {
                'label': "Fuel Amount",
                'value': "fuel_amount"
            }
        ]
    }else if(thirdPartyWalletCodesList.includes(walletCode)){
        return [...UploadDocumentList, {
                'label': "Fuel Amount",
                'value': "fuel_amount"
            }
        ]
    }else if(walletCode === walletListEnum.HUB_ADBLUE){
        return [
            {
                'label': "Adblue Amount",
                'value': "adblue_amount"
            }
        ]
    }else if(walletCode === walletListEnum.OM_PETRO_MART_CASH){
        return UploadDocumentList;
    } else if (walletCode === walletListEnum.GOBOLT_CASH) {
        return UploadDocumentList;
    }
}

export function checkBPCLVariance(suggestiveFuelRate: any, fuelRate: any, walletName: string){
    if(suggestiveFuelRate && fuelRate){
        let variancePercentage = 0;
        if(walletName==="JioBP Wallet" || walletName==walletListEnum.JIOBP){
            variancePercentage = jioBPVariancePercentage;
        }else{
            variancePercentage = BPCLVariancePercentage;
        }
        const variantPrice = suggestiveFuelRate + suggestiveFuelRate*variancePercentage;
        const roundedPrice = round_decimals_up(variantPrice, 2);
        if(roundedPrice < Number(fuelRate)){
            return true;
        }
        return false;
    }
}

export function isRequestFromMaintenanceModule(repairId: string) {
    return !isNullValue(repairId);
}

export const isPaidGobolCashWalletReconcileRequest = (walletCode: string, paymentStatus: string, isReconcileReq: boolean)  => {
    return (walletCode === walletListEnum.GOBOLT_CASH &&
    paymentStatus === paymentStatusEnum.PAID &&
    Boolean(isReconcileReq));
}

export const showGoboltCashReconcileDocuments = (
    paymentStatus: paymentStatusEnum, 
    walletCode: string, 
    documentLinks: any
) => {
    const validPaymentStatuses = [
        paymentStatusEnum.RECONCILED,
        paymentStatusEnum["INVALID PENDING"],
        paymentStatusEnum["INVALID RECONCILED"]
    ];
    return (
        validPaymentStatuses.includes(paymentStatus) &&
        walletCode === walletListEnum.GOBOLT_CASH &&
        documentLinks &&
        (documentLinks?.['goboltCashReconcile']?.length ?? 0) > 0
    );
}

export const isReactiveRepairMaintenenceRequest = (type: string) => {
    return !isNullValue(type) && type.toUpperCase() === 'REACTIVE_REPAIR';
}

export const getUploadFieldsForReactiveRepairMaintenance = () => {
    return [
        {
            'label': "Upload Any Supporting Document",
            'value': "maintenance_amount"
        }
    ]
}
export const getDriverDocLinks = (data: any) => {
    let docLinks: Array<any> = [];
    if(!isNullValue(data?.data?.driver_license_photo)){
        docLinks = [...docLinks, {documentLink: data.data.driver_license_photo}];
    }
    if(!isNullValue(data?.data?.driver_license_photo_back)){
        docLinks = [...docLinks, {documentLink: data.data.driver_license_photo_back}];
    }
    return docLinks;
}

export const isPaidMaintenanceRequestForHappayOrCamionsRazorPay = (response: any) => {
    return response?.repairId
        && response?.paymentStatus === paymentStatusEnum.PAID
        && (
            response?.walletDetails?.walletCode === walletListEnum.HAPPAY
            || response?.walletDetails?.walletCode === walletListEnum.CAMIONS_RAZOR_PAY
        )
}

export const isPaidReactiveMaintenanceRequest = (response: any) => {
    return isReactiveRepairMaintenenceRequest(response?.maintenanceType)
        && response?.paymentStatus === paymentStatusEnum.PAID
}

export const getWalletNameInDetails = (response: any) => {
    const { walletName = "NA", walletCode, paymentStatus, fuelPumpName } = response;

    if (walletName !== 'NA' && walletCode === walletListEnum.GOBOLT_FUEL_PUMP &&
        requestStatusToShowFuelPump(paymentStatus)) {
        return `${walletName}${fuelPumpName ? ` (${fuelPumpName})` : ''}`;
    }

    return walletName;
};

export const requestStatusToShowFuelPump = (requestStatus: string) => {
    const validStatuses = new Set([
        paymentStatusEnum.RECONCILED.toLowerCase(),
        paymentStatusEnum["INVALID PENDING"].toLowerCase(),
        paymentStatusEnum["INVALID RECONCILED"].toLowerCase()
    ]);

    return validStatuses.has(requestStatus.toLowerCase());
}

export const fetchVehicleDetails = (appDispatch: Function, vehicle_number: string, setDetails: Function) => {
    setDetails({});
    return appDispatch(getVehicleDetails({vehicle_number})).then((vehicleDetailsResponse: any)=> {
        if(vehicleDetailsResponse && vehicleDetailsResponse.data){
            setDetails(vehicleDetailsResponse.data);
        }
    });
}

export const extractUPIId = (upiLink: string) => {
    try {
        const url = new URL(upiLink);
        const upiId = url.searchParams.get('pa');
        return upiId ? upiId : null;
    } catch (error) {
        throw new Error('Invalid UPI link');
    }
}

export const isValidUPIId = (upiId: string) => {
    const upiIdRegex = new RegExp(/^[a-zA-Z0-9.\-_]{2,256}@[a-zA-Z]{2,64}$/);
    return upiIdRegex.test(upiId);
}

export const isValidUPILink = (upiLink: string) => {
    try {
        const url = new URL(upiLink);
        return url.protocol === 'upi:' && url.searchParams.has('pa');
    } catch (error) {
        return false;
    }
}