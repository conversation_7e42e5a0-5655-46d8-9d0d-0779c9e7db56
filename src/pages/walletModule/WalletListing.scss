.transaction-view {
  padding: 16px 40px;
  background: #fff;
  @media screen and (max-width: 767px) {
    padding: 10px 16px;
  }
  p {
    margin: 0;
    color: #083654;
    font-size: 16px;
  }
}
.tab-nav--wallet {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0px 1px 3px #0000000d;
  background-color: #ffffff;
  padding: 4px 0;
  .Mui-selected {
    color: #f7931e;
  }
  .autocomplete-wrap {
    display: flex;
    align-items: center;
    .select__control {
      width: 160px;
      min-height: 40px;
      height: 40px;
      margin-left: 8px;
    }
    label {
      margin-bottom: 0;
      color: #083654;
      font-size: 15px;
    }
  }
}

@media (min-width:768px){
  .tab-nav--wallet {
    padding-right: 20px;
  }
}
@media (max-width:767px){
  .tab-nav--wallet {
    flex-direction: column;
     .autocomplete-wrap {
      margin-top: 5px;
     }
  }
}
