import { makeStyles, Tab, Tabs } from '@material-ui/core';
import { AddCircle } from '@material-ui/icons';
import React, { useEffect, useReducer } from 'react';
import { shallowEqual, useDispatch, useSelector } from "react-redux";
import { useHistory, useParams } from 'react-router';
import { headerMenuButtons, rowsPerPageOptions, transactionTypesOptionList, walletNamesArray } from '../../base/constant/ArrayList';
import { walletListingRoute } from '../../base/constant/RoutePath';
import { handleApiError } from '../../base/utility/ErrorHandleUtils';
import { useQuery } from '../../base/utility/Routerutils';
import { isNullValue } from '../../base/utility/StringUtils';
import { isMobile } from '../../base/utility/ViewUtils';
import Filter from '../../component/filter/Filter';
import PageContainer from '../../component/pageContainer/PageContainer';
import { TabPanel } from '../../component/tabs/TabPanel';
import AutoComplete from '../../component/widgets/AutoComplete';
import Button from '../../component/widgets/button/Button';
import CardList from '../../component/widgets/cardlist/CardList';
import TableList from '../../component/widgets/tableView/TableList';
import { OptionType } from '../../component/widgets/widgetsInterfaces';
import { setHeaderMenu } from '../../redux/actions/AppActions';
import { addWalletBalance, getAdblueWalletBalance, getContinentalWalletBalance, getWalletList } from '../../serviceActions/McplServiceAction';
import { adBlueListingTableColumns, walletListingTableColumns } from '../../templates/WalletListingTemplate';
import './WalletListing.scss';
import AddMoneyModal from './walletModals/AddMoneyModal';
import { hideLoading, refreshList, setCurrentPage, setResponse, setRowPerPage, setSelectedTab, setTransactionType, showLoading } from './walletRedux/WalletActions';
import WalletReducer, { WALLET_STATE } from './walletRedux/WalletReducer';

const useStyles = makeStyles({
    indicator: {
        background: "none",
    },
    tabs: {
        "& button[aria-selected='true']": {
            margin: "0 20px",
            borderBottom: "3px solid #F7931E",
        },
    },
});

function WalletListing() {
    const appDispatch = useDispatch();
    const history = useHistory()
    const classes = useStyles();
    const { id } = useParams<any>();
    const params = useQuery();
    const [state = WALLET_STATE, dispatch] = useReducer(WalletReducer, WALLET_STATE);
    const [addMoney, openAddMoney] = React.useState<any>({
        mcplWallet: false,
        adblueWallet: false,
        continentalWallet:false,
    });
    const [walletBalance, setWalletBalance] = React.useState<any>(0);
    const [adBlueBalance, setAdBlueBalance] = React.useState<any>(0);
    const [continentalBalance, setContinentalBalance] = React.useState<any>(0);
    const [loading, setLoading] = React.useState<any>(false);
    const [updateWalletBalance, setUpdateWalletBalance] = React.useState<boolean>(false);
    const [showAddMoneyButton, setShowAddMoneyButton] = React.useState<boolean>(false);
    const rolesList = useSelector((state: any) => state.appReducer.rolesList, shallowEqual);

    const redirectToDashboard = () => {
        history.push("/");
    }
    appDispatch(setHeaderMenu(headerMenuButtons[2]));

    useEffect(() => {
        const getWalletBalance = async () => {
            dispatch(showLoading())
            if (rolesList) {
                const isWalletEditor = rolesList.indexOf("wallet-editor")
                const isWalletViewer = rolesList.indexOf("wallet-viewer")
                if (isWalletEditor >= 0) {
                    setShowAddMoneyButton(true);
                } else if (isWalletViewer >= 0) {
                    setShowAddMoneyButton(false);
                } else {
                    setShowAddMoneyButton(false);
                }
            }
            if (id === undefined || id === walletNamesArray[0]) {
                appDispatch(addWalletBalance()).then((response: any) => {
                    if (response && response[0] && response[0].totalBalance) {
                        let walletBalance = response[0].totalBalance && Number(response[0].totalBalance).toFixed(2);
                        setWalletBalance(walletBalance);
                    }
                    else {
                        setWalletBalance(0);
                    }
                    dispatch(hideLoading())
                }).catch((error: any) => {
                    appDispatch(handleApiError(error.message, appDispatch, redirectToDashboard));
                });;
            } else if (id === walletNamesArray[1]){
                appDispatch(getAdblueWalletBalance()).then((response: any) => {
                    if (response && response[0] && response[0].totalBalance) {
                        let walletBalance = response[0].totalBalance && Number(response[0].totalBalance).toFixed(2);
                        setAdBlueBalance(walletBalance);
                    }
                    else {
                        setAdBlueBalance(0);
                    }
                    dispatch(hideLoading())
                }).catch((error: any) => {
                    appDispatch(handleApiError(error.message, appDispatch, redirectToDashboard));
                });

            } else {
                appDispatch(getContinentalWalletBalance()).then((response: any) => {
                    if (response && response[0] && response[0].totalBalance) {
                        let walletBalance = response[0].totalBalance && Number(response[0].totalBalance).toFixed(2);
                        setContinentalBalance(walletBalance);
                    }
                    else {
                        setContinentalBalance(0);
                    }
                    dispatch(hideLoading())
                }).catch((error: any) => {
                    appDispatch(handleApiError(error.message, appDispatch, redirectToDashboard));
                });
            }
            dispatch(hideLoading())
        }
        getWalletBalance();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [updateWalletBalance, rolesList, state.selectedTabIndex]);


    useEffect(() => {
        const getList = async () => {
            let queryParams: any = {
                page: state.currentPage,
                size: state.pageSize,
                transactionType: state.transactionType?.value
            }
            setLoading(true);
            dispatch(setSelectedTab(id ? walletNamesArray.indexOf(id) : 0))
            appDispatch(getWalletList(queryParams, id)).then((response: any) => {
                if (response) {
                    dispatch(setResponse(response));
                }
                setLoading(false);
            }).catch((error: any) => {
                appDispatch(handleApiError(error.message, appDispatch, redirectToDashboard));
            });
        }
        getList();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [state.refreshList, state.currentPage, state.pageSize, state.selectedTabIndex, history.location.search, state.transactionType.value, id])

    return (
        <div>
            <AddMoneyModal
                open={(isNullValue(id) || id === walletNamesArray[0]) ? addMoney?.mcplWallet : (id === walletNamesArray[1] ? addMoney?.adblueWallet : addMoney?.continentalWallet )}
                onSuccess={() => {
                    openAddMoney({
                        ...openAddMoney,
                        mcplWallet: false,
                        adblueWallet: false,
                        continentalWallet: false,
                    });
                    setUpdateWalletBalance((prev: boolean) => !prev)
                    dispatch(refreshList());
                }}
                onClose={() => {
                    openAddMoney({
                        ...openAddMoney,
                        mcplWallet: false,
                        adblueWallet: false,
                        continentalWallet: false,
                    });
                }}
                walletBalance={(isNullValue(id) || id === walletNamesArray[0]) ? walletBalance : (id === walletNamesArray[1] ? adBlueBalance : continentalBalance)}
                id={id}
            />
            <>
                <Filter
                    pageTitle={
                        <>
                            <div className='d-flex align-item-center legacy-heading'>
                                <div className='legacy-currency'>
                                    <span>₹</span>
                                </div>
                                <div className='legacy-balance'>
                                    <span className='legacy-name'>{(id === undefined || id === walletNamesArray[0]) ? "Current MCPL Balance" : (id === walletNamesArray[1] ? "Current Hub AdBlue Balance" : "Current Continental Petroleums Balance")}</span>
                                    {(id === undefined || id === walletNamesArray[0]) ? <p className='m-0 legacy-price'>{`₹ ${Number(walletBalance)}`}</p>
                                        : (id === walletNamesArray[1] ? <p className='m-0 legacy-price'>{`₹ ${Number(adBlueBalance)}`}</p> : <p className='m-0 legacy-price'>{`₹ ${Number(continentalBalance)}`}</p>) }
                                </div>
                            </div>
                        </>
                    }
                >
                    {showAddMoneyButton && <Button
                        buttonStyle={"btn-orange btn-rounded "}
                        title={(isNullValue(id) || id === walletNamesArray[0]) ? "Add MCPL Money" : (id === walletNamesArray[1] ? "Add Adblue Money" : "Add Continental Money")}
                        loading={state.loading}
                        leftIcon={<AddCircle />}
                        onClick={() => {
                            // let flag = (isNullValue(id) || id === walletNamesArray[0]);
                            if ((isNullValue(id) || id === walletNamesArray[0])) {
                                openAddMoney({
                                    ...openAddMoney,
                                    mcplWallet: true,
                                    adblueWallet: false,
                                    continentalWallet: false,
                                });
                            } else if (id === walletNamesArray[1]){
                                openAddMoney({
                                    ...openAddMoney,
                                    mcplWallet: false,
                                    adblueWallet: true,
                                    continentalWallet: false,
                                });
                            } else {
                                openAddMoney({
                                    ...openAddMoney,
                                    mcplWallet: false,
                                    adblueWallet: false,
                                    continentalWallet: true,
                                });
                            }
                        }}
                    />}
                </Filter>

                <div>
                    <div className="bill-tab tab-nav">
                        {
                            <>
                                <div className="tab-nav--wallet">
                                    <Tabs
                                        value={state.selectedTabIndex}
                                        className={classes.tabs}
                                        classes={{ indicator: classes.indicator }}
                                        onChange={(event: any, newValue: any) => {
                                            if (newValue !== state.selectedTabIndex) {
                                                dispatch(setSelectedTab(newValue));
                                                dispatch(setCurrentPage(1));
                                                dispatch(setTransactionType(transactionTypesOptionList[0]))
                                                dispatch(setRowPerPage(rowsPerPageOptions[0]));
                                                history.replace({
                                                    pathname: walletListingRoute + walletNamesArray[newValue],
                                                    search: params.toString(),
                                                });
                                            }
                                        }}
                                        variant="scrollable"
                                        scrollButtons={isMobile ? "on" : "off"}
                                    >
                                        {walletNamesArray.map((element, index) => (
                                            <Tab key={index} label={element} />
                                        ))}
                                    </Tabs>
                                    <AutoComplete
                                        label={"Transaction Type:"}
                                        placeHolder={"Select Field"}
                                        value={state.transactionType}
                                        options={transactionTypesOptionList}
                                        onChange={(value: OptionType) => {
                                            dispatch(setTransactionType(value));
                                        }}
                                    />
                                </div>
                            </>
                        }

                        <TabPanel value={state.selectedTabIndex} index={state.selectedTabIndex}>
                            {pageContent()}
                        </TabPanel>
                    </div>
                </div>
            </>
        </div>
    );

    function pageContent() {
        if (id === undefined || id === walletNamesArray[0] || id === walletNamesArray[2]) {
            return (<PageContainer
                loading={state.loading || loading}
                listData={state.listData}
            >
                {
                    isMobile ?
                        <CardList
                            listData={state.listData}
                            tableColumns={walletListingTableColumns()}
                            isNextPage={state.pagination && state.pagination.next}
                            onReachEnd={() => {
                                dispatch(setCurrentPage(state.pagination.next))
                            }}
                        />
                        :
                        <TableList
                            tableColumns={walletListingTableColumns()}
                            currentPage={state.currentPage}
                            rowsPerPage={state.pageSize}
                            rowsPerPageOptions={rowsPerPageOptions}
                            totalCount={state.pagination && state.pagination.count}
                            listData={state.listData}
                            onChangePage={(event: any, page: number) => {
                                dispatch(setCurrentPage(page));
                            }}
                            onRowsPerPageChange={(event: any) => {
                                dispatch(setRowPerPage(event.target.value));
                            }}
                        />
                }
            </PageContainer>)
        } else if (id === walletNamesArray[1]){
            return (<PageContainer
                loading={state.loading || loading}
                listData={state.listData}
            >
                {
                    isMobile ?
                        <CardList
                            listData={state.listData}
                            tableColumns={adBlueListingTableColumns()}
                            isNextPage={state.pagination && state.pagination.next}
                            onReachEnd={() => {
                                dispatch(setCurrentPage(state.pagination.next))
                            }}
                        />
                        :
                        <TableList
                            tableColumns={adBlueListingTableColumns()}
                            currentPage={state.currentPage}
                            rowsPerPage={state.pageSize}
                            rowsPerPageOptions={rowsPerPageOptions}
                            totalCount={state.pagination && state.pagination.count}
                            listData={state.listData}
                            onChangePage={(event: any, page: number) => {
                                dispatch(setCurrentPage(page));
                            }}
                            onRowsPerPageChange={(event: any) => {
                                dispatch(setRowPerPage(event.target.value));
                            }}
                        />
                }
            </PageContainer>)
        }
    }
}



export default WalletListing;