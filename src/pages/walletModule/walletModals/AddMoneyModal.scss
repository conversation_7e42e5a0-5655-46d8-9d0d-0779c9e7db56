.add-money-modal {
    .MuiDialog-paper {
        min-width: 560px;
        max-width: 560px;
        @media screen and (max-width:767px) {
            min-width: 100%;
        }
    }

    .MuiDialogContent-root { padding-top: 4px; }

    .MuiDialogActions-root {
        justify-content: space-between;
        @media screen and (max-width:767px) {
            text-align: left;
        }
    }

    .add-current {
        text-align: right;
        .add-current-balance {
            color: #768A9E;
            font-size: 11px;
            line-height: 15px;
            margin-right: 5px;
        }

        .add-current-price{
            color: #323232;
            font-size: 14px;
            line-height: 21px;
            font-weight: bold;
        }
    }

    .total-amount {
        .total-amount-label {
            color: #768A9E;
            font-size: 14px;
            line-height: 1;
        }

        .total-amount-value {
            color: #323232;
            font-size: 20px;
            font-weight: bold;
        }
    }

    .label-down {
        .input-wrap {
            .d-flex {
                margin-bottom: 5px;
                label {
                    margin-bottom: 0;
                }
            }
        }
    }
}