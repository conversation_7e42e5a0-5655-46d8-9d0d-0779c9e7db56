import CheckCircleIcon from '@material-ui/icons/CheckCircle';
import React from "react";
import { useDispatch } from "react-redux";
import { walletNamesArray } from '../../../base/constant/ArrayList';
import { adblueQuantityLabelInLtr, adblueQuantityPlaceHolder, adblueRateLabel, adblueRatePlaceHolder,  fuelPriceLabel, fuelPricePlaceHolder, fuelQuantityLabel, fuelQuantityPlaceHolder, invoiceNoLabel, invoiceNoPlaceHolder } from "../../../base/constant/MessageUtils";
import { isNullValue } from "../../../base/utility/StringUtils";
import EditText from "../../../component/widgets/EditText";
import NumberEditText from "../../../component/widgets/NumberEditText";
import ModalContainer from "../../../modals/ModalContainer";
import { showAlert } from "../../../redux/actions/AppActions";
import { addAdblueWallet, addContinentalWallet, addMcplMoney } from "../../../serviceActions/McplServiceAction";
import "./AddMoneyModal.scss";

interface AddMoneyModalsProps {
    open: boolean
    onClose: any
    onSuccess: any,
    walletBalance: Number,
    id: string
}

function AddMoneyModal(props: AddMoneyModalsProps) {
    const appDispatch = useDispatch();
    const { open, onClose, onSuccess, walletBalance, id } = props;
    const [userParams, setUserParams] = React.useState<any>({
        fuelDetails: {},
        adblueDetails: {},
        invoiceNumber: ''
    });
    const [error, setError] = React.useState<any>({
        fuelDetails: {},
        adblueDetails: {},
    });
    const [loading, setLoading] = React.useState<boolean>(false);

    return (
        <ModalContainer
            title={(isNullValue(id) || id === walletNamesArray[0]) ? "Add MCPL Money" : (id === walletNamesArray[1] ? "Add Hub Adblue Money" : "Add Continental Petroleums Money") }
            styleName={"add-money-modal"}
            primaryButtonTitle={"Add Money"}
            open={open}
            loading={loading}
            primaryButtonType="submit"
            primaryButtonLeftIcon={<CheckCircleIcon />}
            onClose={() => {
                clearData();
                onClose();
                setUserParams({
                    fuelDetails: {},
                    adblueDetails: {},
                    invoiceNumber: ''
                })
            }}
            actionButtonLabel={true}
            actionButtonValue={(isNullValue(id) || id === walletNamesArray[0] || id === walletNamesArray[2]) ? "Total Amount" : "AdBlue Amount"}
            onApply={() => {
                if (validateData()) {
                    let params: any = {}
                    if (isNullValue(id) || id === walletNamesArray[0] || id === walletNamesArray[2]) {
                        params = {
                            fuelQuantity: Number(userParams?.fuelDetails?.fuelQuantity),
                            fuelPrice: Number(userParams?.fuelDetails?.fuelPrice),
                        };
                    } else {
                        params = {
                            adblueQuantity: Number(userParams?.adblueDetails?.adblueQuantity),
                            adbluePrice: Number(userParams?.adblueDetails?.adbluePrice),
                        };
                    }
                    params.invoiceNo = userParams?.invoiceNumber
                    setLoading(true)
                    if (isNullValue(id) || id === walletNamesArray[0]) {
                        appDispatch(addMcplMoney(params)).then((response: any) => {
                            // eslint-disable-next-line
                            if (response?.code == "200") {
                                clearData();
                                onSuccess();
                                response.message && appDispatch(showAlert(response.message, "true"));
                            }
                            setLoading(false)
                        })
                    } else if (id === walletNamesArray[1]){
                        appDispatch(addAdblueWallet(params)).then((response: any) => {
                            // eslint-disable-next-line
                            if (response?.code == "200") {
                                clearData();
                                onSuccess();
                                response.message && appDispatch(showAlert(response.message, "true"));
                            }
                            setLoading(false)
                        })
                    }
                    else {
                        appDispatch(addContinentalWallet(params)).then((response: any) => {
                            // eslint-disable-next-line
                            if (response?.code == "200") {
                                clearData();
                                onSuccess();
                                response.message && appDispatch(showAlert(response.message, "true"));
                            }
                            setLoading(false)
                        })
                    }
                };
            }}
            onClear={() => {
                clearData();
            }}
            fuelQuantity={(isNullValue(id) || id === walletNamesArray[0] || id === walletNamesArray[2]) ? userParams?.fuelDetails?.fuelQuantity :  userParams?.adblueDetails?.adblueQuantity }
            fuelPrice={(isNullValue(id) || id === walletNamesArray[0] || id === walletNamesArray[2]) ? userParams?.fuelDetails?.fuelPrice :  userParams?.adblueDetails?.adbluePrice}
        >
            <div className="add-current">
                <span className="add-current-balance">Current Wallet Balance</span>
                <span className="add-current-price">{`₹ ${walletBalance}`}</span>
            </div>
            <div className="custom-form-row row">
                {(isNullValue(id) || id === walletNamesArray[0] || id === walletNamesArray[2]) ? <>
                    <div className="form-group col-12 label-down">
                        <NumberEditText
                            label={fuelQuantityLabel}
                            mandatory
                            placeholder={fuelQuantityPlaceHolder}
                            required
                            error={error?.fuelDetails?.fuelQuantity}
                            maxLength={8}
                            decimalScale={2}
                            value={userParams?.fuelDetails?.fuelQuantity}
                            onChange={(text: any) => {
                                setError({});
                                setUserParams({
                                    ...userParams,
                                    adblueDetails: undefined,
                                    fuelDetails: {
                                        ...userParams.fuelDetails,
                                        fuelQuantity: text
                                    },
                                });
                            }}
                        />
                    </div>
                    <div className="form-group col-12 label-down">
                        <NumberEditText
                            label={fuelPriceLabel}
                            placeholder={fuelPricePlaceHolder}
                            mandatory
                            error={error?.fuelDetails?.fuelPrice}
                            maxLength={6}
                            decimalScale={2}
                            value={userParams?.fuelDetails?.fuelPrice}
                            onChange={(text: any) => {
                                setError({});
                                setUserParams({
                                    ...userParams,
                                    adblueDetails: undefined,
                                    fuelDetails: {
                                        ...userParams.fuelDetails,
                                        fuelPrice: text
                                    },
                                });
                            }}
                        />
                    </div>
                </> :
                (
                    <>
                        <div className="form-group col-12 label-down">
                            <NumberEditText
                                label={adblueQuantityLabelInLtr}
                                mandatory
                                placeholder={adblueQuantityPlaceHolder}
                                required
                                error={error?.adblueDetails?.adblueQuantity}
                                maxLength={8}
                                decimalScale={2}
                                value={userParams?.adblueDetails?.adblueQuantity}
                                onChange={(text: any) => {
                                    setError({});
                                    setUserParams({
                                        ...userParams,
                                        fuelDetails: undefined,
                                        adblueDetails: {
                                            ...userParams.adblueDetails,
                                            adblueQuantity: text
                                        },
                                    });
                                }}
                            />
                        </div>
                        <div className="form-group col-12 label-down">
                            <NumberEditText
                                label={adblueRateLabel}
                                placeholder={adblueRatePlaceHolder}
                                mandatory
                                error={error?.adblueDetails?.adbluePrice}
                                maxLength={6}
                                decimalScale={2}
                                value={userParams?.adblueDetails?.adbluePrice}
                                onChange={(text: any) => {
                                    setError({});
                                    setUserParams({
                                        ...userParams,
                                        fuelDetails: undefined,
                                        adblueDetails: {
                                            ...userParams.adblueDetails,
                                            adbluePrice: text
                                        },
                                    });
                                }}
                            />
                        </div>
                    </>
                    )
                }
                <div className="form-group col-12 label-down">
                    <EditText
                        label={invoiceNoLabel}
                        placeholder={invoiceNoPlaceHolder}
                        maxLength={50}
                        required
                        error={error?.invoiceNumber}
                        value={userParams?.invoiceNumber}
                        onChange={(text: any) => {
                            setError({});
                            setUserParams({
                                ...userParams,
                                invoiceNumber: text
                            });
                        }}
                    />
                </div>
            </div>
        </ModalContainer >
    );

    function clearData() {
        setUserParams({});
        setError({});
    }

    function validateData() {
        if ((isNullValue(id) || id === walletNamesArray[0] || id === walletNamesArray[2])) {
            if (isNullValue(userParams?.fuelDetails?.fuelQuantity)) {
                setError({
                    ...error,
                    adblueDetails: undefined,
                    fuelDetails: {
                        ...error.fuelDetails,
                        fuelQuantity: "Enter valid fuel quantity"
                    }
                });
                return false;
            }
            else if (isNullValue(userParams?.fuelDetails?.fuelPrice)) {
                setError({
                    ...error,
                    adblueDetails: undefined,
                    fuelDetails: {
                        ...error.fuelDetails,
                        fuelPrice: "Enter valid fuel price"
                    }
                });
                return false;
            }
            return true;
        } else  {
            if (isNullValue(userParams?.adblueDetails?.adblueQuantity)) {
                setError({
                    ...error,
                    fuelDetails: undefined,
                    adblueDetails: {
                        ...error.adblueDetails,
                        adblueQuantity: "Enter valid adblueQuantity"
                    }
                });
                return false;
            }
            else if (isNullValue(userParams?.adblueDetails?.adbluePrice)) {
                setError({
                    ...error,
                    fuelDetails: undefined,
                    adblueDetails: {
                        ...error.adblueDetails,
                        adbluePrice: "Enter valid adbluePrice rate"
                    }
                });
                return false;
            }
            return true;
        }
    }

}

export default AddMoneyModal;
