import WalletTypes from "./WalletTypes";

export const toggleFilter = () => ({
    type: WalletTypes.TOGGLE_FILTER,
});

export const toggleModal = () => ({
    type: WalletTypes.TOGGLE_MODAL,
});

export const setSelectedElement = (value: any) => ({
    type: WalletTypes.SELECTED_ELEMENT,
    value,
});

export const setResponse = (response: any) => ({
    type: WalletTypes.SET_RESPONSE,
    response,
});

export const setCurrentPage = (value: any) => ({
    type: WalletTypes.SET_CURRENT_PAGE,
    value
});

export const refreshList = () => ({
    type: WalletTypes.REFRESH_LIST,
});

export const setRowPerPage = (value: any) => ({
    type: WalletTypes.SET_ROW_PER_PAGE,
    value
});

export const showLoading = () => ({
    type: WalletTypes.SHOW_LOADING,
});

export const hideLoading = () => ({
    type: WalletTypes.HIDE_LOADING,
});

export const setSelectedTab = (value: any) => ({
    type: WalletTypes.SELECTED_TAB_INDEX,
    value
});

export const setTransactionType = (value: any) => ({
    type: WalletTypes.TRANSACTION_TYPE,
    value
})








