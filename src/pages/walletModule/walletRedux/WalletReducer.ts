import { createReducer } from "reduxsauce";
import { rowsPerPageOptions, transactionTypesOptionList, walletNamesArray } from "../../../base/constant/ArrayList";
import { isMobile } from "../../../base/utility/ViewUtils";
import WalletTypes from "./WalletTypes";

interface WalletState {
    openFilter: boolean,
    selectedItem: any,
    pagination: any,
    listData: any,
    openModal: boolean,
    currentPage: number,
    refreshList: boolean,
    loading: boolean,
    pageSize: number,
    filterParams: any,
    filterChips: any,
    selectedTabIndex: number,
    selectedTabName: any,
    transactionType: any
}

export const WALLET_STATE: WalletState = {
    openFilter: false,
    selectedItem: undefined,
    pagination: undefined,
    listData: undefined,
    openModal: false,
    currentPage: 1,
    refreshList: false,
    loading: false,
    pageSize: rowsPerPageOptions[0],
    filterParams: {},
    filterChips: {},
    selectedTabIndex: 0,
    selectedTabName: walletNamesArray[0],
    transactionType: transactionTypesOptionList[0]
}

const toggleFilterReducer = (state = WALLET_STATE) => ({
    ...state,
    openFilter: !state.openFilter
});

const toggleModalReducer = (state = WALLET_STATE) => ({
    ...state,
    openModal: !state.openModal
});

const setSelectedElementReducer = (state = WALLET_STATE, action: any) => ({
    ...state,
    selectedItem: action.value
});

const setResponseReducer = (state = WALLET_STATE, action: any) => ({
    ...state,
    listData: isMobile ?
        (state.listData ? [...state.listData, ...action.response && action.response.transactions] : action.response && action.response.transactions)
        : action.response && action.response.transactions,
    pagination: action.response && action.response.pagination,
    loading: false,
});

const setCurrentPageReducer = (state = WALLET_STATE, action: any) => ({
    ...state,
    currentPage: action.value
});

const refreshListReducer = (state = WALLET_STATE) => ({
    ...state,
    refreshList: !state.refreshList,
    currentPage: 1,
    listData: undefined,
});

const setRowPerPageReducer = (state = WALLET_STATE, action: any) => ({
    ...state,
    pageSize: action.value,
    currentPage: 1,
    listData: undefined,
});

const showLoadingReducer = (state = WALLET_STATE) => ({
    ...state,
    loading: true
});

const hideLoadingReducer = (state = WALLET_STATE) => ({
    ...state,
    loading: false
});

const setSelectedTabReducer = (state = WALLET_STATE, action: any) => ({
    ...state,
    selectedTabIndex: action.value,
    selectedTabName: walletNamesArray[action.value],
    listData: undefined,
});

const setTransactionTypeReducer = (state = WALLET_STATE, action: any) => ({
    ...state,
    transactionType: action.value,
});


const ACTION_HANDLERS = {
    [WalletTypes.TOGGLE_FILTER]: toggleFilterReducer,
    [WalletTypes.TOGGLE_MODAL]: toggleModalReducer,
    [WalletTypes.SELECTED_ELEMENT]: setSelectedElementReducer,
    [WalletTypes.SET_RESPONSE]: setResponseReducer,
    [WalletTypes.SET_CURRENT_PAGE]: setCurrentPageReducer,
    [WalletTypes.REFRESH_LIST]: refreshListReducer,
    [WalletTypes.SET_ROW_PER_PAGE]: setRowPerPageReducer,
    [WalletTypes.SHOW_LOADING]: showLoadingReducer,
    [WalletTypes.HIDE_LOADING]: hideLoadingReducer,
    [WalletTypes.SELECTED_TAB_INDEX]: setSelectedTabReducer,
    [WalletTypes.TRANSACTION_TYPE]: setTransactionTypeReducer
}

export default createReducer(WALLET_STATE, ACTION_HANDLERS);









