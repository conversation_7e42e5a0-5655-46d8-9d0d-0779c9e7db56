import { isNullValue } from '../../base/utility/StringUtils';
import AppTypes from "../types/AppTypes";
import { PaymentType } from '../storeStates/AppStoreInterface';

export const openDrawer = () => ({
    type: AppTypes.OPEN_DRAWER,
});

export const setSideNavigation = () => ({
    type: AppTypes.SIDE_NAVIGATION,
});

export const openSubDrawer = (value: boolean) => ({
    type: AppTypes.OPEN_SUB_DRAWER,
    value,
});

export const showLoader = () => ({
    type: AppTypes.SHOW_LOADER,
});

export const hideLoader = () => ({
    type: AppTypes.HIDE_LOADER,
});


//if string is passed then, success alert otherwise error case
export const showAlert = (message: string, successAlert?: any, checkStartOrStop?: any, onHideCallback?:Function) => ({
    type: AppTypes.SHOW_ALERT,
    message,
    successAlert: !isNullValue(successAlert),
    checkStartOrStop,
    onHideCallback,
});

export const hideAlert = () => ({
    type: AppTypes.HIDE_ALERT,
    onHideCallback : undefined,
});

export const enableActionButton = () => ({
    type: AppTypes.ENABLE_ACTION_BUTTON,
});

export const disableActionButton = () => ({
    type: AppTypes.DISABLE_ACTION_BUTTON,
});

export const setDeviceDataList = (value: any) => ({
    type: AppTypes.DEVICE_DATA_LIST,
    value
});

export const setMainMenuInfo = (value: any) => ({
    type: AppTypes.MAIN_MENU_INFO,
    value
})

export const setUnauthorizedUser = () => ({
    type: AppTypes.SET_UNAUTHORIZED_USER,
});

export const setRolesList = (value: any) => ({
    type: AppTypes.ROLE_LIST,
    value
});

export const setPermissionsList = (value: any) => ({
    type: AppTypes.PERMISSION_LIST,
    value
});

export const setUserInfo = (value: any) => ({
    type: AppTypes.USER_INFO,
    value
});

export const pollStart = (value: any) => ({
    type: AppTypes.POLL_START,
    value
})

export const pollStop = (value: any) => ({
    type: AppTypes.POLL_STOP,
    value
})

export const setHeaderMenu = (value: string) => ({
    type: AppTypes.HEADER_MENU,
    value
})

export const showUploadAlert = (responseMessage: string, walletCode: string, paymentRequestId: any, paymentId: any, showUploadAlert: boolean) => ({
    type: AppTypes.SHOW_UPLOAD_ALERT,
    responseMessage,
    walletCode,
    paymentRequestId,
    paymentId,
    showUploadAlert
});

export const hideUploadAlert = () => ({
    type: AppTypes.HIDE_UPLOAD_ALERT,
});

export const showUploadDocumentsModal = () => ({
    type: AppTypes.SHOW_UPLOAD_DOCUMENTS_MODAL
});

export const hideUploadDocumentsModal = () => ({
    type: AppTypes.HIDE_UPLOAD_DOCUMENTS_MODAL
});

export const showDuplicateEntryModal = (responseMessage: string) => ({
    type: AppTypes.SHOW_DUPLICATE_ENTRY_MODAL,
    duplicateEntryModalMessage: responseMessage
});

export const hideDuplicateEntryModal = () => ({
    type: AppTypes.HIDE_DUPLICATE_ENTRY_MODAL
});

export const setCurrentPaymentType = (value: PaymentType) => ({
    type: AppTypes.SET_CURRENT_PAYMENT_TYPE,
    value
});