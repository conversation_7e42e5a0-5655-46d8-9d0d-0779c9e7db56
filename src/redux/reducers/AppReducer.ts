import { createReducer } from "reduxsauce";
import { AppState } from "../storeStates/AppStoreInterface";
import AppTypes from "../types/AppTypes";

// the initial state of this reducer
export const APP_INIT_STATE: AppState = {
  drawerSate: false,
  subDrawerSate: false,
  successAlert: true,
  showLoader: false,
  showHomeSearch: false,
  showHomeFilter: false,
  isHomePage: false,
  alertMessage: undefined,
  disableActionButton: false,
  deviceDataList: undefined,
  rolesList: undefined,
  permissionList: undefined,
  userInfo: undefined,
  sideNavigation: false,
  menuSelectedIndex: 500,
  menuElement: {},
  headerMenu: "",
  showUploadAlert: false,
  paymentRequestId: undefined,
  paymentId: undefined,
  showUploadDocumentsModal: false,
  walletCode: "",
  showDuplicateEntryModal: false,
  duplicateEntryModalMessage: "",
  currentPaymentType: "TRIP_PAYMENTS",
}

// Handle Drawer Open and Close Sate
const openDrawerReducer = (state = APP_INIT_STATE) => {
  return { ...state, drawerSate: !state.drawerSate }
}

const openSubDrawerReducer = (state = APP_INIT_STATE, action: any) => {
  return { ...state, subDrawerSate: action.value }
}

const showLoaderReducer = (state = APP_INIT_STATE) => {
  return {
    ...state,
    showLoader: true,
  }
};

const hideLoaderReducer = (state = APP_INIT_STATE) => {
  return {
    ...state,
    showLoader: false,
  }
};

const showAlertReducer = (state = APP_INIT_STATE, action: any) => {
  return {
    ...state,
    alertMessage: action.message,
    showAlert: true,
    successAlert: action.successAlert,
    checkStartOrStop: action.checkStartOrStop,
    onHideCallback: action.onHideCallback
  }
};

const setSideNavigation = (state = APP_INIT_STATE, action: any) => {
  return {
    ...state,
    sideNavigation: !state.sideNavigation
  }
};

const hideAlertReducer = (state = APP_INIT_STATE) => {
  return {
    ...state,
    successAlert: true,
    alertMessage: "",
    showAlert: false,
    checkStartOrStop: false,
  }
};


const enableActionButtonReducer = (state = APP_INIT_STATE,) => {
  return {
    ...state,
    disableActionButton: false,
  }
};


const disableActionButtonReducer = (state = APP_INIT_STATE) => {
  return {
    ...state,
    disableActionButton: true,
  }
};

const setDeviceDataListReducer = (state = APP_INIT_STATE, action: any) => {
  return {
    ...state,
    deviceDataList: action.value,
  }
};

const setMainMenuInfoReducer = (state = APP_INIT_STATE, action: any) => {
  return {
    ...state,
    menuSelectedIndex: action.value.index,
    menuElement: action.value.element,
  }
};

const setRolesListReducer = (state = APP_INIT_STATE, action: any) => {
  return {
    ...state,
    rolesList: action.value,
  }
};

const setPermissionsReducer = (state = APP_INIT_STATE, action: any) => {
  return {
    ...state,
    permissionList: action.value,
  }
};

const setUserInfoReducer = (state = APP_INIT_STATE, action: any) => {
  return {
    ...state,
    userInfo: action.value,
  }
};

const pollStartReducer = (state = APP_INIT_STATE, action: any) => ({
  ...state,
  startPoll: action.value,
})

const pollStopReducer = (state = APP_INIT_STATE, action: any) => ({
  ...state,
  stopPoll: action.value
})

const setHeaderMenuReducer = (state = APP_INIT_STATE, action: any) => ({
  ...state,
  headerMenu: action.value
})

const showUploadAlert = (state = APP_INIT_STATE, action: any) => ({
  ...state,
  alertMessage: action.responseMessage,
  walletCode: action.walletCode,
  showUploadAlert: action.showUploadAlert,
  paymentRequestId: action.paymentRequestId,
  paymentId: action.paymentId
})

const hideUploadAlert = (state = APP_INIT_STATE) => ({
  ...state,
  showUploadAlert: false
})

const showUploadDocumentsModal = (state = APP_INIT_STATE) => ({
  ...state,
  showUploadDocumentsModal: true
})

const hideUploadDocumentsModal = (state = APP_INIT_STATE) => ({
  ...state,
  showUploadDocumentsModal: false
})

const showDuplicateEntryModal = (state = APP_INIT_STATE, action: any) => ({
  ...state,
  showDuplicateEntryModal: true,
  duplicateEntryModalMessage: action.duplicateEntryModalMessage
})

const hideDuplicateEntryModal = (state = APP_INIT_STATE) => ({
  ...state,
  showDuplicateEntryModal: false,
  duplicateEntryModalMessage: ""
})

const setCurrentPaymentTypeReducer = (state = APP_INIT_STATE, action: any) => ({
    ...state,
    currentPaymentType: action.value
});

const ACTION_HANDLERS = {
  [AppTypes.OPEN_DRAWER]: openDrawerReducer,
  [AppTypes.OPEN_SUB_DRAWER]: openSubDrawerReducer,
  [AppTypes.SHOW_LOADER]: showLoaderReducer,
  [AppTypes.HIDE_LOADER]: hideLoaderReducer,
  [AppTypes.SHOW_ALERT]: showAlertReducer,
  [AppTypes.HIDE_ALERT]: hideAlertReducer,
  [AppTypes.ENABLE_ACTION_BUTTON]: enableActionButtonReducer,
  [AppTypes.DISABLE_ACTION_BUTTON]: disableActionButtonReducer,
  [AppTypes.DEVICE_DATA_LIST]: setDeviceDataListReducer,
  [AppTypes.SIDE_NAVIGATION]: setSideNavigation,
  [AppTypes.MAIN_MENU_INFO]: setMainMenuInfoReducer,
  [AppTypes.ROLE_LIST]: setRolesListReducer,
  [AppTypes.PERMISSION_LIST]: setPermissionsReducer,
  [AppTypes.USER_INFO]: setUserInfoReducer,
  [AppTypes.POLL_START]: pollStartReducer,
  [AppTypes.POLL_STOP]: pollStopReducer,
  [AppTypes.HEADER_MENU]: setHeaderMenuReducer,
  [AppTypes.SHOW_UPLOAD_ALERT]: showUploadAlert,
  [AppTypes.HIDE_UPLOAD_ALERT]: hideUploadAlert,
  [AppTypes.HIDE_UPLOAD_DOCUMENTS_MODAL]: hideUploadDocumentsModal,
  [AppTypes.SHOW_UPLOAD_DOCUMENTS_MODAL]: showUploadDocumentsModal,
  [AppTypes.SHOW_DUPLICATE_ENTRY_MODAL]: showDuplicateEntryModal,
  [AppTypes.HIDE_DUPLICATE_ENTRY_MODAL]: hideDuplicateEntryModal,
  [AppTypes.SET_CURRENT_PAYMENT_TYPE]: setCurrentPaymentTypeReducer,
}

export default createReducer(APP_INIT_STATE, ACTION_HANDLERS);

