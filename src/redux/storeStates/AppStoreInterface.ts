export type PaymentType = "ADHOC_PAYMENTS" | "TRIP_PAYMENTS";

export interface AppState {
    drawerSate: boolean,
    showLoader: boolean,
    showHomeSearch: boolean,
    showHomeFilter: boolean,
    isHomePage: boolean,
    alertMessage: string | undefined,
    disableActionButton: boolean,
    subDrawerSate: boolean,
    successAlert: boolean
    deviceDataList: any
    sideNavigation: boolean
    menuSelectedIndex: Number
    menuElement: any,
    rolesList: any,
    permissionList: any,
    userInfo: any,
    headerMenu:string,
    showUploadAlert: boolean,
    paymentRequestId: any,
    paymentId: any,
    showUploadDocumentsModal: boolean,
    walletCode: string,
    showDuplicateEntryModal: boolean,
    duplicateEntryModalMessage: string,
    currentPaymentType: PaymentType
}