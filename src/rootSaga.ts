import { Task } from '@redux-saga/types';
import { all, call, cancel, delay, fork, put, take } from 'redux-saga/effects';
import { orchestrationRunningStatusEnum } from './base/constant/ArrayList';
import { isNullValue } from './base/utility/StringUtils';
import { pollStop, showAlert, showDuplicateEntryModal } from './redux/actions/AppActions';

interface ResponseObject {
    customStatus?: any,
    runtimeStatus?: any,
    output?: any,
    status?: any
}
interface ActionObject {
    type: any,
    value: any
}

function* startPolling(action: any) {
    const actionParams = Object.assign({}, action.value);
    const { params } = actionParams;
    while (true) {
        const response: ResponseObject = yield call(() => actionParams.asyncFetch(params));
        if (isNullValue(response)) {
            actionParams.stopPollingLoader();
            break;
        }
        let { customStatus } = response
        if (
            response.runtimeStatus === orchestrationRunningStatusEnum.COMPLETED || response.runtimeStatus === orchestrationRunningStatusEnum.FAILED
        ) {
            if (response.runtimeStatus === orchestrationRunningStatusEnum.COMPLETED || (customStatus && customStatus.code === 200)) {
                actionParams.stopPollingData()
                if (actionParams?.requestType === 'Approve' || actionParams?.requestType === 'ApproveHappay') {
                    yield put(showAlert("Request Approved Successfully", "true"));
                } else if (actionParams?.requestType === 'Cancel') {
                    yield put(showAlert("Request Cancelled Successfully", "true"));
                } else if (actionParams?.requestType === 'BPCLSubmitNumber') {
                    yield put(showAlert("BPCL Mobile Number Successfully Updated", "true"));
                } else if (actionParams?.requestType === 'ResolveAction') {
                    yield put(showAlert("Request Successfully Resolved", "true"));
                } else if (actionParams?.requestType === 'IOCLSubmitNumber') {
                    yield put(showAlert("IOCL Mobile Number Successfully Updated", "true"));
                } else if (actionParams?.requestType === 'CreateMapping') {
                    yield put(showAlert("Mapping Created Successfully", "true"));
                } else if (actionParams?.requestType === 'UpdateMapping') {
                    yield put(showAlert("Mapping Updated Successfully", "true"));
                } else if (actionParams?.requestType === 'JIOBPSubmitNumber') {
                    yield put(showAlert("JioBP Mobile Number Successfully Updated", "true"));
                } else if (actionParams?.requestType === 'ReconcileGoboltCashRequest') {
                    yield put(showAlert("Request Reconciled Successfully", "true"));
                    actionParams?.stopDocUploading();
                } else {
                    yield put(showAlert("Operation Successful",true));
                }
            } else {
                if (response.runtimeStatus === orchestrationRunningStatusEnum.FAILED) {
                    if(response?.status.includes(6) && response?.output.includes('CheckDuplicateRecordsActivity')){
                        yield put(showDuplicateEntryModal(response.status.split(':')?.[1]?.trim()));
                    }else{
                        yield put(showAlert(response.status));
                    }
                }
                actionParams.stopPollingLoader();
                if (actionParams?.requestType === 'ApproveHappay') {
                    if(!response?.status.includes(6)){
                        actionParams.stopPollingData();
                    }
                }
                if (actionParams?.requestType === 'ReconcileGoboltCashRequest') {
                    actionParams?.stopDocUploading();
                }
            }
            yield put(pollStop(params))
        }
        yield delay(1000);
    }
}

function* runMultiplePollingTask() {
    let tempObj: any = {}
    while (true) {
        const action: ActionObject = yield take(['POLL_START', 'POLL_STOP'])
        if (action.type === 'POLL_START') {
            let task: Task = yield fork(startPolling, action)
            console.log("x->", action)
            if (action && action.value && action.value.params && action.value.params.orchestrationId) {
                let key = action.value.params.orchestrationId;
                tempObj[key] = task;
            }
        } else {
            if (action && action.value && action.value.orchestrationId) {
                let response = action && action.value && action.value.orchestrationId;
                yield cancel(tempObj[response])
                delete tempObj[response]
            }
        }
    }
}

export default function* rootSaga() {
    yield all([runMultiplePollingTask()]);
}