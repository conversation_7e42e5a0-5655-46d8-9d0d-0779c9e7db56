import { Dispatch } from "redux";
import { adhocPayment } from "../services";
import { handleApiError } from "../base/utility/ErrorHandleUtils";

export const getPayableContactList = (queryParams: any): any => async (dispatch: Dispatch) => {
    return adhocPayment.getPayableContactList(queryParams).then((responseAxios: any) => {        
        return responseAxios.details;
    }
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const getContactType= (): any => async (dispatch: Dispatch) => {
    return adhocPayment.getContactType().then((responseAxios: any) => {                
        return responseAxios.details.integrationTypes;
    }
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const getIntegrationType= (): any => async (dispatch: Dispatch) => {
    return adhocPayment.getIntegrationType().then((responseAxios: any) => {        
        return responseAxios.details.integrationTypes;
    }
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}