import { Dispatch } from 'redux';
import { handleApiError } from '../base/utility/ErrorHandleUtils';
import { hideLoader, setRolesList, setUserInfo } from '../redux/actions/AppActions';
import { app } from '../services';


export const getUserProfileData = (): any => async (dispatch: Dispatch) => {
    return app.getUserProfile().then((responseAxios: any) => {
        responseAxios && dispatch(setUserInfo(responseAxios.details));
        responseAxios && dispatch(setRolesList(responseAxios.details.roles))
        dispatch(hideLoader());
        return responseAxios.details;
    }).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const getUserMenuList = (): any => async (dispatch: Dispatch) => {
    return app.getUserMenu().then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};
