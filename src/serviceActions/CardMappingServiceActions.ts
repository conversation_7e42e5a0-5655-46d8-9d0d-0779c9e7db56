import { Dispatch } from 'redux';
import { handleApiError } from '../base/utility/ErrorHandleUtils';
import { cardMapping } from '../services';

export const createCardMapping = (queryParams: any): any => async (dispatch: Dispatch) => {
    return cardMapping.createCardMapping(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const updateCardMapping = (queryParams: any): any => async (dispatch: Dispatch) => {
    return cardMapping.updateCardMapping(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const deleteCardMapping = (queryParams: any): any => async (dispatch: Dispatch) => {
    return cardMapping.deleteCardMapping(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const getCardMappingListing = (queryParams: any, id: any): any => async (dispatch: Dispatch) => {
    return cardMapping.getCardMappingListing(queryParams, id).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const getVehicleNumberList = (): any => async (dispatch: Dispatch) => {
    return cardMapping.getVehicleNumberList().then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const createJioBPCardMapping = (queryParams: any): any => async (dispatch: Dispatch) => {
    return cardMapping.createJioBPCardMapping(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const updateJioBPCardMapping = (queryParams: any): any => async (dispatch: Dispatch) => {
    return cardMapping.updateJioBPCardMapping(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const deleteJioBPCardMapping = (queryParams: any): any => async (dispatch: Dispatch) => {
    return cardMapping.deleteJioBPCardMapping(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};
