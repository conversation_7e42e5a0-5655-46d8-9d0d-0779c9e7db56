import { Dispatch } from 'redux';
import { handleApiError } from '../base/utility/ErrorHandleUtils';
import { dashboard } from '../services';

export const getPayments = (queryParams: any): any => async (dispatch: Dispatch) => {
    return dashboard.getPayments(queryParams).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};
