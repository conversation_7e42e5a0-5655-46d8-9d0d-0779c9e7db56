import { Dispatch } from 'redux';
import { handleApiError } from '../base/utility/ErrorHandleUtils';
import { mcpl } from '../services';

export const getWalletList = (queryParams: any, id: any): any => async (dispatch: Dispatch) => {
    return mcpl.getWalletList(queryParams, id).then((responseAxios: any) => responseAxios.details
    )
};

export const addMcplMoney = (params: any): any => async (dispatch: Dispatch) => {
    return mcpl.addMcplMoney(params).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const addWalletBalance = (): any => async (dispatch: Dispatch) => {
    return mcpl.getWalletBalance().then((responseAxios: any) => responseAxios.details
    )
};

export const getAdblueWalletBalance = (): any => async (dispatch: Dispatch) => {
    return mcpl.getAdblueWalletBalance().then((responseAxios: any) => responseAxios.details
    )
};

export const addAdblueWallet = (params: any): any => async (dispatch: Dispatch) => {
    return mcpl.addAdblueWallet(params).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const getContinentalWalletBalance = (): any => async (dispatch: Dispatch) => {
    return mcpl.getContinentalWalletBalance().then((responseAxios: any) => responseAxios.details
    )
};

export const addContinentalWallet = (params: any): any => async (dispatch: Dispatch) => {
    return mcpl.addContinentalWallet(params).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};