import { Dispatch } from 'redux';
import { handleApiError } from "../base/utility/ErrorHandleUtils";
import { opsWorkflow } from "../services";

export const createApproveRequest = (params: any): any => async (dispatch: Dispatch) => {
    return opsWorkflow.createApproveRequest(params).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const reconcileCamionsWalletRequest = (params: any): any => async (dispatch: Dispatch) => {
    return opsWorkflow.reconcileCamionsWalletRequest(params).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const orchestrationToken = (queryParams: any): any => async (dispatch: Dispatch) => {
    return opsWorkflow.orchestrationToken(queryParams).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}