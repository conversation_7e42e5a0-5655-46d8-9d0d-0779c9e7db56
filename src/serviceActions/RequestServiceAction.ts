import { Dispatch } from 'redux';
import { handleApiError } from '../base/utility/ErrorHandleUtils';
import { request } from '../services';
import { CancelTokenSource } from 'axios';

export const getRequestList = (queryParams: any, cancelToken: CancelTokenSource): any => async (dispatch: Dispatch) => {
    return request.getRequestList(queryParams, cancelToken).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const getRequestDetails = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.getRequestDetails(queryParams).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};


export const getVehicleListFromWallet = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.getVehicleListFromWallet(queryParams).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const getVehicleTrips = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.getVehicleTrips(queryParams).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const updateMobileNumber = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.getVehicleTrips(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const createRequest = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.createRequest(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        if(error.code===6){
            return error
        }
        handleApiError(error.message, dispatch)
    });
}

export const createRazorPayRequest = (payload: any): any => async (dispatch: Dispatch) => {
    return request.createRazorPayRequest(payload).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        if(error.code===6){
            return error
        }
        handleApiError(error.message, dispatch)
    });
}

export const bulkApprove = (params: any): any => async (dispatch: Dispatch) => {
    return request.bulkApprove(params).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const rejectRequest = (params: any): any => async (dispatch: Dispatch) => {
    return request.rejectRequest(params).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const cancelRequest = (params: any): any => async (dispatch: Dispatch) => {
    return request.cancelRequest(params).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const cancelRequestPaid = (params: any): any => async (dispatch: Dispatch) => {
    return request.cancelRequestPaid(params).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const cancelRequestPaidGoboltCash = (params: any): any => async (dispatch: Dispatch) => {
    return request.cancelRequestPaidGoboltCash(params).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const modifyRequest = (params: any): any => async (dispatch: Dispatch) => {
    return request.modifyRequest(params).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        if(error.code===6){
            return error
        }
        handleApiError(error.message, dispatch)
    });
}

export const getVehicleNumbersList = (): any => async (dispatch: Dispatch) => {
    return request.getVehicleNumbersList().then((responseAxios: any) => responseAxios.own_vehicles
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const getLocations = (): any => async (dispatch: Dispatch) => {
    return request.getLocations().then((responseAxios: any) => responseAxios && responseAxios.data_list
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const getTripStatus = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.getTripStatus(queryParams).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const getBpclMobileNumber = (queryParams: any): any => async (dispatch: Dispatch) => {
    return  request.getBpclMobileNumber(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const getJioBPCardNumber = (queryParams: any): any => async (dispatch: Dispatch) => {
    return  request.getJioBPCardNumber(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const getJioBPMobileNumber = (queryParams: any): any => async (dispatch: Dispatch) => {
    return  request.getJioBPMobileNumber(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const getBpclOtp = (): any => async (dispatch: Dispatch) => {
    return request.getBpclOtp().then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const updateBpclMobileNumber = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.updateBpclMobileNumber(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}
export const getIoclMobileNumber = (params: any): any => async (dispatch: Dispatch) => {
    return request.getIoclMobileNumber(params).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}
export const updateIoclMobileNumber = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.updateIoclMobileNumber(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const updateJioBPMobileNumber = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.updateJioBPMobileNumber(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const getPreviousPaymentsRequest = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.getPreviousPayments(queryParams).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const getStateList = (): any => async (dispatch: Dispatch) => {
    return request.getStateList().then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const getStateFuelRate = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.getStateFuelRate(queryParams).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    })
}
export const changeUnknownStatus = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.changeUnknownStatus(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};
export const uploadRequestDocuments = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.uploadRequestDocuments(queryParams).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const deleteRequestDocuments = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.deleteRequestDocuments(queryParams).then((responseAxios: any) =>  responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const getCurrentWalletBalance = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.getCurrentWalletBalance(queryParams).then((responseAxios: any) => responseAxios.details
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
};

export const getDriverLicense = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.getDriverLicense(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}


export const getVehicleDetails = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.getVehicleDetails(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const getPayoutContacts = (queryParams?: any): any => async (dispatch: Dispatch) => {
    return request.getPayoutContacts(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const getPayoutFundAccountsForContactId = (queryParams: any): any => async (dispatch: Dispatch) => {
    return request.getPayoutFundAccountsForContactId(queryParams).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const addPayoutFundAccountBankForContactId = (payload: unknown): any => async (dispatch: Dispatch) => {
    return request.addPayoutFundAccountBankForContactId(payload).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const addPayoutFundAccountVpaForContactId = (payload: unknown): any => async (dispatch: Dispatch) => {
    return request.addPayoutFundAccountVpaForContactId(payload).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const createPayoutContact = (payload: unknown): any => async (dispatch: Dispatch) => {
    return request.createPayoutContact(payload).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}

export const updatePayoutContact = (payload: unknown): any => async (dispatch: Dispatch) => {
    return request.updatePayoutContact(payload).then((responseAxios: any) => responseAxios
    ).catch((error: any) => {
        handleApiError(error.message, dispatch)
    });
}
