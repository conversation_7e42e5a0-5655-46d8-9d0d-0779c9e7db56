import { AxiosInstance } from "axios";
import {
  getContactTypeUrl,
  getIntegrationTypeUrl,
  getPayableContactListUrl,
} from "../base/api/ServiceUrl";

// eslint-disable-next-line import/no-anonymous-default-export
export default (api: AxiosInstance) => {
  function getPayableContactList(queryParams: any) {
    return api.get(getPayableContactListUrl, {params: queryParams});
  }

  function getContactType() {
    return api.get(getContactTypeUrl);
  }

  function getIntegrationType() {
    return api.get(getIntegrationTypeUrl);
  }

  return {
    getPayableContactList,
    getContactType,
    getIntegrationType
  };
};
