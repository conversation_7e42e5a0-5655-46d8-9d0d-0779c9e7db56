import { AxiosInstance } from "axios";
import { createCardMappingUrl, updateCardMappingUrl, getCardMappingListUrl, deleteCardMappingUrl, getVehicleNumberListUrl, getJioBPCardMappingListUrl, createJioBPCardMappingUrl, updateJioBPCardMappingUrl, deleteJioBPCardMappingUrl } from '../base/api/ServiceUrl';
import { cardMappingEnum } from "../base/constant/ArrayList";
import { isNullValue } from "../base/utility/StringUtils";

// eslint-disable-next-line
export default (api: AxiosInstance) => {
    function createCardMapping(queryParams: any) {
        return api.post(createCardMappingUrl, queryParams );
    }

    function updateCardMapping(queryParams: any) {
        return api.post(updateCardMappingUrl, queryParams );
    }

    function deleteCardMapping(queryParams: any) {
        return api.put(deleteCardMappingUrl, queryParams);
    }

    function getCardMappingListing(queryParams: any, id: any) {
        return api.get(!isNullValue(id) && id === cardMappingEnum.JIOBP_MAPPING ? getJioBPCardMappingListUrl : getCardMappingListUrl, { params: queryParams });
    }

    function getVehicleNumberList() {
        return api.get(getVehicleNumberListUrl);
    }

    function createJioBPCardMapping(queryParams: any) {
        return api.post(createJioBPCardMappingUrl, queryParams);
    }

    function updateJioBPCardMapping(queryParams: any) {
        return api.put(updateJioBPCardMappingUrl, queryParams);
    }

    function deleteJioBPCardMapping(queryParams: any) {
        return api.put(deleteJioBPCardMappingUrl, queryParams);
    }

    return {
        createCardMapping,
        updateCardMapping,
        deleteCardMapping,
        getCardMappingListing,
        getVehicleNumberList,
        createJioBPCardMapping,
        updateJioBPCardMapping,
        deleteJioBPCardMapping
    }
}