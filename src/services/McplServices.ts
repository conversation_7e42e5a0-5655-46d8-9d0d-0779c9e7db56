import { AxiosInstance } from "axios";
import { addMcplMoneyUrl, getAdblueUrl, getAdBlueWMoneyUrl, getContinentalMoneyUrl, getContinentalUrl, getMcplUrl } from '../base/api/ServiceUrl';
import { walletNamesArray } from "../base/constant/ArrayList";

// eslint-disable-next-line
export default (api: AxiosInstance) => {
    function getWalletList(queryParams: any, id: any) {
        return api.get((id === walletNamesArray[1]) ? getAdblueUrl : ((id === walletNamesArray[2]) ? getContinentalUrl : getMcplUrl), { params: queryParams });
    }

    function addMcplMoney(params: any) {
        return api.post(addMcplMoneyUrl, params);
    }

    function getWalletBalance() {
        return api.get(addMcplMoneyUrl);
    }

    function getAdblueWalletBalance() {
        return api.get(getAdBlueWMoneyUrl);
    }

    function addAdblueWallet(params: any) {
        return api.post(getAdBlueWMoneyUrl, params);
    }

    function getContinentalWalletBalance() {
        return api.get(getContinentalMoneyUrl);
    }

    function addContinentalWallet(params: any) {
        return api.post(getContinentalMoneyUrl, params);
    }
    return {
        getWalletList,
        addMcplMoney,
        getWalletBalance,
        getAdblueWalletBalance,
        addAdblueWallet,
        getContinentalWalletBalance,
        addContinentalWallet
    }
}