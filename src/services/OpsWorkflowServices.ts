import { AxiosInstance } from "axios";
import { approveWorkflowManagerUrl, orchestrationTokenUrl, reconcileCamionsWalletWorkflowManagerUrl } from "../base/api/ServiceUrl";

// eslint-disable-next-line
export default (api: AxiosInstance) => {
    function createApproveRequest(params: any) {
        return api.post(approveWorkflowManagerUrl, params);
    }

    function reconcileCamionsWalletRequest(params: any) {
        return api.post(reconcileCamionsWalletWorkflowManagerUrl, params);
    }
    
    function orchestrationToken(queryParams: any) {
        return api.get(orchestrationTokenUrl, { params: queryParams });
    }
    return {
        createApproveRequest,
        reconcileCamionsWalletRequest,
        orchestrationToken
    }
}