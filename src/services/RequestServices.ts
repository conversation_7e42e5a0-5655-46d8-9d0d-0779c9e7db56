import { AxiosInstance, CancelTokenSource } from "axios";
import { bulkApproveUrl, cancelRequestPaidUrl, cancelRequestUrl, 
        createRequestUrl, deleteRequestDocumentsUrl, getBpclMobileNumberUrl, 
        getBpclOtpUrl, getLocationsUrl, getPreviousPaymentsUrl, getTripStatusUrl, 
        getVehicleListFromWalletUrl, getVehicleNumbersUrl, getVehiclesTripsUrl,
        modifyRequestUrl, rejectRequestUrl, requestListUrl, requestPaymentDetailsUrl, 
        updateBpclMobileNumberUrl, uploadRequestDocumentsUrl, getCurrentWalletBalanceUrl, 
        getIoclLMobileNumberUrl, updateIoclMobileNumberUrl, changeUnknownStatusUrl, 
        getStateListUrl, getStateFuelRateUrl, getJioBPCardNumberUrl, 
        getJioBPMobileNumberUrl, updateJioBPMobileNumberUrl, getDriverLicenseUrl, 
        cancelRequestPaidGoboltCashUrl, getVehicleDetailsUrl, getPayoutContactsUrl, getPayoutFundAccountsForContactUrl, addFundAccountBankUrl, addFundAccountVpaUrl, createPayoutContactUrl, updatePayoutContactUrl, createRequestForRazorpayWalletUrl } from '../base/api/ServiceUrl';

// eslint-disable-next-line
export default (api: AxiosInstance) => {
    function getRequestList(queryParams: any, cancelToken: CancelTokenSource) {
        return api.get(requestListUrl, { params: queryParams, cancelToken: cancelToken.token });
    }
    function getRequestDetails(queryParams: any) {
        return api.get(requestPaymentDetailsUrl, { params: queryParams });
    }
    function getVehicleListFromWallet(queryParams: any) {
        return api.get(getVehicleListFromWalletUrl, { params: queryParams });
    }
    function getVehicleTrips(queryParams: any) {
        return api.get(getVehiclesTripsUrl, { params: queryParams });
    }
    function createRequest(params: any) {
        return api.post(createRequestUrl, params);
    }
    function createRazorPayRequest(payload: any) {
        return api.post(createRequestForRazorpayWalletUrl, payload);
    }
    function bulkApprove(params: any) {
        return api.put(bulkApproveUrl, params);
    }
    function rejectRequest(params: any) {
        return api.put(rejectRequestUrl, params);
    }
    function cancelRequest(params: any) {
        return api.put(cancelRequestUrl, params);
    }
    function cancelRequestPaid(params: any) {
        return api.post(cancelRequestPaidUrl, params);
    }
    function modifyRequest(params: any) {
        return api.put(modifyRequestUrl, params);
    }
    function getVehicleNumbersList() {
        return api.get(getVehicleNumbersUrl);
    }
    function getLocations() {
        return api.get(getLocationsUrl);
    }
    function getTripStatus(params: any) {
        return api.post(getTripStatusUrl, params)
    }
    function getBpclMobileNumber(queryParams: any) {
        return api.get(getBpclMobileNumberUrl, { params: queryParams })
    }
    function getBpclOtp() {
        return api.get(getBpclOtpUrl)
    }
    function updateBpclMobileNumber(params: any) {
        return api.post(updateBpclMobileNumberUrl, params)
    }
    function getIoclMobileNumber(params: any) {
        return api.get(getIoclLMobileNumberUrl, {params : params})
    }
    function updateIoclMobileNumber(params: any) {
        return api.post(updateIoclMobileNumberUrl, params)
    }
    function getPreviousPayments(params: any){
        return api.get(getPreviousPaymentsUrl, { params: params })
    }

    function getStateList(){
        return api.get(getStateListUrl)
    }
    function getStateFuelRate(params: any){
        return api.get(getStateFuelRateUrl, {params: params})
    }
    function changeUnknownStatus(params: any) {
        return api.post(changeUnknownStatusUrl, params)
    }
    
    function uploadRequestDocuments(params: any){
        return api.post(uploadRequestDocumentsUrl, params )
    }

    function deleteRequestDocuments(params: any){
        return api.post(deleteRequestDocumentsUrl, params )
    }

    function getCurrentWalletBalance(queryParams: any) {
        return api.get(getCurrentWalletBalanceUrl, { params: queryParams });
    }

    function getJioBPCardNumber(queryParams: any) {
        return api.get(getJioBPCardNumberUrl, { params: queryParams })
    }

    function getJioBPMobileNumber(params: any) {
        return api.get(getJioBPMobileNumberUrl, {params : params})
    }

    function updateJioBPMobileNumber(params: any) {
        return api.post(updateJioBPMobileNumberUrl, params)
    }

    function getDriverLicense(params: any){
        return api.get(getDriverLicenseUrl, { params: params })
    }

    function cancelRequestPaidGoboltCash(params: any) {
        return api.post(cancelRequestPaidGoboltCashUrl, params);
    }

    function getVehicleDetails(queryParams: any) {
        return api.get(getVehicleDetailsUrl, {params: queryParams});
    }

    function getPayoutContacts(queryParams: any) {
        return api.get(getPayoutContactsUrl, {params: queryParams});
    }

    function getPayoutFundAccountsForContactId(queryParams: any) {
        return api.get(getPayoutFundAccountsForContactUrl, {params: queryParams});
    }

    function addPayoutFundAccountBankForContactId(payload: unknown) {
        return api.post(addFundAccountBankUrl, payload);
    }

    function addPayoutFundAccountVpaForContactId(payload: unknown) {
        return api.post(addFundAccountVpaUrl, payload);
    }

    function createPayoutContact(payload: unknown) {
        return api.post(createPayoutContactUrl, payload);
    }

    function updatePayoutContact(payload: unknown) {
        return api.patch(updatePayoutContactUrl, payload);
    }

    return {
        getRequestList,
        getRequestDetails,
        getVehicleListFromWallet,
        getVehicleTrips,
        createRequest,
        bulkApprove,
        rejectRequest,
        cancelRequest,
        cancelRequestPaid,
        cancelRequestPaidGoboltCash,
        modifyRequest,
        getVehicleNumbersList,
        getLocations,
        getTripStatus,
        getBpclMobileNumber,
        getBpclOtp,
        updateBpclMobileNumber,
        getIoclMobileNumber,
        updateIoclMobileNumber,
        getPreviousPayments,
        getStateList,
        getStateFuelRate,
        changeUnknownStatus,
        uploadRequestDocuments,
        deleteRequestDocuments,
        getCurrentWalletBalance,
        getJioBPCardNumber,
        getJioBPMobileNumber,
        updateJioBPMobileNumber,
        getDriverLicense,
        getVehicleDetails,
        getPayoutContacts,
        getPayoutFundAccountsForContactId,
        addPayoutFundAccountBankForContactId,
        addPayoutFundAccountVpaForContactId,
        createPayoutContact,
        updatePayoutContact,
        createRazorPayRequest
    }
}
