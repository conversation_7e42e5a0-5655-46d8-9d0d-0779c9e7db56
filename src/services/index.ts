import Api from '../base/api/ApiMethods';
import AppServices from "./AppServices";
import McplServices from './McplServices';
import OpsWorkflowServices from './OpsWorkflowServices';
import RequestServices from "./RequestServices";
import DashboardServices from "./DashboardServices";
import CardMappingServices from "./CardMappingServices";
import AdhocPaymentServices from './AdhocPaymentServices';

const app = AppServices(Api)
const request = RequestServices(Api);
const mcpl = McplServices(Api)
const opsWorkflow = OpsWorkflowServices(Api);
const dashboard = DashboardServices(Api);
const cardMapping = CardMappingServices(Api);
const adhocPayment = AdhocPaymentServices(Api)

export {
    app,
    request,
    mcpl,
    opsWorkflow,
    dashboard,
    cardMapping,
    adhocPayment
};
