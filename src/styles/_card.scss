// card css
.card-wrapper{
    h6 {
        padding: 15px 0;
        font-weight: 500;
        margin:0;
        font-size: 16px;
        color: #083654;

    }
    .detail-tooltip-table{
        padding: 14px 0;
    }
    &.MuiCard-root{
        margin-bottom: 18px;
        box-shadow: 2px 2px 6px #0000001a;
        border: 1px solid #e9eff4;
        border-radius: 16px;
        box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.1);
        overflow: inherit;
        .MuiCardHeader-root{
            padding: 11px 20px;
            background: #eaeff3;
            border-radius: 8px 8px 0 0;
            min-height: 46px;
        }
        .MuiCardContent-root:last-child{
            padding: 15px 20px 0;
            @media screen and (max-width:767px) {
                padding: 15px 16px 5px;
            }
        }
    }
    .card-group{
        margin-bottom: 16px;
        @media screen and (max-width:767px) {
                margin: 7px 0;
            }
    }
    // card list wrap css
    .card-list-wrap{
        font-size: 14px;
        color: #768A9E;
        .card-list-row {
            padding: 15px 5px;
            border-bottom: 1px solid #EAEFF3;
            .right-col{
                font-weight: 500;
                color: #083654;
            }
        }
    }
    // card-table-list or error
    .card-table-list{
        padding: 16px 0px 0;
        .table-wrapper{
            min-height: auto;
            max-height: 273px;
            .MuiTable-root{
                border-spacing: 0;
            }
            .MuiTableRow-root{
                .MuiTableCell-root{
                    border-bottom: none;
                    border-left: none;
                    border-radius: 0;
                    padding-left: 20px;
                    padding-right: 20px;
                    border-bottom-right-radius: 8px;
                    border-bottom-left-radius: 8px;
                }
                .MuiTableCell-head{
                    background-color: #fff;
                }
            }
        }
    }
    // not found css
    .data-not-found{
        max-height: 273px;
        max-width: 220px;
        .img-fluid{
            height: 170px;
        }
        .content{
            margin: 0px;
        }
    }

}


@media (max-width:767px){
    .card-wrapper{
        .card-list-wrap{
            .card-list-row {
                .left-col{padding-right: 0;}
                .right-col{
                    white-space: inherit;
                    text-overflow: inherit;
                    word-break: break-word;
                }
            }
        }
        h6{padding: 10px 0;}
    }
}