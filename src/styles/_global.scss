// gloabl
* {
  margin: 0;
  padding: 0;
  outline: none;
}

body {
  margin: 0px;
  padding: 0px;
  font-size: 14px;
  box-sizing: border-box;
  font-family: "Roboto", sans-serif !important;
  font-weight: 400;
  background: #f9f9f9;
}
ul{
  list-style: none;
  margin: 0;
  padding: 0;
}

.checkbox-warp{
  .MuiCheckbox-colorSecondary.Mui-checked {
  color: #f7931e;} 
}

.wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  .main-page {
    padding-top: 55px;
    height: 100%;
    @media screen and (max-width: 767px) {
      padding-top: 2px;
    }
  }
  .main-content {
      padding: 0;
      width: calc(100% - 240px);
      // overflow: scroll;
      // margin-left: 64px;
  }
  .page-container-wrapper{
    padding: 20px;
  }
}

.main-section {
    height: calc(100vh - 56px);
    .page-section{
      padding: 0;
    }
    .drawer-section {
      padding: 0;
      margin: 0;
      width: 100%;
      min-height: 100%;
      max-width: 390px;
      border-radius: 0;
      border: none;
      overflow: auto;
      background-color: #ffffff;
      box-shadow: 4px 3px 7px #00000038;
    }
    .page-section,
    .drawer-section {
      height: calc(100vh - 56px);
      overflow: auto;
      position: sticky;
      top: 0;
    }
  }


.tab-nav {
  .filter-tab{
    background-color: #fff;
    box-shadow: 0px 1px 3px #0000000D;
  }
  .MuiTabs-root {
    background-color: white;
    padding-left: 24px;
    @media screen and (max-width:767px) {
      padding-left: 0;
    }

    .MuiTab-textColorInherit {
      text-transform: capitalize;
      font-size: 16px;
      color: #768a9e;
      font-weight: 400;
      border-bottom: 2px solid transparent;
      @media screen and (max-width:767px) {
        font-size: 15px;
      }
      &.Mui-selected {
        border-bottom: 2px solid #f7931e;
        color: #f7931e;
        margin: 0;
      }
    }
  }
  .MuiBox-root {
    padding: 0;
  }
}

.info-icon-color-orange {
  svg {
    color: #f7931e !important;
  }
}

.cursor-pointer{
  cursor: pointer;
}

.upload-btn {
  position: relative;
  display: inline-block;
  .upload-inner {
    display: flex;
    height: 34px;
    align-items: center;
    gap: 4px;
    border-radius: 2px;
    white-space: nowrap;
    border: solid 1px #E0E4E6;
    background-color: #FAFAFA;
    padding: 4px 10px;
    position: relative;
    .upload-icon {
      color: #282828;
      font-size: 20px;
    }
    .title {
      font-size: 13px;
      color: #282828;
    }
    .upload-input {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      cursor: pointer;
      &::before {
        content: "";
        display: inline-block;
        background: #fff;
        white-space: nowrap;
        cursor: pointer;
        width: 100%;
        height: 100%;
    }

    }
  }
}
.uploadQR{
  position: absolute;
  top: 27px;
  right: 54px;
  z-index: 1
}

.badge-item{
  display: inline-block;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 13px;
  color: #f3923d;
  background-color: rgba(243, 146, 61, 0.1254901961);
}

.badge-item-success{
  color: #1FC900;
  background-color: rgba(31, 201, 0, 0.1254901961);
}
.badge-item-failed{
  color: #ED1B00;
  background-color: rgba(237, 27, 0, 0.1254901961);
}

.btn_doc{
  padding: 0;
  height: auto;
  position: relative;
  img{
    width: 30px;
    height: 30px;
    margin:0;
  }
  span{
    position: absolute;
    top: -5px;
    right: -8px;
    color: #fff;
    background: #F07939;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    min-width: 20px;
    line-height: 1;
    padding: 0 6px;
    height: 20px;
    border-radius: 10px;
    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1607843137);
  }
}

@media(max-width:767px){
  .tab-nav .filter-mob {
      width: 87%;
  }
}