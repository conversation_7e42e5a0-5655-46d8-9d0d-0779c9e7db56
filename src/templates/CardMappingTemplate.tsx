import { Delete, Edit } from "@material-ui/icons";
import React from "react";
import Button from "../component/widgets/button/Button";
import { ColumnStateModel } from "../Interfaces/AppInterfaces";
import  "./CardMappingTemplate.css";

export const cardMappingTableColumns = (onEdit:Function, onDelete:Function, actionButtons: any) => {
    const columnList: ColumnStateModel[] = [
        { id: "vehicleNumber", label: "Vehicle Number", format: (value: any) => value || "NA" },
        { id: "cardPan", label: "Card Pan", format: (value: any) => value || "NA" },
        { id: "mobileNumber", label: "Mobile Number", format: (value: any) => value || "NA" },
        { id: "mobileExpiryDate", label: "Mobile Expiry Date", format: (value: any) => value || "NA" },
        {
            id: 'action', label: 'Action', format: (value: any) => value,
            customView: (element: any) => {
                return (
                    <div className="card-mapping-btns">
                        <Button
                            buttonStyle="btn-detail mr-3"
                            title={ "Edit"}
                            leftIcon = {<Edit/>}
                            onClick = {() => {
                                onEdit(element);
                            }}
                            disable={!actionButtons?.['edit'] }
                        />
                        <Button
                            buttonStyle="btn-grey--cancel delete-btn"
                            title={""}
                            leftIcon={<Delete/>}
                            onClick = {() => {
                                onDelete(element);
                            }}
                            disable={!actionButtons?.['delete']}
                        />
                    </div>
                )
            }
        }
    ]
    return columnList;
}

export const JioBPCardMappingTableColumns = (onEdit:Function, onDelete:Function, actionButtons: any) => {
    const columnList: ColumnStateModel[] = [
        { id: "vehicleNumber", label: "Vehicle Number", format: (value: any) => value || "NA" },
        { id: "cardNo", label: "Card Number", format: (value: any) => value || "NA" },
        {
            id: 'action', label: 'Action', format: (value: any) => value,
            customView: (element: any) => {
                return (
                    <div className="card-mapping-btns">
                        <Button
                            buttonStyle="btn-detail mr-3"
                            title={ "Edit"}
                            leftIcon = {<Edit/>}
                            onClick = {() => {
                                onEdit(element);
                            }}
                            disable={!actionButtons?.['edit'] }
                        />
                        <Button
                            buttonStyle="btn-grey--cancel delete-btn"
                            title={""}
                            leftIcon={<Delete/>}
                            onClick = {() => {
                                onDelete(element);
                            }}
                            disable={!actionButtons?.['delete']}
                        />
                    </div>
                )
            }
        }
    ]
    return columnList;
}
