import React from "react";
import { ColumnStateModel } from "../Interfaces/AppInterfaces";
import Button from "../component/widgets/button/Button";
import { KeyboardArrowRightOutlined } from "@material-ui/icons";
import { contactListingStatusArray } from "../base/constant/ArrayList";
import { convertDateFormat, displayDateTimeFormatter } from "../base/utility/DateUtils";

export const contactsListingTableColumns = (status:string) => {
    let columnList: ColumnStateModel[] = [
        { id: "name", label: "Name", format: (value: any) => value || "NA" },
        { id: "contactType", label: "Type", format: (value: any) => value || "NA" ,
            customView: (element: any) => {
                return (
                    <div className="badge-item">
                        {element.contactType}
                    </div>
                )
            }
        },
        { id: "phone", label: "Phone", format: (value: any) => value || "NA" },
        { id: "panNumber", label: "PAN Number", format: (value: any) => value || "NA" },
        { id: "integrationType", label: "Integration Type", format: (value: any) => value || "NA" },
        { id: "email", label: "Email", format: (value: any) => value || "NA" },
        { id: "createdBy", label: "Raised By",isVisible: (status=== contactListingStatusArray[0]), format: (value: any) => value || "NA" },
        { id: "createdAt", label: "Raised At",isVisible: (status=== contactListingStatusArray[0]), format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA"|| "NA" },
        { id: "approvedBy", label: "Approved By",isVisible: (status=== contactListingStatusArray[1]), format: (value: any) => value || "NA" },
        { id: "approvedAt", label: "Approved At",isVisible: (status=== contactListingStatusArray[1]), format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA" || "NA" },
        { id: "rejectedBy", label: "Rejected By",isVisible: (status=== contactListingStatusArray[2]), format: (value: any) => value || "NA" },
        { id: "rejectedAt", label: "Rejected At",isVisible: (status=== contactListingStatusArray[2]), format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA" || "NA" },
        { 
            id: "action", 
            label: "Action", 
            format: (value: any) => value || "NA" ,
            sticky: true,
            isVisible: (status=== contactListingStatusArray[1]),
            customView: (element: any) => {
                return (
                    <>
                        <Button
                            buttonStyle="btn-outline-danger"
                            title={'Inactive'}
                            onClick={() => {}}
                        />
                        {/* <Button
                            buttonStyle="btn-outline-gray"
                            title={'Active'}
                            onClick={() => {}}
                        /> */}
                        <KeyboardArrowRightOutlined className="ml-1 orange-text" fontSize="small" />
                    </>
                )
            }
        },
    ]
    return columnList;
}
        