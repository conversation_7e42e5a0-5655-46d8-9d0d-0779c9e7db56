import React from "react";
import { ColumnStateModel } from "../Interfaces/AppInterfaces";
import Button from "../component/widgets/button/Button";
import { Visibility } from "@material-ui/icons";

export const logsListingTableColumns = (onClickDetailsButton: Function) => {
    let columnList: ColumnStateModel[] = [
        { id: "requestId", label: "Request ID", format: (value: any) => value || "NA" },
        { id: "jobType", label: "Job Type", format: (value: any) => value || "NA" },
        { id: "status", label: "Status", format: (value: any) => value || "NA" ,
            customView: (element: any) => {
                return (
                    <>
                        {
                            element.status === 'Completed' ?
                            <div className="badge-item badge-item-success">Completed</div> :
                            <div className="badge-item badge-item-failed">Failed</div>
                        }
                    </>
                )
            }
        },
        { id: "jobCreatedAt", label: "Job Created Date & Time", format: (value: any) => value || "NA" },
        { 
            id: "action", 
            label: "Action", 
            format: (value: any) => value || "NA" ,
            customView: (element: any) => {
                return (
                    <>
                        <Button
                            buttonStyle="btn-detail"
                            title={'View'}
                            leftIcon={<Visibility />}
                            onClick={() => {
                                onClickDetailsButton(element)
                            }}
                        />
                    </>
                )
            }
        },
    ]
    return columnList;
}
        