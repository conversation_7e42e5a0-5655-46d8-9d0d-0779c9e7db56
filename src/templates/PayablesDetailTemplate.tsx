
import { ColumnStateModel } from '../Interfaces/AppInterfaces';

export const payablesDetailTableColumns = () => {
    const columnList: ColumnStateModel[] = [
        { id: "billAmount", label: "Bill Amount", format: (value: any) => value || "NA" },
        { id: "GST", label: "GST", format: (value: any) => value || "NA" },
        { id: "baseAmount", label: "Base Amount", format: (value: any) => value || "NA" },
        { id: "TDSRate", label: "TDS Rate (%)", format: (value: any) => value || "NA" },
    ]
    return columnList;
}


