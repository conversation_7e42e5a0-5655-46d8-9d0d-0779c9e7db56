import React from 'react';
import { Visibility } from '@material-ui/icons';
import { convertDateFormat, displayDateTimeFormatter } from '../base/utility/DateUtils';
import Button from '../component/widgets/button/Button';
import { ColumnStateModel } from '../Interfaces/AppInterfaces';
import { Checkbox } from '@material-ui/core';
import { paybalesListingStatusArray } from '../base/constant/ArrayList';

export const payablesListingTableColumns = (status:string, onClickDetailsButton: Function,
    onItemCheck?: (element: any, checked: boolean) => void,
    onAllItemsCheck?: (checked: boolean) => void,
    state?: any,) => {
    const columnList: ColumnStateModel[] = [
        {
            id: '#', label: '#', isVisible: (status === paybalesListingStatusArray[0]), format: (value: any) => value || "NA",
            customHead: (element: any) =>
                <div className="checkbox-warp">
                    <Checkbox
                     onChange={(event) => {
                                onAllItemsCheck && onAllItemsCheck(event.target.checked);                                
                            }}
                    checked={state.selectedRequestsToBeApproved.isAllSelected}
                    />
                    <span>All</span>
                </div>,
            customView: (element: any) =>
                {return <div className="checkbox-warp">
                        <Checkbox
                            onChange={(event) => {
                                onItemCheck && onItemCheck(element, event.target.checked);

                            } }
                            checked={Boolean(element.isChecked)}
 />
                    </div>;
                }
        },
        { id: "paymentId", label: "Payment ID", format: (value: any) => value || "NA" },
        { id: "narration", label: "Narration", format: (value: any) => value || "NA" },
        { id: "paymentCategory", label: "Payment Category", format: (value: any) => value || "NA" },
        { id: "requestAmount", label: "Request Amount", format: (value: any) => value || "NA" },
        { id: "contactName", label: "Contact Name", format: (value: any) => value || "NA" },
        { id: "contactNumber", label: "Contact Number", format: (value: any) => value || "NA" },
        { id: "integrationType", label: "Integration Type", format: (value: any) => value || "NA" },
        { id: "createdBy", label: "Raised By",isVisible:(status=== paybalesListingStatusArray[0] ||  status=== paybalesListingStatusArray[3]), format: (value: any) => value || "NA" },
        { id: "createdAt", label: "Raised At",isVisible:(status=== paybalesListingStatusArray[0] ||  status=== paybalesListingStatusArray[3]), format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA" },
        { id: "approvedByL1", label: "Approved By (Level 1)", isVisible:(status=== paybalesListingStatusArray[1] ||  status=== paybalesListingStatusArray[2]), format: (value: any) => value || "NA" },
        { id: "approvedAtL1", label: "Approved At",isVisible:(status=== paybalesListingStatusArray[1] ||  status=== paybalesListingStatusArray[2]), format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA" },
        { id: "approvedByL2", label: "Approved By (Level 2)", isVisible:(status=== paybalesListingStatusArray[1] ||  status=== paybalesListingStatusArray[2]), format: (value: any) => value || "NA" },
        { id: "approvedAtL2", label: "Approved At", isVisible:(status=== paybalesListingStatusArray[1] ||  status=== paybalesListingStatusArray[2]), format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA" },
        { id: "requestedBy", label: "Requested By",isVisible:(status=== paybalesListingStatusArray[4] ), format: (value: any) => value || "NA" },
        { id: "requestedAt", label: "Requested At",isVisible:(status=== paybalesListingStatusArray[4] ), format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA" },
        { id: "rejectedBy", label: "Rejected By",isVisible:(status=== paybalesListingStatusArray[5] ), format: (value: any) => value || "NA" },
        { id: "rejectedAt", label: "Rejected At",isVisible:(status=== paybalesListingStatusArray[5]), format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA" },
        { id: "cancelledBy", label: "Cancelled By",isVisible:(status=== paybalesListingStatusArray[6]), format: (value: any) => value || "NA" },
        { id: "cancelledAt", label: "Cancelled At",isVisible:(status=== paybalesListingStatusArray[6]), format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA" },
        {
            id: 'action', label: 'Action', format: (value: any) => value || ".....",
                customView: (element: any) => {
                    return (
                        <Button
                            buttonStyle="btn-detail"
                            title="Details"
                            leftIcon={<Visibility />}
                            onClick={() => {
                                onClickDetailsButton(element)
                            }}
                        />
                    )
            }
        }
    ]
    return columnList;
}


