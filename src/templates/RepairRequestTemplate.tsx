import React from "react";
import { ColumnStateModel } from "../Interfaces/AppInterfaces";
import { isEmptyArray } from "../base/utility/StringUtils";
import { TooltipList } from "../component/widgets/tooltipList/TooltipList";
import { convertDateFormat, displayDateTimeFormatter } from "../base/utility/DateUtils";
import Button from "../component/widgets/button/Button";

export default function repairRequestTableColumns(setDocumentLinks: Function, setOpenViewDocumentsModal: Function, setVehicleDetailsModalData: Function){
    const columnList: ColumnStateModel[] = [
        { id: "repairId", label: "Repair Id", format: (value: any) => value || "NA" },
        { id: "billNumber", label: "Bill Number", format: (value: any) => value || "NA" },
        { id: "maintenanceDocuments", label: "Document", format: (value: any) => value || "NA", 
            customView: (element: any) => {
                return (
                    <>
                        {
                            element?.maintenanceDocuments &&  (
                                <Button
                                  buttonStyle='upload-doc-btn' 
                                  onClick={()=>{
                                    setDocumentLinks(element?.maintenanceDocuments);
                                    setOpenViewDocumentsModal(true);
                                  }}
                                    leftIcon={
                                        <img src="/images/InsertPhotoOutlined.svg" alt="InsertPhotoOutlined" />
                                    }
                                    rightIcon={
                                        <span className='value_show'>{element.maintenanceDocuments.length}</span>
                                    }
                                />
                              )
                        }
                    </>
                )
            }
        },
        { id: "repairReason", label: "Repair Reason", format: (value: any) => value || "NA",
            customView: (element: any) => {
                return (
                    <>
                        {
                            (Array.isArray(element?.repairReasons) && !isEmptyArray(element?.repairReasons)) ?
                                <TooltipList
                                    label={element?.repairReasons?.[0]?.repairReasonName}
                                    showTooltipData={element.repairReasons.length > 1}
                                    tooltipData={getReactiveTooltipData(element.repairReasons)}
                                    infoText={`+${element.repairReasons.length - 1}`}
                                /> : 'NA'
                        }
                    </>
                )
            }
        },
        { id: "vehicleNumber", label: "Vehicle Number", format: (value: any) => value || "NA", 
            customView: (element: any) => (
                <span
                    className="blue-text cursor-pointer"
                    onClick={()=>{
                        setVehicleDetailsModalData({open: true, vehicleNumber: element.vehicleNumber})
                    }} 
                >
                {element.vehicleNumber}
                </span>
            )
        },
        { id: "amount", label: "Amount", format: (value: any) => (value && `₹ ${value?.toFixed(2)}`) || "₹ 0.00" },
        { id: "createdAt", label: "Created At", format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA" },
        { id: "remark", label: "Remarks", format: (value: any) => value || "NA" },
    ]
    return columnList;
}

const getReactiveTooltipData = (reasonList: Array<any>) => {
    let dataList = [];
    if (reasonList.length > 1) {
        dataList = reasonList.slice(1);
    }
    dataList = dataList.map(el => el.repairReasonName)
    return dataList; 
}