import React from 'react';
import { ColumnStateModel } from "../Interfaces/AppInterfaces";
import { CustomTooltipTable } from '../component/widgets/tooltip/CustomTooltipTable/CustomToolTipTable';

export default function getPastEntryColumns(){
    const columnList: ColumnStateModel[] = [
        { id: "orderCode", label: "Order Code", format: (value: any) => value || "NA" },
        { id: "originName", label: "Origin", format: (value: any) => value || "NA" },
        { id: "destinationName", label: "Destination", format: (value: any) => value || "NA" },
        { id: "bpclAmount", label: "BPCL Wallet", format: (value: any) => (value && `₹ ${value?.toFixed(2)}`) || "₹ 0.00" },
        { id: "jiobpAmount", label: "JioBP Wallet", format: (value: any) => (value && `₹ ${value?.toFixed(2)}`) || "₹ 0.00" },
        { id: "camionsRazorPayAmount", label: "Camions Razor Pay", format: (value: any) => (value && `₹ ${value?.toFixed(2)}`) || "₹ 0.00" },
        { id: "continentalPetroleumsAmount", label: "Continental Petroleums", format: (value: any) => (value && `₹ ${value?.toFixed(2)}`) || "₹ 0.00" },
        { id: "goboltFuelPumpAmount", label: "Gobolt Fuel Pump", format: (value: any) => (value && `₹ ${value?.toFixed(2)}`) || "₹ 0.00", 
            customView: (element: any) => {
                return (
                    <>
                        <div className='d-flex align-items-center info-icon-color-orange'>
                            {(element?.goboltFuelPumpAmount && `₹ ${element.goboltFuelPumpAmount.toFixed(2)}`) || "₹ 0.00"}
                            {Array.isArray(element?.fuelPumpIButtonData) && element?.fuelPumpIButtonData.length > 0 && (
                                <CustomTooltipTable
                                    tableColumn={getFuelPumpNameAmountColumns()}
                                    tableData={element?.fuelPumpIButtonData || []}
                                    showStringValue={true}
                                    placement={"top"}
                                />
                            )}
                        </div>
                    </>
                );
            }
        },
        { id: "goboltCashAmount", label: "Camions Wallet", format: (value: any) => (value && `₹ ${value?.toFixed(2)}`) || "₹ 0.00" },
        { id: "mcplAmount", label: "GoBolt Wallet(MCPL)", format: (value: any) => (value && `₹ ${value?.toFixed(2)}`) || "₹ 0.00" },
        { id: "happayAmount", label: "Happay Wallet", format: (value: any) => (value && `₹ ${value?.toFixed(2)}`) || "₹ 0.00" },
        { id: "ioclAmount", label: "IOCL Wallet", format: (value: any) => (value && `₹ ${value?.toFixed(2)}`) || "₹ 0.00" },
        { id: "hubAdblueAmount", label: "Hub AdBlue", format: (value: any) => (value && `₹ ${value?.toFixed(2)}`) || "₹ 0.00" },
        { id: "omPetroMartCashHappayAmount", label: "Om Petro Mart (Cash/Happay)", format: (value: any) => (value && `₹ ${value?.toFixed(2)}`) || "₹ 0.00" },
        { id: "omPetroMartDieselBpclAmount", label: "Om Petro Mart (Diesel/BPCL)", format: (value: any) => (value && `₹ ${value?.toFixed(2)}`) || "₹ 0.00" },
        { id: "opsWalletAmount", label: "OPS Wallet", format: (value: any) => (value && `₹ ${value?.toFixed(2)}`) || "₹ 0.00" },
        { id: "totalAmount", label: "Total Amount", format: (value: any) => (value && `₹ ${value?.toFixed(2)}`) || "₹ 0.00" }
    ]
    return columnList;
}

export const getFuelPumpNameAmountColumns = () => {
    return [
        { name: "fuelPumpName", description: "Fuel Pump", format: (value: any) => value || "NA" },
        { 
            name: "amount", 
            description: "Amount", 
            customView: (element: any) => {
                return (
                    <td>
                        <div className='tooltip-data'>
                            <span>{element?.amount ? `₹ ${element.amount.toFixed(2)}` : "₹ 0.00"}</span>
                        </div>
                    </td>
                );
            } 
        }
    ];
};
