import { Checkbox, IconButton } from "@material-ui/core";
import { Visibility } from "@material-ui/icons";
import React, { Dispatch } from 'react';
import { convertDateFormat, displayDateTimeFormatter } from "../base/utility/DateUtils";
import Button from "../component/widgets/button/Button";
import { ColumnStateModel } from '../Interfaces/AppInterfaces';
import { isEmptyArray } from "../base/utility/StringUtils";
import { TooltipList } from "../component/widgets/tooltipList/TooltipList";
import { handleViewDocumentModal } from "../pages/requestModule/requestModuleRedux/RequestActions";
import { isMobile } from "../base/utility/ViewUtils";
import { isReactiveRepairMaintenenceRequest, requestStatusToShowFuelPump } from "../pages/requestModule/requestModuleUtility/RequestUtility";
import { InfoTooltip } from "../component/widgets/tooltip/InfoTooltip";
import { OverflowTip } from "../component/widgets/tooltip/OverFlowToolTip";
import { walletListEnum, walletStatusListEnum } from "../base/constant/ArrayList";

function getTooltipData(reasonList: Array<any>) {
    let dataList = [];
    if (reasonList.length > 1) {
        dataList = reasonList.slice(1);
    }
    return dataList;
}


export const requestListingTableColumns = (onClickDetailsButton: Function, selectedTabIndex: Number) => {
    let columnList: ColumnStateModel[] = [
        { id: "paymentId", label: "Payment Id", format: (value: any) => value || "NA" },
        { id: "orderCode", label: "Order Code", format: (value: any) => value || "NA" },
        { id: "", label: "Vehicle Number", format: (value: any) => value || "NA",
            customView: (element: any) => {
                let vehiclesList; 
                if(isReactiveRepairMaintenenceRequest(element?.maintenanceType)){
                    vehiclesList = element?.vehicleNoList?.map((vehicleNo: string) => vehicleNo) ?? [];
                }else{
                    vehiclesList = [element.vehicleNumber];
                }
                return (
                    <>
                        {(Array.isArray(vehiclesList) && !isEmptyArray(vehiclesList)) ?
                            <TooltipList
                                label={vehiclesList[0]}
                                showTooltipData={vehiclesList.length > 1}
                                tooltipData={getTooltipData(vehiclesList)}
                                infoText={`+${vehiclesList.length - 1}`}
                            /> : <span className="title d-block text-truncate ">NA</span>
                        }
                    </>
                )
            }
        },
        {
            id: "walletName", label: "Wallet", format: (value: any) => value || "NA",
            customView: (element: any) => {
                const hasRequiredStaus = requestStatusToShowFuelPump(element.paymentStatus);
                const isGoBoltFuelPump = element.walletCode === walletListEnum.GOBOLT_FUEL_PUMP;

                return (
                    <>
                        {(hasRequiredStaus && isGoBoltFuelPump) ? (
                            <div className='d-flex align-items-center info-icon-color-orange'>
                                <OverflowTip text={element.walletName || 'NA'} />
                                <InfoTooltip
                                    title={element.fuelPumpName || 'NA'}
                                    placement={'top'}
                                />
                            </div>
                        ) : (
                            <span>{element.walletName || 'NA'}</span>
                        )}
                    </>
                );
            }
        },
        { id: "totalRequestAmount", label: "Request Amount", format: (value: any) => `₹ ${value?.toFixed(2)}` || "₹ 0.00" },
        { id: "createdBy", label: "Raised By", format: (value: any) => value || "NA" },
        { id: "createdAt", label: "Raised At", format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA" },
        {
            id: 'action', label: 'Action', format: (value: any) => value || ".....",
            customView: (element: any) => {
                return (
                    <Button
                        buttonStyle="btn-grey-bg "
                        title="Details"
                        leftIcon={<Visibility />}
                        onClick={() => {
                            onClickDetailsButton(element)
                        }}
                    />
                )
            }
        }
    ]
    if (selectedTabIndex === 1 || selectedTabIndex === 2) {
        columnList[5] = { id: "approvedBy", label: "Approved By", format: (value: any) => value || "NA" }
        columnList[6] = { id: "approvedAt", label: "Approved Date and Time", format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA" }
    }
    if (selectedTabIndex === 4) {
        columnList[5] = { id: "rejectedBy", label: "Rejected By", format: (value: any) => value || "NA" }
        columnList[6] = { id: "rejectedAt", label: "Rejected Date and Time", format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA" }
    }
    if (selectedTabIndex === 3 || selectedTabIndex === 6 || selectedTabIndex === 7 || selectedTabIndex === 8) {
        columnList[5] = { id: "createdBy", label: "Requested By", format: (value: any) => value || "NA" }
        columnList[6] = { id: "createdAt", label: "Requested Date and Time", format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA" }
    }
    if (selectedTabIndex === 5) {
        columnList[5] = { id: "updatedBy", label: "Cancelled By", format: (value: any) => value || "NA" }
        columnList[6] = { id: "updatedAt", label: "Cancelled Date and Time", format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA" }
    }

    columnList = isMobile
                ? [
                    ...columnList,
                    { id: 'seeMore', label: 'See More', type: 'expand' }
                ]
                : columnList;

    return columnList;
}

// children
export const requestChildrenTableColumns = (dispatch: Dispatch<any>) => {
    let columnList: ColumnStateModel[] = [
        { id: 'repairId', label: 'Repair Id', format: (value: any) => value || "NA" },
        { id: 'billNumber', label: 'Bill Number', format: (value: any) => value || "NA" },
        {
            id: 'document', label: 'Document', format: (value: any) => value || "NA",
            customView: (element: any) => {
                return (
                    <Button
                        buttonStyle='upload-doc-btn' 
                        onClick={() => {
                            dispatch(handleViewDocumentModal(true, element?.maintenanceDocuments?.map((docItem: any) => ({ documentLink: docItem.documentLink })) || []));
                        }}
                        leftIcon={
                            <img src="/images/InsertPhotoOutlined.svg" alt="InsertPhotoOutlined" />
                        }
                        rightIcon={
                            <span className='value_show'>
                                {
                                    element?.maintenanceDocuments?.length || 0
                                }
                            </span>
                        }
                    />
                )
            }
        },
        {
            id: "repairReason", label: "Repair Reason", format: (value: any) => value || "NA",
            customView: (element: any) => {
                const repairReasonsList = element?.repairReasons?.map((reason: any) => reason.repairReasonName) || [];
                return (
                    <>
                        {(Array.isArray(repairReasonsList) && !isEmptyArray(repairReasonsList)) ?
                            <TooltipList
                                label={repairReasonsList[0]}
                                showTooltipData={repairReasonsList.length > 1}
                                tooltipData={getTooltipData(repairReasonsList)}
                                infoText={`+${repairReasonsList.length - 1}`}
                            /> : 'NA'
                        }
                    </>
                )
            }
        },
        { id: "vehicleNumber", label: "Vehicle No.", format: (value: any) => value || "NA" },
        { id: 'amount', label: 'Amount', format: (value: any) => `₹ ${value?.toFixed(2)}` || "₹ 0.00" },
        { id: 'remark', label: 'Remarks', format: (value: any) => value || "NA" },
    ];

    return columnList;
}

export const requestBulkApproveTableColumns = (handleChecks: Function, handleAllChecks: Function, allValue: any) => {
    const columnList: ColumnStateModel[] = [
        {
            id: '#', label: 'All', format: (value: any) => value || "NA",
            customHead: () =>
                <div className="checkbox-warp">
                    <Checkbox
                        onChange={(e) => {
                            handleAllChecks && handleAllChecks(e.target.checked)
                        }}
                        checked={allValue}
                    />
                </div>,
            customView: (element: any) =>
                <div className="checkbox-warp">
                    <Checkbox
                        onChange={(e) => {
                            handleChecks && handleChecks(element.paymentId, e.target.checked);
                        }}
                        checked={element.isCheckboxChecked ? true : false}
                        name="checked"
                    />
                </div>
        },
        { id: "paymentId", label: "Payment ID", format: (value: any) => value || "NA" },
        { id: "orderCode", label: "Order Code", format: (value: any) => value || "NA" },
        { id: "vehicleNumber", label: "Vehicle Number", format: (value: any) => value || "NA" },
        { id: "walletName", label: "Wallet", format: (value: any) => value || "NA" },
        { id: "totalRequestAmount", label: "Request Amount", format: (value: any) => `₹ ${value?.toFixed(2)}` || "₹ 0.00" },
        { id: "createdBy", label: "Raised By", format: (value: any) => value || "NA" },
        { id: "createdAt", label: "Raised At", format: (value: any) => convertDateFormat(value, displayDateTimeFormatter) || "NA" },
    ]
    return columnList;
}

export const getGoboltTooltipTableColumns = () =>{
    return [
        {name:'opsWalletBeneficiaryName', description:'Beneficiary Name'}, 
        {name:'opsWalletBeneficiaryAccountNo', description:'Beneficiary Account Number'}
    ]
}

export const getGoboltTooltipData = (response: any) => {
    if(!(response?.opsWalletBeneficiaryAccountNo && response?.opsWalletBeneficiaryName)){
        return [];
    }
    return [
        {
            opsWalletBeneficiaryAccountNo: response?.opsWalletBeneficiaryAccountNo,
            opsWalletBeneficiaryName: response?.opsWalletBeneficiaryName
        },
    ]
}