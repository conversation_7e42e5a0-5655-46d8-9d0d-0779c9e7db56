import React from "react";
import Information from "../component/information/Information";
import { OverflowTip } from "../component/widgets/tooltip/OverFlowToolTip";
import { isNullValue } from "../base/utility/StringUtils";

// Mapper for vehicle details
export const getVehicleDetailsMapper = (customViewClasses?: any) => {
  return [
    { title: 'Vehicle Registration Number', key: 'vehicle_number' },
    { title: 'Model Number', key: 'model_number' },
    { title: 'Make', key: 'vehicle_make' },
    {
      title: '',
      key: 'registration_date',
      customView: (element: any) => (
        <Information
          title={'Registration Month & Year'}
          text={element?.registration_month && element?.registration_year
            ? `${element.registration_month}, ${element.registration_year}`
            : 'NA'}
          className={`${customViewClasses?.registration_date ? customViewClasses.registration_date : 'col-6 col-md-3 mb-3'}`}
        />
      )
    },
    { title: 'Model Type', key: 'vehicle_model_type' },
    { title: 'Standard Fuel Mileage (Loaded km/ltr)', key: 'standard_fuel_mileage_loaded' },
    { title: 'Standard Fuel Mileage (Empty km/ltr)', key: 'standard_fuel_mileage_empty' },
    { title: 'Standard DEF Percentage', key: 'standard_def_percentage' },
    { title: 'Tank1 Capacity (Litres)', key: 'tank1_capacity' },
    { title: 'Tank2 Capacity (Litres)', key: 'tank2_capacity' },
    { title: 'Body Type', key: 'body_type' },
    { title: 'GVW (kgs)', key: 'gvw' },
    { title: '', key: 'unladen_weight', customView: (element: any) => (
        <Information
          title='Unladen Weight (kgs)'
          customView={
            <OverflowTip 
              text={`${isNullValue(element.unladen_weight) ? 'NA' : element.unladen_weight}`}
            />
          }
          className={`${customViewClasses?.unladen_weight ? customViewClasses.unladen_weight : 'col-6 col-md-3 mb-3'}`}
        />
      ) 
    }
  ];
}
