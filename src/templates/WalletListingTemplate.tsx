import { convertDateFormat, displayDateTimeFormatter } from '../base/utility/DateUtils';
import { ColumnStateModel } from '../Interfaces/AppInterfaces';

export const walletListingTableColumns = () => {
    const columnList: ColumnStateModel[] = [
        { id: "rechargeId", label: "Recharge ID", format: (value: any) => value || "NA" },
        { id: "fuelQuantity", label: "Fuel Quantity(Ltr)", format: (value: any) => (value && value.toFixed(2)) || "NA" },
        { id: "fuelPrice", label: "Fuel Price (₹/Per Ltr)", format: (value: any) => (value && value.toFixed(2)) || "NA" },
        { id: "invoiceNo", label: "Invoice No.", format: (value: any) => value || "NA" },
        { id: "transactionType", label: "Transaction Type", format: (value: any) => value || "NA" },
        { id: "referenceId", label: "Reference ID", format: (value: any) => value || "NA" },
        { id: "rechargedBy", label: "Transaction By", format: (value: any) => value || "NA" },
        { id: "rechargedAt", label: "Transaction At", format: (value: any) => (value && convertDateFormat(value, displayDateTimeFormatter)) || "NA" },
        { id: "totalAmount", label: "Amount", format: (value: any) => (value && ("₹ " + value.toFixed(2))) || "NA" },
    ]
    return columnList;
}

export const adBlueListingTableColumns = () => {
    const columnList: ColumnStateModel[] = [
        { id: "rechargeId", label: "Recharge ID", format: (value: any) => value || "NA" },
        { id: "adblueQuantity", label: "AdBlue Quantity(Ltr)", format: (value: any) => (value && value.toFixed(2)) || "NA" },
        { id: "adbluePrice", label: "AdBlue Price (₹/Per Ltr)", format: (value: any) => (value && value.toFixed(2)) || "NA" },
        { id: "invoiceNo", label: "Invoice No.", format: (value: any) => value || "NA" },
        { id: "transactionType", label: "Transaction Type", format: (value: any) => value || "NA" },
        { id: "referenceId", label: "Reference ID", format: (value: any) => value || "NA" },
        { id: "rechargedBy", label: "Transaction By", format: (value: any) => value || "NA" },
        { id: "rechargedAt", label: "Transaction At", format: (value: any) => (value && convertDateFormat(value, displayDateTimeFormatter)) || "NA" },
        { id: "totalAmount", label: "Amount", format: (value: any) => (value && ("₹ " + value.toFixed(2))) || "NA" },
    ]
    return columnList;
}
